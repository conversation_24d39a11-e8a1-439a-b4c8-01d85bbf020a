/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "breadcrumbs": {
      "type": "breadcrumbs",
      "disabled": true,
      "settings": {
        "hide_mobile": false
      }
    },
    "section-slideshow": {
      "type": "section-slideshow",
      "blocks": {
        "slide_1": {
          "type": "slide",
          "settings": {
            "layout": "background",
            "overlay_opacity": 0,
            "color_palette": "scheme-3",
            "text_position": "text-start",
            "title": "<h1>How can we help<br/>you today?</h1>",
            "title_underline_style": "none",
            "text": "<p>You can email and call us. We are there for you 24/7, every day.</p>",
            "show_first_link": true,
            "first_link_text": "Send an E-mail",
            "first_link_url": "/",
            "first_button_style": "secondary plain",
            "show_second_link": false,
            "second_link_text": "Button",
            "second_link_url": "",
            "second_button_style": "secondary plain"
          }
        }
      },
      "block_order": [
        "slide_1"
      ],
      "settings": {
        "height": "32/9",
        "width": "wide",
        "autoplay": false,
        "autoplay_seconds": 3,
        "enable_controls": false,
        "height_mobile": "m",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    },
    "page-service-menu": {
      "type": "page-service-menu",
      "disabled": true,
      "settings": {}
    },
    "1655236642bd2b4796": {
      "type": "section-icon-text-blocks",
      "blocks": {
        "1655236642d3f67aec-0": {
          "type": "text",
          "settings": {
            "icon": "speech_bubbles",
            "image_height": 55,
            "title": "Chat live with us",
            "text": "<p>Maeve is happy to help you</p>",
            "show_link": false,
            "link_text": "",
            "link_url": "",
            "button_style": "primary plain"
          }
        },
        "1655236642d3f67aec-1": {
          "type": "text",
          "settings": {
            "icon": "help_call",
            "image_height": 55,
            "title": "Give us a call",
            "text": "<p>Monday - Friday: 10am - 9pm <br/>@ 1234567890</p>",
            "show_link": false,
            "link_text": "",
            "link_url": "",
            "button_style": "primary plain"
          }
        },
        "1655236642d3f67aec-2": {
          "type": "text",
          "settings": {
            "icon": "email",
            "image_height": 55,
            "title": "E-mail us anytime",
            "text": "<p>We try to respond within 24 hours</p>",
            "show_link": false,
            "link_text": "",
            "link_url": "",
            "button_style": "primary plain"
          }
        }
      },
      "block_order": [
        "1655236642d3f67aec-0",
        "1655236642d3f67aec-1",
        "1655236642d3f67aec-2"
      ],
      "settings": {
        "items_width": 3,
        "text_alignment": "start",
        "title": "",
        "title_underline_style": "none",
        "text": "",
        "font_size": "16px",
        "show_link": false,
        "link_text": "",
        "link_url": "/",
        "button_style": "primary plain",
        "blocks_text_alignment": "center",
        "blocks_title_font": "primary",
        "blocks_title_size": "h3",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    },
    "1655237119425c893f": {
      "type": "section-image-with-text",
      "blocks": {
        "16552371192c5f56ae-0": {
          "type": "title",
          "settings": {
            "title": "<h2>Would you like to visit us?</h2>",
            "title_underline_style": "none"
          }
        },
        "16552371192c5f56ae-1": {
          "type": "text",
          "settings": {
            "text": "<p> You can visit us in Canada:</p><p>123 Main Road St.<br/>Ottowa, Canada</p><p>Monday - Friday: 10am - 9pm<br/>Saturday: 11am - 9pm<br/>Sunday: Closed</p>"
          }
        },
        "16552371192c5f56ae-2": {
          "type": "buttons",
          "settings": {
            "show_first_link": true,
            "first_link_text": "Plan route",
            "first_link_url": "/",
            "first_button_style": "primary plain",
            "show_second_link": false,
            "second_link_text": "Button",
            "second_link_url": "",
            "second_button_style": "secondary plain"
          }
        }
      },
      "block_order": [
        "16552371192c5f56ae-0",
        "16552371192c5f56ae-1",
        "16552371192c5f56ae-2"
      ],
      "settings": {
        "overlay_opacity": 0,
        "color_palette": "scheme-6",
        "layout": "image-left",
        "text_position": "text-start",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    },
    "section-faq": {
      "type": "section-faq",
      "blocks": {
        "faq_1": {
          "type": "text",
          "settings": {
            "faq_question": "When will my order be delivered?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>We ship worldwide. Your order will be packaged really carefully and delivered wherever you want. Delivery takes between 2-4 business days. You will receive an e-mail after ordering with more information about the delivery.</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "faq_2": {
          "type": "text",
          "settings": {
            "faq_question": "Can I Return my product?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>Orders can be returned or exchanged within 30 days of receiving the parcel, providing they are in original resalable condition.</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "faq_3": {
          "type": "text",
          "settings": {
            "faq_question": "I would like to speak with the customer service. How can I get in touch?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>You can email and call us. We are there for you 24/7, every day.<br/>You can visit us in Canada:<br/><br/>123 example St.<br/>Ottowa, Canada</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "51446526-3af6-4670-8e9b-69889aa885e3": {
          "type": "text",
          "settings": {
            "faq_question": "What can I do if my item (or part of it) is damaged?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>We work hard to deliver your items without damage. Orders can be returned or exchanged within 30 days of receiving the parcel, providing they are in original resalable condition.</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "91da26b1-27a7-4617-8194-e0ddb3857976": {
          "type": "text",
          "settings": {
            "faq_question": "What if I paid too much?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>We work hard to deliver your items without damage. Orders can be returned or exchanged within 30 days of receiving the parcel, providing they are in original resalable condition.</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "f8e8db3a-0585-4d17-b956-25a786ddb592": {
          "type": "text",
          "settings": {
            "faq_question": "Where is Shopify located?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>Shopify is located in Ottowa, Canada.</p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        },
        "a4606a71-bd6c-4611-8905-84456f89417a": {
          "type": "text",
          "settings": {
            "faq_question": "What is the name of this theme for Shopify?",
            "icon": "none",
            "header_image_width": 25,
            "faq_answer": "<p>The name of this theme is Theme Seed for Shopify. </p>",
            "page": "",
            "image_width": 465,
            "liquid": "",
            "html": "",
            "show_contact_form": false
          }
        }
      },
      "block_order": [
        "faq_1",
        "faq_2",
        "faq_3",
        "51446526-3af6-4670-8e9b-69889aa885e3",
        "91da26b1-27a7-4617-8194-e0ddb3857976",
        "f8e8db3a-0585-4d17-b956-25a786ddb592",
        "a4606a71-bd6c-4611-8905-84456f89417a"
      ],
      "settings": {
        "alignment": "left",
        "compact": false,
        "title": "<h2>Frequently asked questions</h2>",
        "title_underline_style": "none",
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    },
    "page-service-info-blocks": {
      "type": "page-service-info-blocks",
      "blocks": {
        "1aac02b6-6669-4da9-8318-348245b71f6a": {
          "type": "information",
          "settings": {
            "color_palette": "scheme-8",
            "title": "Customer service",
            "text": "<p>Give your customers more details about your online store.</p>",
            "show_login_links": false,
            "show_shop_address": false,
            "show_maps_url": false,
            "show_phone_link": true,
            "show_mail_link": true,
            "show_whatsapp_link": true
          }
        },
        "18ebb386-ad40-4c3f-8386-2c45cb1a6cb2": {
          "type": "information",
          "settings": {
            "color_palette": "scheme-1",
            "title": "Account details",
            "text": "<p><a href=\"/account\" title=\"/account\">Track your last order</a>, <a href=\"/account\" title=\"/account\">view all orders</a> or <a href=\"/account/addresses\" title=\"/account/addresses\">manage your addresses</a>.</p>",
            "show_login_links": true,
            "show_shop_address": false,
            "show_maps_url": false,
            "show_phone_link": false,
            "show_mail_link": false,
            "show_whatsapp_link": false
          }
        },
        "e40e00a1-f1c1-46de-84f4-9f8ac1ca29af": {
          "type": "information",
          "settings": {
            "color_palette": "scheme-1",
            "title": "Theme Seed",
            "text": "<p>Give your customer more details about your online store. </p>",
            "show_login_links": true,
            "show_shop_address": true,
            "show_maps_url": true,
            "show_phone_link": false,
            "show_mail_link": false,
            "show_whatsapp_link": false
          }
        }
      },
      "block_order": [
        "1aac02b6-6669-4da9-8318-348245b71f6a",
        "18ebb386-ad40-4c3f-8386-2c45cb1a6cb2",
        "e40e00a1-f1c1-46de-84f4-9f8ac1ca29af"
      ],
      "settings": {}
    },
    "section-google-maps": {
      "type": "section-google-maps",
      "settings": {
        "google_maps_iframe": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2800.4613648351315!2d-75.69569834823506!3d45.420200278997804!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4cce0502f07d210b%3A0xa1502f7f1d25c118!2sShopify!5e0!3m2!1snl!2snl!4v1653470436404!5m2!1snl!2snl\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>",
        "spacing_desktop": 20,
        "spacing_mobile": 20
      }
    }
  },
  "order": [
    "breadcrumbs",
    "section-slideshow",
    "page-service-menu",
    "1655236642bd2b4796",
    "1655237119425c893f",
    "section-faq",
    "page-service-info-blocks",
    "section-google-maps"
  ]
}
