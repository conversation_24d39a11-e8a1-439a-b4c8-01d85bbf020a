/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "breadcrumbs": {
      "type": "breadcrumbs",
      "settings": {
        "hide_mobile": false
      }
    },
    "main-article": {
      "type": "main-article",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {
            "show_banner": true,
            "overlay_opacity": 25,
            "show_date": true,
            "show_author": true,
            "show_article_reading_time": true
          }
        },
        "featured_image": {
          "type": "featured_image",
          "disabled": true,
          "settings": {}
        },
        "excerpt": {
          "type": "excerpt",
          "settings": {}
        },
        "content": {
          "type": "content",
          "settings": {}
        },
        "tags": {
          "type": "tags",
          "settings": {}
        },
        "comments": {
          "type": "comments",
          "settings": {}
        },
        "db5d637d-f346-4923-afc2-d959a7a5a8a6": {
          "type": "spacer",
          "settings": {
            "height": 20
          }
        },
        "cae634eb-3596-48c8-9d94-628d20d4d95b": {
          "type": "share_buttons",
          "settings": {
            "share_whatsapp": true,
            "share_facebook": true,
            "share_twitter": true,
            "share_pinterest": true,
            "share_messenger": true,
            "share_email": true
          }
        }
      },
      "block_order": [
        "title",
        "featured_image",
        "excerpt",
        "content",
        "tags",
        "comments",
        "db5d637d-f346-4923-afc2-d959a7a5a8a6",
        "cae634eb-3596-48c8-9d94-628d20d4d95b"
      ],
      "settings": {
        "big_fontsize": true,
        "compact": true
      }
    },
    "blogs_row": {
      "type": "section-blog-posts",
      "settings": {
        "blog": "",
        "number_of_items": 3,
        "text_alignment": "start",
        "title": "<h1>Blog posts</h1>",
        "link_text": "View all",
        "link_url": "",
        "show_link": false,
        "show_image": true,
        "show_date": true,
        "show_author": true,
        "show_excerpt": false,
        "spacing_desktop": 50,
        "spacing_mobile": 30
      }
    }
  },
  "order": [
    "breadcrumbs",
    "main-article",
    "blogs_row"
  ]
}
