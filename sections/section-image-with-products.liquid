{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  assign ref_img = section.settings.image

  if section.settings.mobile_height == 'adapt' and ref_img
    assign padding_bottom_mobile = 1 | divided_by: ref_img.aspect_ratio | times: 100 | round: 2
  else
    assign aspect_ratio = section.settings.mobile_height | split: '/'
    assign temp = aspect_ratio[0] | append: '.0'
    assign ratio = aspect_ratio[1] | divided_by: temp | round: 2
    assign padding_bottom_mobile = ratio | times: 100 | round: 2
  endif
-%}
<article>
  <div
    class="
      m6ac
      align-stretch
      mobile-inv
      {% if section.settings.layout == 'image-left' %}inv{% endif %}
      has-l4cl
      m0
    "
    style="
      --padding_bottom_mobile: {{ padding_bottom_mobile }};
      --align_text: {{ section.settings.text_position | split: ' ' | first }};
      --justify_text: {{ section.settings.text_position | split: ' ' | last }};
    "
  >
    <div class="w33">
      <ul class="l4cl {% if settings.enable_quick_buy %}with-quick-buy{% endif %} hr dont-move">
        {%- liquid
          if section.settings.products == empty
            for i in (1..3)
              capture placeholder_int
                cycle 1, 2, 3
              endcapture
              render 'product-item', product: blank, placeholder_int: placeholder_int, enable_quick_buy_desktop: section.settings.enable_quick_buy_desktop, enable_quick_buy_mobile: section.settings.enable_quick_buy_mobile, enable_quick_buy_qty_selector: section.settings.enable_quick_buy_qty_selector, quick_buy_compact: section.settings.enable_quick_buy_compact, enable_quick_buy_drawer: section.settings.enable_quick_buy_drawer, layout: 'list'
            endfor
          endif
          for product in section.settings.products
            capture placeholder_int
              cycle 1, 2, 3, 4, 5, 6
            endcapture
            render 'product-item', product: product, placeholder_int: placeholder_int, enable_quick_buy_desktop: section.settings.enable_quick_buy_desktop, enable_quick_buy_mobile: section.settings.enable_quick_buy_mobile, enable_quick_buy_qty_selector: section.settings.enable_quick_buy_qty_selector, quick_buy_compact: section.settings.enable_quick_buy_compact, enable_quick_buy_drawer: section.settings.enable_quick_buy_drawer, enable_color_picker: section.settings.enable_color_picker, layout: 'list'
          endfor
        -%}
      </ul>
    </div>
    <div class="w66">
      <ul class="l4ft{% if section.settings.width_mobile == 'wide' %} fullwidth-m{% endif %}">
        <li
          class="
            w100
            {{ section.settings.text_position }}
            {{ section.settings.mobile_height }}-mobile
          "
        >
          <div
            class="
              palette-{{ section.settings.color_palette }}
              module-color-palette
              text-{{ section.settings.text_position | split: ' ' | last }}
              main
            "
          >
            <figure>
              <span
                class="img-overlay"
                style="opacity:{{ section.settings.overlay_opacity | divided_by: 100.0 }}"
              ></span>
              {%- if section.settings.image -%}
                <picture>
                  <img
                    src="{{ section.settings.image | image_url: width: 640 }}"
                    srcset="{% render 'image-srcset', image: section.settings.image %}"
                    sizes="
                      (min-width: 1300px) calc({% if settings.width < 2000 %}{{ settings.width }}px{% else %}100vw{% endif %} / 2),
                      100vw
                    "
                    width="640"
                    height="385"
                    alt="{{ section.settings.image.alt | default: section.settings.title | escape }}"
                    style="object-position: {{ section.settings.image.presentation.focal_point }}"
                    loading="{% if section.index > 1 %}lazy{% else %}eager{% endif %}"
                  >
                </picture>
              {% else %}
                {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}z
              {% endif %}
            </figure>
            <div
              class="
                {% if section.settings.title_underline_style != 'none' %}
                  title-underline-none
                  {% if section.settings.title_underline_style contains 'accent' %}
                    title-underline-accent
                  {% elsif section.settings.title_underline_style contains 'gradient' %}
                    title-underline-gradient
                  {% endif %}
                  {% if section.settings.title_underline_style contains 'secondary_font' %}
                    title-underline-secondary-font
                  {% endif %}
                {% endif %}
              "
            >
              {%- if section.settings.title != empty -%}
                {{ section.settings.title }}
              {%- endif -%}
              {%- if section.settings.text -%}{{ section.settings.text }}{%- endif -%}
              {%- if section.settings.show_link
                and section.settings.link_text != empty
                and section.settings.link_url != blank
              -%}
                {%- liquid
                  assign button_color = section.settings.button_style | split: ' ' | first
                  assign button_style = section.settings.button_style | split: ' ' | last
                  assign is_link = false
                  if button_style == 'link'
                    assign is_link = true
                  endif
                -%}
                <p class="link{% unless is_link %}-btn{% endunless %}">
                  <a
                    href="{{ section.settings.link_url }}"
                    class="overlay-{{ button_color }} {% if is_link %}strong inline{% elsif button_style == 'inv' %}inv{% endif %}"
                  >
                    {% if is_link %}<span>{% endif -%}
                    {{- section.settings.link_text -}}
                    {%- if is_link %}</span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>{% endif %}
                  </a>
                </p>
              {%- endif %}
              {%- if section.settings.show_overlay_link and section.settings.overlay_url != blank -%}
                <a
                  class="link-overlay"
                  href="{{ section.settings.overlay_url }}"
                  aria-label="{{ section.settings.title | escape | default: section.settings.image.alt | default: "Image with products" }}"
                ></a>
              {%- endif -%}
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</article>

<style>
  #shopify-section-{{ section.id }} { position: relative; z-index: {{ section.settings.fix_zindex }}!important; }
  @media only screen and (min-width: 47.5em) {
    #shopify-section-{{ section.id }} .m6ac { margin-bottom: {{ section.settings.spacing_desktop | minus: 8 }}px; }
  }
  @media only screen and (max-width: 47.5em) {
    #shopify-section-{{ section.id }} .l4cl { margin-bottom: {{ section.settings.spacing_mobile | minus: 8 }}px; }
  }

  #shopify-section-{{ section.id }}  .m6ac[style*="--padding_bottom_mobile"] .l4ft {
    --mih: 0;
  }
  #shopify-section-{{ section.id }}  .m6ac[style*="--padding_bottom_mobile"] .l4ft .main {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    align-items: var(--align_text) !important;
    justify-content: var(--justify_text) !important;
  }

  @media only screen and (max-width: 761px) {
    #shopify-section-{{ section.id }}  .m6ac[style*="--padding_bottom_mobile"] .l4ft .main:after {
      --padding_bottom: var(--padding_bottom_mobile);
      content: ""!important;
      display: block!important;
      padding-bottom: calc(var(--padding_bottom)* 1%)!important;
    }
    #shopify-section-{{ section.id }}  .m6ac[style*="--padding_bottom_mobile"] .l4ft.fullwidth-m .main:after {
      --padding_bottom: var(--padding_bottom_mobile);
      padding-bottom: calc(var(--padding_bottom)* 1vw - var(--scrollbar_width));
    }
  }
</style>

{% schema %}
{
  "name": "t:sections.image_with_products.name",
  "disabled_on": {
    "templates": ["gift_card", "password"],
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "id": "image",
      "type": "image_picker",
      "label": "t:sections.image_with_products.settings.image.label"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:sections.image_with_products.settings.overlay_opacity.label",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "default": 0
    },
    {
      "type": "color_scheme",
      "id": "color_palette",
      "label": "t:global.color_palette.label",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.image_with_products.settings.layout.label",
      "options": [
        {
          "value": "image-left",
          "label": "t:sections.image_with_products.settings.layout.options__1.label"
        },
        {
          "value": "image-right",
          "label": "t:sections.image_with_products.settings.layout.options__2.label"
        }
      ],
      "default": "image-left"
    },
    {
      "id": "products",
      "type": "product_list",
      "label": "t:sections.image_with_products.settings.products.label",
      "limit": 6
    },
    {
      "id": "text_position",
      "type": "select",
      "label": "t:sections.image_with_products.settings.text_position.label",
      "options": [
        {
          "value": "start",
          "label": "t:sections.image_with_products.settings.text_position.options__1.label"
        },
        {
          "value": "start center",
          "label": "t:sections.image_with_products.settings.text_position.options__2.label"
        },
        {
          "value": "start end",
          "label": "t:sections.image_with_products.settings.text_position.options__3.label"
        },
        {
          "value": "center start",
          "label": "t:sections.image_with_products.settings.text_position.options__4.label"
        },
        {
          "value": "center",
          "label": "t:sections.image_with_products.settings.text_position.options__5.label"
        },
        {
          "value": "center end",
          "label": "t:sections.image_with_products.settings.text_position.options__6.label"
        },
        {
          "value": "end start",
          "label": "t:sections.image_with_products.settings.text_position.options__7.label"
        },
        {
          "value": "end center",
          "label": "t:sections.image_with_products.settings.text_position.options__8.label"
        },
        {
          "value": "end",
          "label": "t:sections.image_with_products.settings.text_position.options__9.label"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "t:global.typography.title.label"
    },
    {
      "type": "richtext",
      "id": "title",
      "label": "t:global.typography.title.label",
      "info": "t:global.typography.title.info",
      "default": "<h2>Image banner with products</h2>"
    },
    {
      "type": "select",
      "id": "title_underline_style",
      "label": "t:global.typography.title_underline_style.label",
      "options": [
        {
          "value": "none",
          "label": "t:global.typography.title_underline_style.none.label"
        },
        {
          "value": "secondary_font",
          "label": "t:global.typography.title_underline_style.secondary_font.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_accent",
          "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_gradient",
          "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "accent",
          "label": "t:global.typography.title_underline_style.accent.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        },
        {
          "value": "gradient",
          "label": "t:global.typography.title_underline_style.gradient.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:global.typography.text.header"
    },
    {
      "id": "text",
      "type": "richtext",
      "label": "t:global.typography.text.label",
      "default": "<p>Give customers details about the banner image(s) or content and add related products</p>"
    },
    {
      "type": "header",
      "content": "t:global.button.header"
    },
    {
      "id": "show_link",
      "type": "checkbox",
      "label": "t:global.button.show_link.label",
      "default": true
    },
    {
      "id": "link_text",
      "type": "text",
      "label": "t:global.button.link_text.label",
      "default": "Button",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "id": "link_url",
      "type": "url",
      "label": "t:global.button.link_url.label",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:global.button.button_style.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "primary link",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "secondary link",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "tertiary link",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.link"
        }
      ],
      "default": "primary plain",
      "visible_if": "{{ section.settings.show_link }}"
    },
    {
      "type": "header",
      "content": "t:global.overlay.header"
    },
    {
      "id": "show_overlay_link",
      "type": "checkbox",
      "label": "t:global.overlay.show_overlay_link.label",
      "default": true
    },
    {
      "id": "overlay_url",
      "type": "url",
      "label": "t:global.overlay.overlay_url.label",
      "visible_if": "{{ section.settings.show_overlay_link }}"
    },
    {
      "type": "header",
      "content": "t:sections.image_with_products.settings.quick_buy.header"
    },
    {
      "type": "paragraph",
      "content": "t:sections.image_with_products.settings.quick_buy.paragraph"
    },
    {
      "id": "enable_quick_buy_desktop",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_desktop.label",
      "default": true
    },
    {
      "id": "enable_quick_buy_mobile",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_mobile.label",
      "default": true
    },
    {
      "id": "enable_quick_buy_drawer",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_drawer.label",
      "info": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_drawer.info",
      "visible_if": "{{ section.settings.enable_quick_buy_desktop or section.settings.enable_quick_buy_mobile }}"
    },
    {
      "id": "enable_quick_buy_qty_selector",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_qty_selector.label",
      "info": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_qty_selector.info",
      "default": true,
      "visible_if": "{{ section.settings.enable_quick_buy_desktop or section.settings.enable_quick_buy_mobile }}"
    },
    {
      "id": "enable_color_picker",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_color_picker.label",
      "visible_if": "{{ section.settings.enable_quick_buy_desktop or section.settings.enable_quick_buy_mobile }}"
    },
    {
      "id": "enable_quick_buy_compact",
      "type": "checkbox",
      "label": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_compact.label",
      "info": "t:sections.image_with_products.settings.quick_buy.enable_quick_buy_compact.info",
      "visible_if": "{{ section.settings.enable_quick_buy_desktop or section.settings.enable_quick_buy_mobile }}"
    },
    {
      "type": "header",
      "content": "t:sections.image_with_products.settings.mobile.header"
    },
    {
      "type": "select",
      "id": "width_mobile",
      "label": "t:sections.image_with_products.settings.mobile.width_mobile.label",
      "options": [
        {
          "value": "wide",
          "label": "t:sections.image_with_products.settings.mobile.width_mobile.options__1.label"
        },
        {
          "value": "boxed",
          "label": "t:sections.image_with_products.settings.mobile.width_mobile.options__2.label"
        }
      ],
      "default": "boxed"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "t:global.layout.height.label",
      "options": [
        {
          "value": "16/9",
          "label": "t:global.layout.height.16_9.label"
        },
        {
          "value": "1/1",
          "label": "t:global.layout.height.1_1.label"
        },
        {
          "value": "4/5",
          "label": "t:global.layout.height.4_5.label"
        },
        {
          "value": "adapt",
          "label": "t:global.layout.height.adapt.label"
        }
      ],
      "default": "1/1"
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 50
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 30
    },
    {
      "id": "fix_zindex",
      "type": "range",
      "label": "t:global.fix_zindex.label",
      "info": "t:global.fix_zindex.info",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:sections.image_with_products.presets.name"
    }
  ]
}
{% endschema %}
