{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  case section.settings.items_width
    when 1
      assign width_class = 'w100'
    when 2
      assign width_class = 'w50'
    when 3
      assign width_class = 'w33'
    when 4
      assign width_class = 'w25'
    when 5
      assign width_class = 'w20'
  endcase

  assign last_blocks = section.blocks.size | modulo: section.settings.items_width

  if section.settings.height_mobile == 'adapt' and section.settings.layout_mobile == 'compact'
    if section.blocks.first.settings.image_mobile
      assign ref_img = section.blocks.first.settings.image_mobile
    else
      assign ref_img = section.blocks.first.settings.image
      if ref_img == blank and section.blocks.first.settings.video
        assign ref_img = section.blocks.first.settings.video
      endif
    endif
    assign padding_bottom_mobile = 1 | divided_by: ref_img.aspect_ratio | times: 100 | round: 2
  else
    assign aspect_ratio = section.settings.height_mobile | split: '/'
    assign temp = aspect_ratio[0] | append: '.0'
    assign ratio = aspect_ratio[1] | divided_by: temp | round: 2
    assign padding_bottom_mobile = ratio | times: 100 | round: 2
  endif
  assign row_width = 0
-%}

<article>
  <ul
    class="
      l4ft
      static
      {% if section.settings.width == 'wide' %}fullwidth{% endif %}
      {% if section.settings.image_zoom %}zoom{% endif %}
      {% if section.settings.layout_mobile == 'compact' %}mobile-compact{% endif %}
      {% if section.settings.space_between == 0 %}outer-radius{% endif %}
      mobile-box
      dont-move
    "
    style="--dist_a: {{ section.settings.space_between }}px;"
  >
    {%- for block in section.blocks -%}
      {%- liquid
        capture current
          cycle 1, 2
        endcapture
        assign blocks_left = forloop.length | minus: forloop.index
        if blocks_left < last_blocks
          assign width = 100 | divided_by: last_blocks
          assign different_width_class = 'w' | append: width
        else
          assign different_width_class = false
        endif
        if different_width_class
          assign img_width = width
        else
          assign img_width = width_class | remove: 'w' | plus: 0
        endif

        if block.settings.video
          assign source = block.settings.video.sources | where: 'format', 'mp4' | first
          if source == null
            assign source = block.settings.video.sources | where: 'format', 'mov' | first
          endif
          if source == null
            assign source = block.settings.video.sources.first
          endif
        endif

        if forloop.first or row_width == 0
          assign padding_bottom = 0
          assign ref_img = block.settings.image
          if ref_img == blank and block.settings.video
            assign ref_img = block.settings.video
          endif
          if section.settings.height == 'adapt'
            assign padding_bottom = 1 | divided_by: ref_img.aspect_ratio | times: 100 | round: 2
          else
            assign aspect_ratio = section.settings.height | split: '/'
            assign temp = aspect_ratio[0] | append: '.0'
            assign ratio = aspect_ratio[1] | divided_by: temp | round: 2
            assign padding_bottom = ratio | times: 100 | round: 2
          endif
        endif

        if section.settings.height_mobile == 'adapt' and section.settings.layout_mobile == 'rows'
          if block.settings.image_mobile
            assign ref_img = block.settings.image_mobile
          else
            assign ref_img = block.settings.image
            if ref_img == blank and block.settings.video
              assign ref_img = block.settings.video
            endif
          endif
          assign padding_bottom_mobile = 1 | divided_by: ref_img.aspect_ratio | times: 100 | round: 2
        endif
      -%}
      <li
        class="
          block-{{ block.id }}
          item-{{ forloop.index }}
          dont-move
          {{ block.settings.text_position }}
          {% if different_width_class %}{{ different_width_class }}{% else %}{{ width_class }}{% endif %}
        "
        style="
          --height: 0;
          --padding_bottom: {{ padding_bottom }};
          --padding_bottom_mobile: {{ padding_bottom_mobile }};
        "
        {{ block.shopify_attributes }}
      >
        {%- if block.settings.video or block.settings.image -%}
          <div
            class="
              palette-{{ block.settings.color_palette }}
              module-color-palette
              main
            "
          >
            <figure>
              {%- if block.settings.image -%}
                {%- if block.settings.video -%}
                  <a
                    data-fancybox="section-aside-{{ section.id }}-{{ block.id }}"
                    data-title="{{ block.settings.video.alt | escape }}"
                    href="{{ source.url }}"
                  >
                {%- endif -%}
                <span
                  class="img-overlay"
                  style="opacity:{{ block.settings.overlay_opacity_img | divided_by: 100.0 }}"
                ></span>
                <picture>
                  <img
                    src="{{ block.settings.image | image_url: width: 620 }}"
                    srcset="{% render 'image-srcset', image: block.settings.image %}"
                    sizes="
                      {% if section.settings.width == 'boxed' or settings.width < 2000 %}
                        (min-width: 1300px) {% if img_width == 100 %}{{ settings.width }}px{% else %}calc({{ settings.width }}px * 0.{{ img_width }}){% endif %},
                      {% endif %}
                      (min-width: 1000px) {% if img_width == 100 %}100vw{% else %}calc(100vw * 0.{{ img_width }}){% endif %},
                      {% if section.settings.layout_mobile == 'compact' %}315px{% else %}100vw{% endif %}
                    "
                    width="620"
                    height="700"
                    alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                    style="object-position: {{ block.settings.image.presentation.focal_point }}"
                    loading="{% if section.index > 1 or forloop.first == false %}lazy{% else %}eager{% endif %}"
                  >
                </picture>
                {%- if block.settings.video -%}
                  {%- if block.settings.video -%}<i aria-hidden="true" class="icon-play"></i></a>{%- endif -%}
                {%- endif -%}
              {% elsif block.settings.video %}
                <span
                  class="img-overlay"
                  style="opacity:{{ block.settings.overlay_opacity_img | divided_by: 100.0 }}"
                ></span>
                {%- liquid
                  assign source = block.settings.video.sources | where: 'height', 1080 | where: 'format', 'mp4' | first
                  if source == null
                    assign source = block.settings.video.sources | where: 'format', 'mp4' | first
                  endif
                  if source == null
                    assign source = block.settings.video.sources | where: 'format', 'mov' | first
                  endif
                  if source == null
                    assign source = block.settings.video.sources.first
                  endif
                -%}
                <video
                  class="lazy"
                  autoplay
                  muted
                  loop
                  playsinline
                  poster="{{ block.settings.video.preview_image | image_url }}"
                >
                  <source data-src="{{ source.url }}" type="video/mp4">
                </video>
              {% endif %}
            </figure>
            {%- unless block.settings.video and block.settings.image -%}
              {%- if block.settings.show_overlay_link and block.settings.overlay_url != blank -%}
                <a class="link-overlay" href="{{ block.settings.overlay_url }}"></a>
              {%- endif -%}
            {%- endunless -%}
          </div>
        {%- endif -%}
        <div
          class="
            palette-{{ block.settings.color_palette }}
            module-color-palette
            content box
            {% unless block.settings.video or block.settings.image %}no-img{% endunless %}
            {% if block.settings.title_underline_style != 'none' %}
              title-underline-none
              {% if block.settings.title_underline_style contains 'accent' %}
                title-underline-accent
              {% elsif block.settings.title_underline_style contains 'gradient' %}
                title-underline-gradient
              {% endif %}
              {% if block.settings.title_underline_style contains 'secondary_font' %}
                title-underline-secondary-font
              {% endif %}
            {% endif %}
          "
        >
          {%- if block.settings.title != blank -%}
            <{{ section.settings.title_size_blocks }}>
              {{ block.settings.title }}
            </{{ section.settings.title_size_blocks }}>
          {%- endif -%}
          {%- if block.settings.text -%}{{ block.settings.text }}{%- endif -%}
          {%- if block.settings.show_link and block.settings.link_text != empty and block.settings.link_url != blank -%}
            {%- liquid
              assign button_color = block.settings.button_style | split: ' ' | first
              assign button_style = block.settings.button_style | split: ' ' | last
              assign is_link = false
              if button_style == 'link'
                assign is_link = true
              endif
            -%}
            <p class="link{% unless is_link %}-btn{% endunless %}">
              <a
                href="{{ block.settings.link_url }}"
                class="overlay-{{ button_color }} {% if is_link %}inline strong{% elsif button_style == 'inv' %}inv{% endif %}"
              >
                {% if is_link %}<span>{% endif -%}
                {{- block.settings.link_text -}}
                {%- if is_link %}</span>&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i>{% endif %}
              </a>
            </p>
          {%- endif %}
          {%- unless block.settings.video -%}
            {%- if block.settings.show_overlay_link and block.settings.overlay_url != blank -%}
              <a
                class="link-overlay"
                href="{{ block.settings.overlay_url }}"
                aria-label="{{ block.settings.title | escape | default: block.settings.image.alt | default: "Promo banner" }}"
              ></a>
            {%- endif -%}
          {%- endunless -%}
        </div>
      </li>
      {% liquid
        assign row_width = row_width | plus: img_width
        if row_width >= 99
          assign row_width = 0
        endif
      %}
    {%- endfor -%}
  </ul>
</article>

<style>
  #shopify-section-{{ section.id }} { position: relative; z-index: {{ section.settings.fix_zindex }}!important; }
  {%- for block in section.blocks -%}
  #shopify-section-{{ section.id }} li.item-{{ forloop.index }} .module-color-palette.box:before { opacity: {{ block.settings.overlay_opacity | divided_by: 100.0 }}; }
  {%- endfor -%}
  #shopify-section-{{ section.id }} li div.main > figure { will-change: transform; }
  {% if section.settings.shadow %}
  #shopify-section-{{ section.id }} li .box { box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08); }
  {% endif %}
  @media only screen and (min-width: 47.5em) {
    #shopify-section-{{ section.id }} .l4ft { margin-bottom: {{ section.settings.spacing_desktop | minus: 16 }}px; }
  {% if section.settings.spacing_desktop < 0 %}
    #shopify-section-{{ section.id }} + *.has-kinetic-text { pointer-events: none; }
  {% endif %}
  }
  @media only screen and (max-width: 47.5em) {
  {% if section.settings.layout_mobile == 'rows' %}
    #shopify-section-{{ section.id }} .l4ft { margin-bottom: {{ section.settings.spacing_mobile }}px; }
  {% else %}
    #shopify-section-{{ section.id }} .l4ft { margin-bottom: {{ section.settings.spacing_mobile | minus: 16 }}px; }
  {% endif %}
  {% if section.settings.spacing_mobile < 0 %}
    #shopify-section-{{ section.id }} + *.has-kinetic-text { pointer-events: none; }
  {% endif %}
  }

  @media only screen and (max-width: 760px) {
    #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main {
      --padding_bottom: var(--padding_bottom_mobile); !important;
    }
  }

  #shopify-section-{{ section.id }} .l4ft li {
    min-height: 0!important;
  }
  #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    min-height: 0 !important;
    align-items: var(--align_text) !important;
    justify-content: var(--justify_text) !important;
  }

  #shopify-section-{{ section.id }} .l4ft li[style*="--padding_bottom"] > .main:after {
    content: "";
    display: block;
    padding-bottom: calc(var(--padding_bottom)* 1%);
  }
</style>

{% schema %}
{
  "name": "t:sections.promo_gallery.name",
  "max_blocks": 9,
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "range",
      "id": "items_width",
      "label": "t:sections.promo_gallery.settings.items_width.label",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3
    },
    {
      "type": "select",
      "id": "width",
      "label": "t:global.layout.width.label",
      "options": [
        {
          "value": "boxed",
          "label": "t:global.layout.width.options__1.label"
        },
        {
          "value": "wide",
          "label": "t:global.layout.width.options__2.label"
        }
      ],
      "default": "boxed"
    },
    {
      "type": "select",
      "id": "height",
      "label": "t:global.layout.height.label_banner",
      "info": "t:sections.promo_gallery.settings.height.info",
      "options": [
        {
          "value": "16/9",
          "label": "t:global.layout.height.16_9.label"
        },
        {
          "value": "1/1",
          "label": "t:global.layout.height.1_1.label"
        },
        {
          "value": "4/5",
          "label": "t:global.layout.height.4_5.label"
        },
        {
          "value": "adapt",
          "label": "t:global.layout.height.adapt.label"
        }
      ],
      "default": "16/9"
    },
    {
      "type": "range",
      "id": "space_between",
      "label": "t:sections.promo_gallery.settings.space_between.label",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "header",
      "content": "t:sections.promo_gallery.settings.banners.header"
    },
    {
      "id": "image_zoom",
      "type": "checkbox",
      "label": "t:sections.promo_gallery.settings.image_zoom.label",
      "default": true
    },
    {
      "id": "shadow",
      "type": "checkbox",
      "label": "t:sections.promo_gallery.settings.shadow.label"
    },
    {
      "type": "select",
      "id": "title_size_blocks",
      "label": "t:sections.promo_gallery.settings.banners.title_size_blocks.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h2"
    },
    {
      "type": "header",
      "content": "t:sections.promo_gallery.settings.mobile.header"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "t:sections.promo_gallery.settings.mobile.layout_mobile.label",
      "options": [
        {
          "value": "rows",
          "label": "t:sections.promo_gallery.settings.mobile.layout_mobile.options__1.label"
        },
        {
          "value": "compact",
          "label": "t:sections.promo_gallery.settings.mobile.layout_mobile.options__2.label"
        }
      ],
      "default": "rows"
    },
    {
      "type": "select",
      "id": "height_mobile",
      "label": "t:global.layout.height.label_banner",
      "options": [
        {
          "value": "16/9",
          "label": "t:global.layout.height.16_9.label"
        },
        {
          "value": "1/1",
          "label": "t:global.layout.height.1_1.label"
        },
        {
          "value": "4/5",
          "label": "t:global.layout.height.4_5.label"
        },
        {
          "value": "adapt",
          "label": "t:global.layout.height.adapt.label"
        }
      ],
      "default": "16/9"
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": -300,
      "max": 300,
      "step": 10,
      "unit": "px",
      "default": 50
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -300,
      "max": 300,
      "step": 10,
      "unit": "px",
      "default": 30
    },
    {
      "id": "fix_zindex",
      "type": "range",
      "label": "t:global.fix_zindex.label",
      "info": "t:global.fix_zindex.info",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.promo_gallery.blocks.image.name",
      "settings": [
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:sections.promo_gallery.blocks.image.settings.image.label"
        },
        {
          "type": "video",
          "id": "video",
          "label": "t:sections.promo_gallery.blocks.image.settings.video.label"
        },
        {
          "type": "range",
          "id": "overlay_opacity_img",
          "label": "t:sections.promo_gallery.blocks.image.settings.overlay_opacity_img.label",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0
        },
        {
          "type": "color_scheme",
          "id": "color_palette",
          "label": "t:global.color_palette.label",
          "default": "scheme-1"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "t:sections.promo_gallery.blocks.image.settings.overlay_opacity.label",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 100
        },
        {
          "id": "text_position",
          "type": "select",
          "label": "t:sections.promo_gallery.blocks.image.settings.text_position.label",
          "options": [
            {
              "value": "text-start",
              "label": "t:sections.promo_gallery.blocks.image.settings.text_position.options__1.label"
            },
            {
              "value": "text-center",
              "label": "t:sections.promo_gallery.blocks.image.settings.text_position.options__2.label"
            },
            {
              "value": "align-middle",
              "label": "t:sections.promo_gallery.blocks.image.settings.text_position.options__3.label"
            },
            {
              "value": "text-center align-middle",
              "label": "t:sections.promo_gallery.blocks.image.settings.text_position.options__4.label"
            }
          ],
          "default": "text-center align-middle"
        },
        {
          "type": "header",
          "content": "t:global.typography.title.label"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:global.typography.title.label",
          "info": "t:global.typography.title.info",
          "default": "Promo banner"
        },
        {
          "type": "select",
          "id": "title_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:global.typography.text.header"
        },
        {
          "id": "text",
          "type": "richtext",
          "label": "t:global.typography.text.label",
          "default": "<p>Describe a product or make an announcement</p>"
        },
        {
          "type": "header",
          "content": "t:global.button.header"
        },
        {
          "id": "show_link",
          "type": "checkbox",
          "label": "t:global.button.show_link.label",
          "default": true
        },
        {
          "id": "link_text",
          "type": "text",
          "label": "t:global.button.link_text.label",
          "default": "Button",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "id": "link_url",
          "type": "url",
          "label": "t:global.button.link_url.label",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "primary link",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "secondary link",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "tertiary link",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.link"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "type": "header",
          "content": "t:global.overlay.header"
        },
        {
          "id": "show_overlay_link",
          "type": "checkbox",
          "label": "t:global.overlay.show_overlay_link.label",
          "default": true
        },
        {
          "id": "overlay_url",
          "type": "url",
          "label": "t:global.overlay.overlay_url.label",
          "visible_if": "{{ block.settings.show_overlay_link }}"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.promo_gallery.presets.name",
      "settings": {},
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}
