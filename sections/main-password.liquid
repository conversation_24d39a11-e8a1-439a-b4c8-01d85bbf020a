{%- liquid
  assign password_form_shown = false
  assign newsletter_form_shown = false
-%}
{%- for block in section.blocks -%}
  {%- case block.type -%}
    {%- when '@app' -%}
      {% render block %}
    {%- when 'spacer' -%}
      <div class="module-spacer" style="margin-bottom:{{ block.settings.height }}px;" {{ block.shopify_attributes }}></div>
    {%- when 'logo'-%}
      <figure class="m65" {{ block.shopify_attributes }}>
        {%- if block.settings.svg_logo or block.settings.logo -%}
          <img class="logo"
            {%- if block.settings.svg_logo -%}
              src="{{- block.settings.svg_logo -}}"
            {%- else -%}
              srcset="{%- render 'image-srcset', image: block.settings.logo, max_width: 500 -%}"
              src="{{ block.settings.logo | image_url: width: block.settings.logo_width }}"
            {%- endif -%}
            width="{{ block.settings.logo_width }}"
            height="100"
            alt="{{ block.settings.logo.alt | default: shop.name | escape }}"
            loading="eager"
          >
        {%- else -%}
          <h1>{{ shop.name }}</h1>
        {%- endif -%}
      </figure>
    {%- when 'title'-%}
      <h1 class="m15" {{ block.shopify_attributes }}>{{ 'password_page.title' | t }}</h1>
    {%- when 'message'-%}
      {%- unless shop.password_message == blank -%}
        <p {{ block.shopify_attributes }}>{{ shop.password_message }}</p>
      {%- endunless -%}
    {%- when 'newsletter_form'-%}
      {%- if password_form_shown -%}
        <p>{{ 'password_page.or_sign_up' | t }}</p>
      {%- endif -%}
      {%- assign newsletter_form_class = 'f8lg m10' -%}
      {%- form 'customer', class: newsletter_form_class -%}
        {%- if form.posted_successfully? -%}
          <script>
            document.addEventListener('DOMContentLoaded', function () {
              var alertAttributes = { message: "{{ 'password_page.newsletter_form.success_message' | t }}", type: "success", origin: "password" },
                      showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
              window.dispatchEvent(showAlertEvent);
            });
          </script>
        {%- elsif form.errors -%}
          {%- for error in form.errors -%}
            <script>
              document.addEventListener('DOMContentLoaded', function () {
                var alertAttributes = { message: "{%- if error == 'form' -%}{{ form.errors.messages[error] }}{%- else -%}{{ form.errors.translated_fields[error] | capitalize }} {{ form.errors.messages[error] }}{%- endif -%}", type: "error", origin: "recover_password" },
                        showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                window.dispatchEvent(showAlertEvent);
              });
            </script>
          {%- endfor -%}
        {%- endif -%}
        <fieldset {{ block.shopify_attributes }}>
          <legend>{{ 'password_page.newsletter_form.title' | t }}</legend>
          <input type="hidden" name="contact[tags]" value="prospect, password page">
          <p>
            <label for="email_address">{{ 'password_page.newsletter_form.email' | t }}<span class="overlay-theme">*</span></label>
            <input type="email" id="email_address" name="contact[email]" placeholder="{{ 'password_page.newsletter_form.email' | t }}" required>
          </p>
          <p class="submit"><button type="submit">{{ 'password_page.newsletter_form.button_text' | t }}</button></p>
        </fieldset>
      {%- endform -%}
      {%- assign newsletter_form_shown = true -%}
    {%- when 'password_form'-%}
      {%- if newsletter_form_shown -%}
        <p>{{ 'password_page.or_enter_password' | t }}</p>
      {%- endif -%}
      {%- assign password_form_class = 'f8lg m20' -%}
      {%- form 'storefront_password', class: password_form_class -%}
        {{ form.errors | default_errors }}
        <fieldset {{ block.shopify_attributes }}>
          <legend>{{ 'password_page.password_form.title' | t }}</legend>
          <p>
            <label for="password">{{ 'password_page.password_form.password' | t }}<span class="overlay-theme">*</span> <a href="./" class="show"><span>{{ 'general.accessibility.show' | t }}</span> <span class="hidden">{{ 'general.accessibility.hide' | t }}</span></a></label>
            <input type="password" id="password" name="password" placeholder="{{ 'password_page.password_form.password' | t }}" required>
          </p>
          <p class="submit"><button type="submit">{{ 'password_page.password_form.button_text' | t }}</button></p>
        </fieldset>
      {%- endform -%}
      {%- assign password_form_shown = true -%}
    {%- when 'social_sharing'-%}
      {%- capture socials -%}
        {%- if settings.social_instagram -%}instagram|{{settings.social_instagram}}->{%- endif -%}
        {%- if settings.social_pinterest -%}pinterest|{{settings.social_pinterest}}->{%- endif -%}
        {%- if settings.social_youtube -%}youtube|{{settings.social_youtube}}->{%- endif -%}
        {%- if settings.social_facebook -%}facebook|{{settings.social_facebook}}->{%- endif -%}
        {%- if settings.social_twitter -%}twitter|{{settings.social_twitter}}{%- endif -%}
      {%- endcapture -%}
      {%- assign socials = socials | split: "->" -%}
      {%- if socials -%}
        {%- capture socials -%}
          {%- if settings.social_instagram -%}instagram|{{settings.social_instagram}}|{{ settings.social_instagram_name }}->{%- endif -%}
          {%- if settings.social_pinterest -%}pinterest|{{settings.social_pinterest}}|{{ settings.social_pinterest_name }}->{%- endif -%}
          {%- if settings.social_youtube -%}youtube|{{settings.social_youtube}}|{{ settings.social_youtube_name }}->{%- endif -%}
          {%- if settings.social_facebook -%}facebook|{{settings.social_facebook}}|{{ settings.social_facebook_name }}->{%- endif -%}
          {%- if settings.social_twitter -%}twitter|{{settings.social_twitter}}|{{ settings.social_twitter_name }}->{%- endif -%}
          {%- if settings.social_tiktok -%}tiktok|{{settings.social_tiktok}}|{{ settings.social_tiktok_name }}->{%- endif -%}
          {%- if settings.social_tumblr -%}tumblr|{{settings.social_tumblr}}|{{ settings.social_tumblr_name }}->{%- endif -%}
          {%- if settings.social_snapchat -%}snapchat|{{settings.social_snapchat}}|{{ settings.social_snapchat_name }}{%- endif -%}
        {%- endcapture -%}
        {%- assign socials = socials | split: "->" -%}
        {%- if block.settings.title != empty -%}<h3>{{ block.settings.title }}</h3>{%- endif -%}
        {%- if socials.size > 0 -%}
          <ul class="l4sc" {{ block.shopify_attributes }}>
            {%- for social in socials -%}
              {%- assign social_info = social | split: '|' -%}
              {%- assign social_translation = 'socials.' | append: social_info[0] -%}
              <li><a aria-label="{{ social_translation | t }}" href="{{ social_info[1] }}" rel="external noopener" target="external"><i aria-hidden="true" class="icon-{{ social_info[0] }}"></i><span>{{ social_translation | t }}</span></a></li>
            {%- endfor -%}
          </ul>
        {%- else -%}
          {%- if shop.brand.metafields.social_links.size > 0 -%}
            <ul class="l4sc">
              {%- for social in shop.brand.metafields.social_links -%}
                {%- assign social_translation = 'socials.' | append: social[0] -%}
                <li><a aria-label="{{ social_translation | t }}" href="{{ social[1] }}" rel="external noopener" target="external"><i aria-hidden="true" class="icon-{{ social[0] }}"></i><span>{{ social_translation | t }}</span></a></li>
              {%- endfor -%}
            </ul>
          {%- endif -%}
        {%- endif -%}
      {%- endif -%}
  {%- endcase -%}
{%- endfor -%}
<figure id="background">
  <span class="img-overlay hidden"></span>
	<picture>
    {%- if section.settings.image -%}
      <img
        src="{{ section.settings.image | image_url: width: 1400 }}"
        srcset="{% render 'image-srcset', image: section.settings.image %}"
        sizes="
          (min-width: 760) 50vw
          100vw
        "
        width="1400"
        height="846"
        alt="{{ section.settings.image.alt | default: shop.name | escape }}"
        loading="lazy"
      >
    {%- else -%}
      {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
    {% endif %}
	</picture>
</figure>

{% schema %}
{
  "name": "t:main.password.name",
  "settings": [
    {
      "id": "image",
      "type": "image_picker",
      "label": "t:main.password.settings.image.label",
      "info": "t:main.password.settings.image.info"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "spacer",
      "name": "t:main.password.blocks.spacer.name",
      "settings": [
        {
          "id": "height",
          "type": "range",
          "label": "t:main.article.blocks.spacer.settings.height.label",
          "min": -100,
          "max": 200,
          "step": 5,
          "unit": "px",
          "default": 20
        }
      ]
    },
    {
      "type": "logo",
      "name": "t:main.password.blocks.logo.name",
      "limit": 1,
      "settings": [
        {
          "id": "logo",
          "type": "image_picker",
          "label": "t:main.password.blocks.logo.settings.logo.label"
        },
        {
          "id": "logo_width",
          "type": "range",
          "label": "t:main.password.blocks.logo.settings.logo_width.label",
          "min": 100,
          "max": 250,
          "step": 5,
          "unit": "px",
          "default": 250
        },
        {
          "id": "svg_logo",
          "type": "url",
          "label": "t:main.password.blocks.logo.settings.svg_logo.label",
          "info": "t:main.password.blocks.logo.settings.svg_logo.info"
        }
      ]
    },
    {
      "type": "social_sharing",
      "name": "t:main.password.blocks.social_sharing.name",
      "limit": 1
    },
    {
      "type": "message",
      "name": "t:main.password.blocks.message.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.password.blocks.message.settings.paragraph"
        }
      ]
    },
    {
      "type": "newsletter_form",
      "name": "t:main.password.blocks.newsletter_form.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.password.blocks.newsletter_form.settings.paragraph"
        }
      ]
    },
    {
      "type": "password_form",
      "name": "t:main.password.blocks.password_form.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:main.password.blocks.password_form.settings.paragraph"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:main.password.blocks.title.name",
      "limit": 1
    }
  ]
}
{% endschema %}
