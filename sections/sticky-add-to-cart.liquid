{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
    assign is_combined_listing = false
    if product.options_with_values.first.values.first.product_url
        assign is_combined_listing = true
    endif
-%}
{%- if section.settings.enable and is_combined_listing == false and product.variants.size < 250 -%}
    {%- unless section.settings.hide_in_theme_editor and request.design_mode and product != empty -%}
        {%- liquid
            assign product_image_ratio = settings.product_image_ratio
            case product_image_ratio
                when '310x430'
                    assign image_width =  140
                    assign image_height = 176
                when '430x310'
                    assign image_width =  140
                    assign image_height = 104
                else
                    assign image_width =  140
                    assign image_height = 140
            endcase
            assign current_variant = product.selected_or_first_available_variant
        -%}
        <form action="./" method="post" id="sticky-add-to-cart" class="palette-{{ section.settings.color_palette }} module-color-palette f8ps{% if section.settings.position == 'top' %} align-top{% endif %}{% unless section.settings.show_mobile %} mobile-hide{% endunless %} base-font{% if current_variant == null%} unavailable{% endif %}" aria-hidden="true">
            <fieldset>
                <figure class="{% if settings.fill_product_images %} cover{% endif %} {% if settings.multiply_product_images == 'multiply' %}img-multiply{% elsif settings.multiply_product_images == 'multiply-bg' %}img-multiply-bg{% endif %}">
                    <picture>
                        {% if current_variant.image %}
                            <img src="
                                {%- liquid
                                    if settings.fill_product_images
                                        echo current_variant.image | image_url: width: image_width, height: image_height, crop: 'center'
                                    else
                                        echo current_variant.image | image_url: width: image_width
                                    endif
                                -%}"
                                 alt="{{ current_variant.image.alt }}"
                                 width="{{ image_width }}"
                                 height="{{ image_height }}"
                                 loading="lazy"
                            >
                        {% elsif product.featured_media %}
                            <img src="
                                {%- liquid
                                    if settings.fill_product_images
                                        echo product.featured_media | image_url: width: image_width, height: image_height, crop: 'center'
                                    else
                                        echo product.featured_media | image_url: width: image_width
                                    endif
                                -%}"
                                 alt="{{ product.featured_media.alt }}"
                                 width="{{ image_width }}"
                                 height="{{ image_height }}"
                                 loading="lazy"
                            >
                        {% else %}
                            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
                        {% endif %}
                    </picture>
                </figure>
                <header>
                    <h2>
                        {%- if settings.show_vendor and product.vendor != "vendor-unknown" and product.vendor != shop.name -%}{%- assign show_vendor = true -%}<span class="small">{{ product.vendor }}</span>{%- endif -%}
                        {{ product.title }}
                        {% if section.settings.show_variant_picker == false and product.has_only_default_variant == false %}
                            {% if show_vendor %} - {% else %}<span class="small">{% endif %}{{ current_variant.title }}{% unless show_vendor %}</span>{% endunless %}
                        {% endif %}
                    </h2>
                </header>
                {%- if section.settings.show_variant_picker and product.has_only_default_variant == false and product.variants.size > 1 -%}
                    <p>
                        <select name="product_id_sticky" id="product_id_sticky">
                            {%- for variant in product.variants -%}
                                {%- if section.settings.show_unavailable_variants == false and variant.available == false -%}{% continue %}{%- endif -%}
                                <option
                                    value="{{ variant.id }}"
                                    {% unless variant.available %}disabled data-class="disabled"{% endunless %}
                                    {% if variant == current_variant %}selected{% endif %}
                                >{{ variant.title }}</option>
                            {%- endfor -%}
                        </select>
                    </p>
                {%- endif -%}
                <footer>
                    <div>
                        <p class="f8pr-price s1pr price form-group">
                            {%- if current_variant.compare_at_price > current_variant.price -%}
                                <span class="old-price">{{ current_variant.compare_at_price | money }}</span>&nbsp;
                            {%- endif -%}
                            {{ current_variant.price | money }}{% if section.settings.show_tax %} {% if cart.taxes_included %}{{ 'product.including_tax' | t }}{% else %}{{ 'product.excluding_tax' | t }}{% endif %}{% endif %}
                            {%- if current_variant.unit_price_measurement -%}
                                <span class="small">{{ 'product.unit_price_label' | t }}{{ current_variant.unit_price | unit_price_with_measurement: current_variant.unit_price_measurement }}</span>
                            {%- endif -%}
                        </p>
                    </div>
                    <p class="submit form-group {% unless current_variant.available %}unavailable{% endunless %}">
                       {%- if section.settings.show_amount_selection -%}
                           <span class="input-amount">
                                <label for="product_qty_sticky" class="hidden">{{ 'product.form.quantity' | t }}</label>
                                <input type="number" id="product_qty_sticky" name="product_qty_sticky" data-link="#quantity" value="{{ current_variant.quantity_rule.min }}"
                                   min="{{ current_variant.quantity_rule.min }}"
                                   {% if current_variant.inventory_management == 'shopify' and current_variant.inventory_policy == 'deny' -%}
                                       max="{{ current_variant.inventory_quantity }}"
                                   {% elsif current_variant.quantity_rule.max %}
                                       max="{{ current_variant.quantity_rule.max }}"
                                   {% endif %}
                                   {% if current_variant.quantity_rule.increment %}step="{{ current_variant.quantity_rule.increment }}"{% endif %}
                                   required>
                          </span>
                        {%- endif -%}
                        {%- liquid
                            assign button_color = section.settings.button_style | split: ' ' | first
                            assign button_style = section.settings.button_style | split: ' ' | last
                        -%}
                        {%- if current_variant == null -%}
                          <button form="{{ 'main-product-form-' | append: product.id }}" type="submit" disabled class="disabled visible overlay-unavailable-buy-button{% if button_style == 'inv' %} inv{% endif %}">{{ 'product.form.unavailable' | t }}</button>
                        {%- elsif current_variant.inventory_management != nil and current_variant.inventory_policy == 'continue' and current_variant.available and current_variant.inventory_quantity <= 0 and section.settings.preorder -%}
                            <button form="{{ 'main-product-form-' | append: product.id }}" type="submit" class="overlay-preorder{% if button_style == 'inv' %} inv{% endif %}">{{ 'product.form.pre_order' | t }}</button>
                        {%- elsif current_variant.available -%}
                            <button form="{{ 'main-product-form-' | append: product.id }}" type="submit" class="overlay-{{ button_color }}{% if button_style == 'inv' %} inv{% endif %}">{{ 'product.form.add_to_cart' | t }}</button>
                        {%- else -%}
                            <button form="{{ 'main-product-form-' | append: product.id }}" type="submit" class="disabled visible overlay-unavailable-buy-button{% if button_style == 'inv' %} inv{% endif %}">{{ 'product.form.not_in_stock' | t }}</button>
                        {%- endif -%}
                    </p>
                </footer>
            </fieldset>
        </form>
    {%- endunless -%}
{%- endif -%}

{% schema %}
{
  "name": "Sticky add to cart",
  "settings": [
    {
      "id": "enable",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.enable.label",
      "info": "t:static_sections.sticky_add_to_cart.settings.enable.info"
    },
    {
      "id": "position",
      "type": "select",
      "label": "t:static_sections.sticky_add_to_cart.settings.position.label",
      "options": [
        {
          "value": "top",
          "label": "t:static_sections.sticky_add_to_cart.settings.position.options__1.label"
        },
        {
          "value": "bottom",
          "label": "t:static_sections.sticky_add_to_cart.settings.position.options__2.label"
        }
      ],
      "default": "bottom"
    },
    {
      "type": "color_scheme",
      "id": "color_palette",
      "label": "t:global.color_palette.label",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:static_sections.sticky_add_to_cart.settings.button_style.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "buy_button plain",
          "label": "t:global.button.button_style.buy_button.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "dynamic_buy_button plain",
          "label": "t:global.button.button_style.dynamic_buy_button.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "buy_button inv",
          "label": "t:global.button.button_style.buy_button.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "dynamic_buy_button inv",
          "label": "t:global.button.button_style.dynamic_buy_button.label",
          "group": "t:global.button.button_style.group.inv"
        }
      ],
      "default": "buy_button plain"
    },
    {
      "id": "show_variant_picker",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.show_variant_picker.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_unavailable_variants",
      "label": "t:static_sections.sticky_add_to_cart.settings.show_unavailable_variants.label",
      "default": true
    },
    {
      "id": "show_tax",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.show_tax.label",
      "default": false
    },
    {
      "id": "show_amount_selection",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.show_amount_selection.label",
      "default": true
    },
    {
      "id": "preorder",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.preorder.label",
      "default": false
    },
    {
      "id": "hide_in_theme_editor",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.hide_in_theme_editor.label"
    },
    {
      "type": "header",
      "content": "t:static_sections.sticky_add_to_cart.settings.mobile.header"
    },
    {
      "id": "show_mobile",
      "type": "checkbox",
      "label": "t:static_sections.sticky_add_to_cart.settings.mobile.show_mobile.label",
      "default": true
    }
  ]
}
{% endschema %}
