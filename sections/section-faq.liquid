{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  capture title_classes
    echo 'w720 '
    if section.settings.alignment == 'center'
      echo ' text-center align-center'
    endif
  endcapture

  assign show = false
  for block in section.blocks
    if block.settings.faq_question != empty
      if block.settings.faq_answer != empty or block.settings.page != blank or block.settings.image != blank or block.settings.liquid != blank or block.settings.html != blank or block.settings.show_contact_form
        assign show = true
        break
      endif
    endif
  endfor
-%}
{% if show %}
  <article class="{% if section.settings.compact %}w940 align-center{% endif %}">
    {%- if section.settings.title != blank -%}
      <div
        class="
          {{ title_classes }} title-styling
          {% if section.settings.title_underline_style != 'none' %}
            title-underline-none
            {% if section.settings.title_underline_style contains 'accent' %}
              title-underline-accent
            {% elsif section.settings.title_underline_style contains 'gradient' %}
              title-underline-gradient
            {% endif %}
            {% if section.settings.title_underline_style contains 'secondary_font' %}
              title-underline-secondary-font
            {% endif %}
          {% endif %}
        "
      >
        {{ section.settings.title }}
      </div>
    {%- endif -%}
    <div class="accordion-a">
      {%- for block in section.blocks -%}
        {%- liquid
          assign show = false
          if block.settings.faq_question != empty
            if block.settings.faq_answer != empty or block.settings.page != blank or block.settings.image != blank or block.settings.liquid != blank or block.settings.html != blank or block.settings.show_contact_form
              assign show = true
            endif
          endif
        -%}
        {% if show %}
          <details class="block-{{ block.id }}" {{ block.shopify_attributes }}>
            <summary>
              {%- assign image_width = block.settings.header_image_width -%}
              {%- assign image_width_2 = image_width | times: 2 -%}
              {%- if block.settings.header_image or block.settings.header_image_svg -%}
                <img
                  {% if block.settings.header_image_svg %}
                    src="{{ block.settings.header_image_svg }}"
                  {% elsif block.settings.header_image %}
                    src="{{ block.settings.header_image | image_url: width: image_width }}"
                    srcset="{{ block.settings.header_image | image_url: width: image_width }} 1x,{{ block.settings.header_image | image_url: width: image_width_2 }} 2x"
                  {% endif %}
                  height="20"
                  width="{{ image_width }}"
                  style="width:{{ image_width }}px!important"
                  alt="{{ block.settings.header_image.alt | default: block.settings.faq_question | escape }}"
                  loading="lazy"
                >
              {%- elsif block.settings.icon != 'none' -%}
                {%- render 'icons', icon: block.settings.icon -%}
                <style>
                  #shopify-section-{{ section.id }} .block-{{ block.id }} svg { --secondary_bg: var(--primary_text); height: auto!important; width: {{ image_width }}px!important; }
                </style>
              {%- endif -%}
              <span>{{ block.settings.faq_question }}</span>
            </summary>
            <div>
              {%- if block.settings.faq_answer != empty -%}{{ block.settings.faq_answer }}{%- endif -%}
              {%- if block.settings.page -%}{{ block.settings.page.content }}{%- endif %}
              {%- if block.settings.image != blank -%}
                <div class="m20">
                  <img
                    src="{{ block.settings.image | image_url: width: block.settings.image_width }}"
                    srcset="{% render 'image-srcset', image: block.settings.image %}"
                    sizes="
                      (min-width: 760px) 500px
                      100vw
                    "
                    alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                    width="{{ block.settings.image_width }}"
                    height="640"
                    loading="lazy"
                  >
                </div>
              {%- endif -%}
              {%- if block.settings.liquid != empty -%}{{ block.settings.liquid }}{%- endif -%}
              {%- if block.settings.html != empty -%}{{ block.settings.html }}{%- endif -%}
              {%- if block.settings.show_contact_form -%}
                {%- liquid
                  if section.settings.compact
                    assign contactform_classes = 'f8cm f8vl wide m30'
                  else
                    assign contactform_classes = 'f8cm f8vl w940 m30'
                  endif
                  assign contactform_id = section.id | append: '-' | append: block.id
                -%}
                {%- form 'contact', id: contactform_id, class: contactform_classes -%}
                  {%- if form.errors -%}
                    <script>
                      document.addEventListener('DOMContentLoaded', function () {
                        var alertAttributes = {
                            message: "{{ 'service.contact_form.email' | t }} {{ form.errors.messages['email'] }}",
                            type: 'error',
                            origin: '{{ contactform_id }}',
                          },
                          showAlertEvent = new CustomEvent('showAlert', { detail: alertAttributes });
                        window.dispatchEvent(showAlertEvent);
                      });
                    </script>
                  {%- elsif form.posted_successfully? -%}
                    <script>
                      document.addEventListener('DOMContentLoaded', function () {
                        var alertAttributes = {
                            message: "{{ 'service.contact_form.posted_successfully' | t }}",
                            type: 'success',
                            origin: '{{ contactform_id }}',
                          },
                          showAlertEvent = new CustomEvent('showAlert', { detail: alertAttributes });
                        window.dispatchEvent(showAlertEvent);
                      });
                    </script>
                  {%- endif -%}
                  <fieldset>
                    <legend>{{ 'service.contact_form.title' | t }}</legend>
                    <p>
                      <label for="subject">{{ 'service.contact_form.subject' | t }}</label>
                      <input
                        type="text"
                        id="subject"
                        name="contact[subject]"
                        placeholder="{{ 'service.contact_form.subject' | t }}"
                      >
                    </p>
                    <div class="cols">
                      <p class="w50">
                        <label for="name">{{ 'service.contact_form.name' | t }}</label>
                        <input
                          type="text"
                          id="name"
                          name="contact[name]"
                          placeholder="{{ 'service.contact_form.name' | t }}"
                        >
                      </p>
                      <p class="w50">
                        <label for="email">
                          {{- 'service.contact_form.email' | t -}}
                          <span class="overlay-theme">*</span></label
                        >
                        <input
                          type="email"
                          id="email"
                          name="contact[email]"
                          placeholder="{{ 'service.contact_form.email' | t }}"
                          required
                        >
                      </p>
                    </div>
                    <p>
                      <label for="body">
                        {{- 'service.contact_form.message' | t -}}
                        <span class="overlay-theme">*</span></label
                      >
                      <textarea
                        id="message"
                        name="contact[body]"
                        placeholder="{{ 'service.contact_form.message' | t }}"
                        required
                      ></textarea>
                    </p>
                    <p class="submit m0">
                      <button
                        type="submit"
                        {% if settings.button_style == 'inv' %}
                          class="inv"
                        {% endif %}
                      >
                        {{ 'service.contact_form.send' | t }}
                      </button>
                    </p>
                  </fieldset>
                {% endform %}
              {%- endif -%}
            </div>
          </details>
        {% endif %}
      {%- endfor -%}
    </div>
  </article>

  <style>
    #shopify-section-{{ section.id }} { position: relative; z-index: {{ section.settings.fix_zindex }}!important; }
    #shopify-section-{{ section.id }} .accordion-a { margin-bottom: {{ section.settings.spacing_desktop }}px; }
    @media only screen and (max-width: 47.5em) {
      #shopify-section-{{ section.id }} .accordion-a { margin-bottom: {{ section.settings.spacing_mobile }}px; }
    }
  </style>
{% endif %}

{% schema %}
{
  "name": "t:sections.faq.name",
  "class": "shopify-section-faq",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "alignment",
      "label": "t:sections.faq.settings.alignment.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.faq.settings.alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.faq.settings.alignment.options__2.label"
        }
      ],
      "default": "left"
    },
    {
      "id": "compact",
      "type": "checkbox",
      "label": "t:sections.faq.settings.compact.label"
    },
    {
      "type": "header",
      "content": "t:global.typography.title.label"
    },
    {
      "type": "richtext",
      "id": "title",
      "label": "t:global.typography.title.label",
      "info": "t:global.typography.title.info",
      "default": "<h2>Collapsible content</h2>"
    },
    {
      "type": "select",
      "id": "title_underline_style",
      "label": "t:global.typography.title_underline_style.label",
      "options": [
        {
          "value": "none",
          "label": "t:global.typography.title_underline_style.none.label"
        },
        {
          "value": "secondary_font",
          "label": "t:global.typography.title_underline_style.secondary_font.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_accent",
          "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "secondary_font_gradient",
          "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
          "group": "t:global.typography.title_underline_style.group.fonts"
        },
        {
          "value": "accent",
          "label": "t:global.typography.title_underline_style.accent.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        },
        {
          "value": "gradient",
          "label": "t:global.typography.title_underline_style.gradient.label",
          "group": "t:global.typography.title_underline_style.group.colors"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 50
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 30
    },
    {
      "id": "fix_zindex",
      "type": "range",
      "label": "t:global.fix_zindex.label",
      "info": "t:global.fix_zindex.info",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "t:sections.faq.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "faq_question",
          "label": "t:sections.faq.blocks.text.settings.faq_question.label",
          "info": "t:sections.faq.blocks.text.settings.faq_question.info",
          "default": "Collapsible row"
        },
        {
          "id": "icon",
          "type": "select",
          "label": "t:sections.faq.blocks.text.settings.icon.label",
          "info": "t:sections.faq.blocks.text.settings.icon.info",
          "options": [
            {
              "value": "none",
              "label": "t:sections.faq.blocks.text.settings.icon.options__1.label"
            },
            {
              "value": "group",
              "label": "t:sections.faq.blocks.text.settings.icon.options__2.label"
            },
            {
              "value": "notification",
              "label": "t:sections.faq.blocks.text.settings.icon.options__3.label"
            },
            {
              "value": "cloud_data",
              "label": "t:sections.faq.blocks.text.settings.icon.options__4.label"
            },
            {
              "value": "verified",
              "label": "t:sections.faq.blocks.text.settings.icon.options__5.label"
            },
            {
              "value": "truck",
              "label": "t:sections.faq.blocks.text.settings.icon.options__6.label"
            },
            {
              "value": "image_placeholder",
              "label": "t:sections.faq.blocks.text.settings.icon.options__7.label"
            },
            {
              "value": "help_call",
              "label": "t:sections.faq.blocks.text.settings.icon.options__8.label"
            },
            {
              "value": "filters",
              "label": "t:sections.faq.blocks.text.settings.icon.options__9.label"
            },
            {
              "value": "shopping_bag",
              "label": "t:sections.faq.blocks.text.settings.icon.options__10.label"
            },
            {
              "value": "global_shipping",
              "label": "t:sections.faq.blocks.text.settings.icon.options__11.label"
            },
            {
              "value": "barcode",
              "label": "t:sections.faq.blocks.text.settings.icon.options__12.label"
            },
            {
              "value": "delivery_box_1",
              "label": "t:sections.faq.blocks.text.settings.icon.options__13.label"
            },
            {
              "value": "delivery_box_2",
              "label": "t:sections.faq.blocks.text.settings.icon.options__14.label"
            },
            {
              "value": "statistic",
              "label": "t:sections.faq.blocks.text.settings.icon.options__15.label"
            },
            {
              "value": "review",
              "label": "t:sections.faq.blocks.text.settings.icon.options__16.label"
            },
            {
              "value": "email",
              "label": "t:sections.faq.blocks.text.settings.icon.options__17.label"
            },
            {
              "value": "coin",
              "label": "t:sections.faq.blocks.text.settings.icon.options__18.label"
            },
            {
              "value": "24_hour_clock",
              "label": "t:sections.faq.blocks.text.settings.icon.options__19.label"
            },
            {
              "value": "question",
              "label": "t:sections.faq.blocks.text.settings.icon.options__20.label"
            },
            {
              "value": "24_7_call",
              "label": "t:sections.faq.blocks.text.settings.icon.options__21.label"
            },
            {
              "value": "speech_bubbles",
              "label": "t:sections.faq.blocks.text.settings.icon.options__22.label"
            },
            {
              "value": "coupon",
              "label": "t:sections.faq.blocks.text.settings.icon.options__23.label"
            },
            {
              "value": "mobile_payment",
              "label": "t:sections.faq.blocks.text.settings.icon.options__24.label"
            },
            {
              "value": "calculator",
              "label": "t:sections.faq.blocks.text.settings.icon.options__25.label"
            },
            {
              "value": "secure",
              "label": "t:sections.faq.blocks.text.settings.icon.options__26.label"
            }
          ],
          "default": "none"
        },
        {
          "id": "header_image",
          "type": "image_picker",
          "label": "t:sections.faq.blocks.text.settings.header_image.label",
          "info": "t:sections.faq.blocks.text.settings.header_image.info"
        },
        {
          "id": "header_image_svg",
          "type": "url",
          "label": "t:sections.faq.blocks.text.settings.header_image_svg.label",
          "info": "t:sections.faq.blocks.text.settings.header_image_svg.info"
        },
        {
          "id": "header_image_width",
          "type": "range",
          "label": "t:sections.faq.blocks.text.settings.header_image_width.label",
          "min": 20,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 25
        },
        {
          "type": "header",
          "content": "t:sections.faq.blocks.text.settings.content.header"
        },
        {
          "type": "paragraph",
          "content": "t:sections.faq.blocks.text.settings.content.paragraph"
        },
        {
          "id": "faq_answer",
          "type": "richtext",
          "label": "t:sections.faq.blocks.text.settings.content.faq_answer.label",
          "default": "<p>Row content</p>"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.faq.blocks.text.settings.content.page.label"
        },
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:sections.faq.blocks.text.settings.content.image.label"
        },
        {
          "id": "image_width",
          "type": "range",
          "label": "t:sections.faq.blocks.text.settings.content.image_width.label",
          "min": 10,
          "max": 465,
          "step": 5,
          "unit": "px",
          "default": 465
        },
        {
          "type": "header",
          "content": "t:sections.faq.blocks.text.settings.custom_code.header"
        },
        {
          "id": "liquid",
          "type": "liquid",
          "label": "t:sections.faq.blocks.text.settings.custom_code.liquid.label"
        },
        {
          "id": "html",
          "type": "html",
          "label": "t:sections.faq.blocks.text.settings.custom_code.html.label"
        },
        {
          "type": "header",
          "content": "t:sections.faq.blocks.text.settings.form.header"
        },
        {
          "id": "show_contact_form",
          "type": "checkbox",
          "label": "t:sections.faq.blocks.text.settings.form.show_contact_form.label",
          "info": "t:sections.faq.blocks.text.settings.form.show_contact_form.info"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.faq.presets.name",
      "blocks": [
        {
          "type": "text",
          "settings": {
            "faq_question": "Collapsible row"
          }
        },
        {
          "type": "text",
          "settings": {
            "faq_question": "Collapsible row"
          }
        },
        {
          "type": "text",
          "settings": {
            "faq_question": "Collapsible row"
          }
        }
      ]
    }
  ]
}
{% endschema %}
