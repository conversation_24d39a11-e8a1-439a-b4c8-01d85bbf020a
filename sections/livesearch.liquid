{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- if predictive_search.performed -%}
  <div>
    <p>{{ 'search.results_plus_term' | t: term: predictive_search.terms }} </p>
  </div>
  {%- liquid
    if settings.search_drawer_enable_suggestions
      assign all_suggestions = predictive_search.resources.queries
    endif
    if settings.search_drawer_enable_collections
      if all_suggestions
        assign all_suggestions = all_suggestions | concat: predictive_search.resources.collections
      else
        assign all_suggestions = predictive_search.resources.collections
      endif
    endif
  -%}
  {%- if all_suggestions.size > 0 and all_suggestions.first.text != predictive_search.terms -%}
    {%- if settings.search_drawer_enable_suggestions -%}<p class="strong">{{ 'search.suggestions' | t }}</p>{%- endif -%}
    <ul class="l4pl">
      {%- for suggestion in all_suggestions limit: 5 -%}
        {%- unless suggestion.text == predictive_search.terms %}
          <li><a href="{{ suggestion.url }}"{% if suggestion.text %} data-search-suggestion="{{ suggestion.text }}"{% endif %}>{{ suggestion.title | highlight: predictive_search.terms }}{{ suggestion.text | highlight: predictive_search.terms }}</a></li>
        {%- endunless -%}
      {%- endfor -%}
    </ul>
  {%- endif -%}
  {%- if settings.search_drawer_enable_products and predictive_search.resources.products.size > 0 -%}
    <ul class="l4ca compact">
      {%- for product in predictive_search.resources.products -%}
        <li class="{% if product.featured_media == blank %} no-img{% endif %}{% if settings.fill_product_images %} cover{% endif %}{% if product.compare_at_price > product.price %} has-discount{% endif %}">
          {%- if product.featured_media != blank -%}
            <figure{% if settings.multiply_product_images == 'multiply' %} class="img-multiply"{% elsif settings.multiply_product_images == 'multiply-bg' %} class="img-multiply-bg"{% endif %}>
              <picture>
                <img src="{{ product.featured_media | image_url: width: 140 }}"
                     srcset="{{ product.featured_media | image_url: width: 70 }} 1x,{{ product.featured_media | image_url: width: 140 }} 2x"
                     alt="{{ product.featured_media.alt }}"
                     width="70"
                     height="71"
                >
              </picture>
            </figure>
          {%- endif -%}
          <section>
            <div class="cols align-middle">
              <div>
                <h2>
                  {%- if settings.search_drawer_show_vendor and product.vendor != "vendor-unknown" and product.vendor != shop.name -%}
                    <span class="small">{{ product.vendor }}</span>
                  {%- endif -%}
                  <a href="{{ product.url }}">{{ product.title }} </a>
                </h2>
              </div>
              {%- if settings.search_drawer_show_price -%}
                <p class="price s1pr">
                {%- if product.compare_at_price > product.price -%}<span class="old-price">{{ product.compare_at_price | money }}</span>{%- endif -%}&nbsp;
                  {{ product.price | money }}
                </p>
              {%- endif -%}
            </div>
            {%- if product.metafields.reviews.rating and settings.show_product_rating -%}
              <p class="r6rt" data-val="{{ product.metafields.reviews.rating.value }}" data-of="5">
                {%- if product.metafields.reviews.rating_count -%}{{ product.metafields.reviews.rating_count }} {{ 'product.reviews.count' | t: count: product.metafields.reviews.rating_count | downcase }}{%- endif -%}
              </p>
            {%- endif -%}
          </section>
        </li>
      {%- endfor -%}
    </ul>
  {%- endif -%}
  <p><a href="{{ routes.search_url }}?q={{ predictive_search.terms }}" class="strong inline"><span class="link-underline">{{ 'search.view_all_results' | t }}</span></a></p>
  {%- liquid
    assign all_articles = ''
    if settings.search_drawer_enable_pages
      assign all_articles = predictive_search.resources.pages
    endif
    if settings.search_drawer_enable_articles
      assign all_articles = all_articles | concat: predictive_search.resources.articles
    endif
  -%}
  {%- if all_articles.size > 0 -%}
    <p class="strong">{{ 'search.questions_and_advice' | t }}</p>
    <ul class="l4pl">
      {%- for article in all_articles limit: 5 -%}
        <li><a href="{{ article.url }}">{{ article.title | highlight: predictive_search.terms }}</a></li>
      {%- endfor -%}
    </ul>
  {%- endif -%}
{%- endif -%}
