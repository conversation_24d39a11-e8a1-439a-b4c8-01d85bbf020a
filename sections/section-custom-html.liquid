{{ section.settings.custom_html }}

<style>
  #shopify-section-{{ section.id }} { position: relative; z-index: {{ section.settings.fix_zindex }}!important; }
  #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_desktop }}px; }
  @media only screen and (max-width: 47.5em) {
      #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_mobile }}px; }
  }
</style>

{% schema %}
{
  "name": "t:sections.custom_html_snippet.name",
  "disabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "html",
      "id": "custom_html_snippet",
      "label": "t:sections.custom_html_snippet.settings.custom_html_snippet.label",
      "info": "t:sections.custom_html_snippet.settings.custom_html_snippet.info"
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 50
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 30
    },
    {
      "id": "fix_zindex",
      "type": "range",
      "label": "t:global.fix_zindex.label",
      "info": "t:global.fix_zindex.info",
      "min": 0,
      "max": 25,
      "step": 1,
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:sections.custom_html_snippet.presets.name"
    }
  ]
}
{% endschema %}
