<!-- Upstep Dashboard Container -->
<div class="upstep-dashboard-container">

  <!-- Welcome Section -->
  <div class="upstep-welcome-section">
    <h1 class="upstep-heading-large">{{ 'customer.account.welcome' | t: first_name: customer.first_name }}</h1>
    <p class="upstep-nav-text" style="text-align: center; margin-top: 16px;">{{ 'customer.account.subtitle_html' | t }}</p>
  </div>
  <!-- Main Dashboard Content -->
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 40px;">

    <!-- Account Details Card -->
    <div class="upstep-card">
      <h2 class="upstep-text-black-bold" style="text-align: left; margin-bottom: 20px;">{{ 'customer.account.details' | t }}</h2>
      <div class="upstep-card-content">
        <div style="margin-bottom: 12px;">
          <span class="upstep-card-text" style="font-weight: 600;">{{ 'customer.name' | t }}:</span>
          <span class="upstep-card-text">{{ customer.name }}</span>
        </div>
        {%- if customer.phone != blank -%}
        <div style="margin-bottom: 12px;">
          <span class="upstep-card-text" style="font-weight: 600;">{{ 'customer.phone_number' | t }}:</span>
          <span class="upstep-card-text">{{ customer.phone }}</span>
        </div>
        {%- endif -%}
        <div style="margin-bottom: 12px;">
          <span class="upstep-card-text" style="font-weight: 600;">{{ 'customer.email' | t }}:</span>
          <span class="upstep-card-text">{{ customer.email }}</span>
        </div>
        {%- if customer.default_address -%}
        <div style="margin-bottom: 12px;">
          <span class="upstep-card-text" style="font-weight: 600;">{{ 'customer.account.address' | t }}:</span>
          <div class="upstep-card-text">
            {% if customer.default_address.company != empty %}{{ customer.default_address.company }}<br>{% endif %}
            {{ customer.default_address.street }}<br>
            {{ customer.default_address.zip }} {{ customer.default_address.city }}<br>
            {{ customer.default_address.country }}
          </div>
        </div>
        {%- endif -%}
      </div>
      <a href="{{ routes.account_addresses_url }}" class="upstep-btn-primary" style="display: inline-block; padding: 12px 24px; text-decoration: none; margin-top: 16px;">
        {{ 'customer.account.manage_addresses' | t }}
      </a>
      {% assign app_blocks = section.blocks | where: "type", "@app" %}
      {% for app_block in app_blocks %}
        {% render app_block %}
      {% endfor %}
    </div>

    <!-- Customer Service Card (conditionally shown) -->
    {%- if section.settings.text != empty or section.settings.show_phone_link or section.settings.show_mail_link or section.settings.show_whatsapp_link -%}
    <div class="upstep-card">
      <h2 class="upstep-text-black-bold" style="text-align: left; margin-bottom: 20px;">{{ 'customer.account.customer_service' | t }}</h2>
      <div class="upstep-card-content">
        {{ section.settings.text }}
        {%- if section.settings.show_phone_link or section.settings.show_mail_link or section.settings.show_whatsapp_link -%}
          <div style="margin-top: 16px;">
            {% if shop.phone != empty and section.settings.show_phone_link %}
              <div style="margin-bottom: 8px;">
                <a href="tel:{{ shop.phone }}" class="upstep-primary-text" style="text-decoration: none;">
                  <i aria-hidden="true" class="icon-phone"></i> {{ shop.phone }}
                </a>
              </div>
            {% endif %}
            {% if shop.email != empty and section.settings.show_mail_link %}
              <div style="margin-bottom: 8px;">
                <a href="mailto:{{ shop.email }}" class="upstep-primary-text" style="text-decoration: none;">
                  <i aria-hidden="true" class="icon-envelope"></i> {{ shop.email }}
                </a>
              </div>
            {% endif %}
            {% if settings.whatsapp != empty and settings.whatsapp != 0 and section.settings.show_whatsapp_link %}
              <div style="margin-bottom: 8px;">
                <a href="https://wa.me/{{ settings.whatsapp }}" class="upstep-primary-text" style="text-decoration: none;">
                  <i aria-hidden="true" class="icon-whatsapp"></i> {{ 'footer.whatsapp_html' | t }}
                </a>
              </div>
            {% endif %}
          </div>
        {%- endif -%}
      </div>
    </div>
    {%- endif -%}
  </div>

  <!-- Orders Section -->
  <div class="upstep-card" style="margin-bottom: 40px;">
    <h2 class="upstep-text-black-bold" style="text-align: left; margin-bottom: 24px;">{{ 'customer.account.orders_title' | t }}</h2>

    {%- if customer.orders == blank -%}
      <div class="upstep-card-light" style="text-align: center; padding: 40px;">
        <p class="upstep-card-text">{{ 'customer.account.no_orders_content' | t }}</p>
        <a href="{{ routes.all_products_collection_url }}" class="upstep-btn-primary" style="display: inline-block; padding: 12px 24px; text-decoration: none; margin-top: 16px;">
          Start Shopping
        </a>
      </div>
    {%- else -%}
      {%- paginate customer.orders by 12 -%}
        <div class="upstep-orders-grid" style="display: grid; gap: 16px;">
          {%- for order in customer.orders -%}
            <div class="upstep-card-light" style="padding: 20px;">
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 16px; align-items: center;">

                <!-- Order Number -->
                <div>
                  <span class="upstep-card-text" style="font-size: 14px; color: #464444;">{{ 'customer.orders.order_number' | t }}</span>
                  <div class="upstep-text-blue-bold" style="text-align: left;">{{ order.name }}</div>
                </div>

                <!-- Date -->
                <div>
                  <span class="upstep-card-text" style="font-size: 14px; color: #464444;">{{ 'customer.orders.date' | t }}</span>
                  <div class="upstep-card-text" style="font-weight: 600;">{{ order.created_at | date: format: 'month_day_year_basic' }}</div>
                </div>

                <!-- Status & Total -->
                <div>
                  <span class="upstep-card-text" style="font-size: 14px; color: #464444;">{{ 'customer.orders.total' | t }}</span>
                  <div class="upstep-card-text" style="font-weight: 600;">{{ order.total_price | money }}</div>
                  <div style="margin-top: 4px;">
                    {% if order.financial_status == 'paid' %}
                      <span style="color: #00AEF8; font-size: 12px; font-weight: 600;">
                        <i aria-hidden="true" class="icon-check"></i> {{ order.financial_status_label }}
                      </span>
                    {% else %}
                      <span style="color: #464444; font-size: 12px;">{{ order.financial_status_label }}</span>
                    {% endif %}
                  </div>
                </div>

                <!-- View Order Button -->
                <div>
                  <a href="{{ order.customer_url }}" class="upstep-btn-quiz" style="padding: 8px 16px; font-size: 14px; text-decoration: none;">
                    {{ 'customer.orders.view_order' | t }}
                  </a>
                </div>

              </div>
            </div>
          {%- endfor -%}
        </div>

        {%- render 'pagination',
          paginate: paginate,
          show_amount: true
        -%}
      {%- endpaginate -%}
    {%- endif -%}
  </div>

</div> <!-- Close upstep-dashboard-container -->

{% schema %}
{
  "name": "t:main.account_dashboard.name",
  "settings": [
    {
      "type": "header",
      "content": "t:main.account_dashboard.settings.banner.header"
    },
    {
      "id": "image",
      "type": "image_picker",
      "label": "t:main.account_dashboard.settings.banner.image.label"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "t:main.account_dashboard.settings.banner.overlay_opacity.label",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "default": 25
    },
    {
      "type": "color_scheme",
      "id": "color_palette",
      "label": "t:global.color_palette.label",
      "default": "scheme-4"
    },
    {
      "id": "text_position",
      "type": "select",
      "label": "t:main.account_dashboard.settings.banner.text_position.label",
      "options": [
        {
          "value": "text-start",
          "label": "t:main.account_dashboard.settings.banner.text_position.options__1.label"
        },
        {
          "value": "text-center",
          "label": "t:main.account_dashboard.settings.banner.text_position.options__2.label"
        },
        {
          "value": "text-end",
          "label": "t:main.account_dashboard.settings.banner.text_position.options__3.label"
        }
      ],
      "default": "text-start"
    },
    {
      "type": "header",
      "content": "t:main.account_dashboard.settings.customer_service.header"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:main.account_dashboard.settings.customer_service.text.label",
      "default": "<p>Extra shop info</p>"
    },
    {
      "type": "checkbox",
      "id": "show_phone_link",
      "label": "t:main.account_dashboard.settings.customer_service.show_phone_link.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mail_link",
      "label": "t:main.account_dashboard.settings.customer_service.show_mail_link.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_whatsapp_link",
      "label": "t:main.account_dashboard.settings.customer_service.show_whatsapp_link.label",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
