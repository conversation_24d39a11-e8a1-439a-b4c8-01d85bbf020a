{%- paginate blog.articles by section.settings.pagination_qty -%}
  <article class="m0">
    <header class="cols">
      <{{ section.settings.title_size }}>
        {%- if section.settings.title != empty -%}
          {{ section.settings.title }}
        {%- else -%}
          {{ blog.title }}
        {%- endif -%}
      </{{ section.settings.title_size }}>
    </header>
    {% if section.settings.show_tags and blog.tags.size > 0 %}
      <p class="link-btn mobile-compact">
        {%- for tag in blog.all_tags -%}
          {%- if current_tags contains tag -%}
            {%- liquid
             if settings.button_style == 'inv'
               assign active_button_class = 'plain'
             else
               assign active_button_class = 'inv'
             endif
             assign replacement = '<a class="' | append: active_button_class | append: '" '
            -%}
            {{ tag | link_to_remove_tag: tag | replace: '<a ', replacement }}
          {%- else -%}
            {{ tag | link_to_tag: tag }}
          {%- endif -%}
        {%- endfor -%}
      </p>
    {% endif %}
    <ul class="l4ne landscape wide">
      {%- for article in blog.articles -%}
        {%- liquid
          if section.index > 2 or forloop.first == false
            assign lazyload = true
          else
            assign lazyload = false
          endif
        -%}
        {%- render 'article-item',
          article: article, show_excerpt: section.settings.show_excerpt, show_image: section.settings.show_image, show_date: section.settings.show_date, show_author: section.settings.show_author, button_style: section.settings.button_style_post, lazyload: lazyload, blog_title_size: section.settings.blog_title_size
        -%}
      {%- endfor -%}
    </ul>
  </article>
  {% render 'pagination',
    paginate: paginate,
    show_amount: true
  %}
{%- endpaginate -%}

{% schema %}
{
  "name": "t:main.blog.name",
  "settings": [
    {
      "type": "select",
      "id": "title_size",
      "label": "t:global.typography.title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h1"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:main.blog.settings.title.label",
      "info": "t:main.blog.settings.title.info"
    },
    {
      "type": "checkbox",
      "id": "show_tags",
      "label": "t:main.blog.settings.show_tags.label"
    },
    {
      "type": "range",
      "id": "pagination_qty",
      "label": "t:main.blog.settings.pagination_qty.label",
      "min": 3,
      "max": 15,
      "step": 3,
      "default": 12
    },
    {
      "type": "header",
      "content": "t:main.blog.settings.blog_posts.header"
    },
    {
      "type": "select",
      "id": "blog_title_size",
      "label": "t:global.typography.blog_title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h4"
    },
    {
      "id": "show_image",
      "type": "checkbox",
      "label": "t:main.blog.settings.blog_posts.show_image.label",
      "default": true
    },
    {
      "id": "show_date",
      "type": "checkbox",
      "label": "t:main.blog.settings.blog_posts.show_date.label",
      "default": true
    },
    {
      "id": "show_author",
      "type": "checkbox",
      "label": "t:main.blog.settings.blog_posts.show_author.label",
      "default": true
    },
    {
      "id": "show_excerpt",
      "type": "checkbox",
      "label": "t:main.blog.settings.blog_posts.show_excerpt.label"
    },
    {
      "type": "select",
      "id": "button_style_post",
      "label": "t:main.blog.settings.blog_posts.button_style_post.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "primary link",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "secondary link",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.link"
        },
        {
          "value": "tertiary link",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.link"
        }
      ],
      "default": "secondary link"
    }
  ]
}
{% endschema %}
