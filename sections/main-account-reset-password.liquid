<div class="w630 align-center">
    <{{ settings.global_title_size }} class="m30 text-start">{{ 'customer.reset_password.title' | t }}</{{ settings.global_title_size }}>
    {%- assign reset_password_form_classes = 'f8lg f8vl text-start' %}
    {%- form 'reset_customer_password', class: reset_password_form_classes -%}
    {%- if form.errors -%}
    {%- for error in form.errors -%}
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var alertAttributes = { message: "{%- if error == 'form' -%}{{ form.errors.messages[error] }}{%- else -%}{{ form.errors.translated_fields[error] | capitalize }} {{ form.errors.messages[error] }}{%- endif -%}", type: "error", origin: "recover_password" },
                    showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                window.dispatchEvent(showAlertEvent);
            });
        </script>
    {%- endfor -%}
    {%- endif -%}
        <fieldset>
            <legend>{{ 'customer.reset_password.title' | t }}</legend>
            <div class="cols">
                <p class="w50">
                    <label for="password">{{ 'customer.reset_password.new_password' | t }}<span class="overlay-theme">*</span> <a href="./" class="show"><span>{{ 'general.accessibility.show' | t }}</span> <span class="hidden">{{ 'general.accessibility.hide' | t }}</span></a></label>
                    <input type="password" id="password" name="customer[password]" placeholder="{{ 'customer.reset_password.new_password' | t }}" required>
                </p>
                <p class="w50">
                    <label for="password_repeat">{{ 'customer.reset_password.repeat_new_password' | t }}<span class="overlay-theme">*</span> <a href="./" class="show"><span>{{ 'general.accessibility.show' | t }}</span> <span class="hidden">{{ 'general.accessibility.hide' | t }}</span></a></label>
                    <input type="password" id="password_repeat" name="customer[password_confirmation]" placeholder="{{ 'customer.reset_password.repeat_new_password' | t }}" required data-match="#password" data-match-error="{{ 'customer.register.passwords_dont_match' | t }}">
                </p>
            </div>
            <p class="submit">
                <button type="submit"{% if settings.button_style == 'inv' %} class="inv"{% endif %}>{{ 'customer.reset_password.submit' | t }}</button>
            </p>
        </fieldset>
    {%- endform -%}
</div>
