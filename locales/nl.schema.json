/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "global": {
    "fix_zindex": {
      "label": "Control the stacking order of sections",
      "info": "Increase or decrease the z-index value to control the visual layering of sections, ensuring the desired display arrangement when using negative spacing. [Read more](https://intercom.help/someoneyouknow/en/articles/8310188-z-index-settings)"
    },
    "mobile": {
      "header": "Mobiele lay-out"
    },
    "alignment": {
      "left": {
        "label": "Links"
      },
      "center": {
        "label": "Midden"
      },
      "right": {
        "label": "Rechts"
      },
      "divide_evenly": {
        "label": "Verdeel gelijkmatig"
      },
      "top_left": {
        "label": "Linksboven"
      },
      "top_center": {
        "label": "Midden boven"
      },
      "top_right": {
        "label": "Rechtsboven"
      },
      "center_left": {
        "label": "Midden links"
      },
      "center_center": {
        "label": "Midden gecentreerd"
      },
      "center_right": {
        "label": "Midden rechts"
      },
      "bottom_left": {
        "label": "Linksonder"
      },
      "bottom_center": {
        "label": "Middenonder"
      },
      "bottom_right": {
        "label": "Rechts onder"
      }
    },
    "media_position": {
      "label": "Positie op desktop",
      "desktop": {
        "label": "Positie op desktop",
        "options__1": {
          "label": "Voor de inhoud"
        },
        "options__2": {
          "label": "Na de inhoud"
        }
      },
      "mobile": {
        "label": "Positie op mobiel",
        "options__1": {
          "label": "Voor de inhoud"
        },
        "options__2": {
          "label": "Na de inhoud"
        }
      }
    },
    "shop_the_look": {
      "label": "Shop the look",
      "show_look": {
        "label": "Schakel de functie 'shop the look' in"
      },
      "product_list": {
        "label": "Producten"
      },
      "look_btn_style": {
        "label": "Toevoegen aan winkelwagen knop stijl"
      },
      "look_position": {
        "label": "Positie",
        "info": "Dit is de positie van de knop ‘Toevoegen aan winkelwagen’ die in de banner zal verschijnen.",
        "options__1": {
          "label": "Links"
        },
        "options__2": {
          "label": "Rechts"
        }
      },
      "label_header": {
        "header": "Label"
      },
      "show_look_label": {
        "label": "Toon label"
      },
      "look_label_text": {
        "label": "Label tekst"
      }
    },
    "typography": {
      "title_weight": {
        "label": "Heading font weight"
      },
      "title": {
        "label": "Heading",
        "info": "To add an underline, select the text and press Ctrl + U for Windows and Linux or Cmd + U for Mac."
      },
      "title_underline_style": {
        "label": "Underlined heading style",
        "group": {
          "fonts": "Fonts",
          "colors": "Colors"
        },
        "none": {
          "label": "None"
        },
        "secondary_font": {
          "label": "Secondary font"
        },
        "secondary_font_accent": {
          "label": "Secondary font in accent color"
        },
        "secondary_font_gradient": {
          "label": "Secondary font in accent gradient color"
        },
        "accent": {
          "label": "Accent color"
        },
        "gradient": {
          "label": "Accent gradient color"
        }
      },
      "title_size": {
        "label": "Heading font size",
        "h6": {
          "label": "Heading 6"
        },
        "h5": {
          "label": "Heading 5"
        },
        "h4": {
          "label": "Heading 4"
        },
        "h3": {
          "label": "Heading 3"
        },
        "h2": {
          "label": "Heading 2"
        },
        "h1": {
          "label": "Heading 1"
        }
      },
      "blog_title_size": {
        "label": "Blog berichten titelgrootte"
      },
      "text": {
        "header": "Text",
        "label": "Content"
      },
      "section_text": {
        "header": "Text"
      },
      "title_font": {
        "label": "Heading font",
        "primary": {
          "label": "Primary font"
        },
        "secondary": {
          "label": "Secondary font"
        }
      },
      "font_size": {
        "label": "Text font size",
        "13px": {
          "label": "Small"
        },
        "14px": {
          "label": "Medium"
        },
        "16px": {
          "label": "Large"
        },
        "18px": {
          "label": "Extra large"
        }
      },
      "font_weight": {
        "label": "Text font weight",
        "100": {
          "label": "Thin"
        },
        "200": {
          "label": "Extra light"
        },
        "300": {
          "label": "Light"
        },
        "400": {
          "label": "Regular"
        },
        "500": {
          "label": "Medium"
        },
        "600": {
          "label": "Semi bold"
        },
        "700": {
          "label": "Bold"
        },
        "800": {
          "label": "Extra bold"
        },
        "900": {
          "label": "Black"
        }
      },
      "text_position": {
        "label": "Content position",
        "top_left": {
          "label": "Top left"
        },
        "top_center": {
          "label": "Top center"
        },
        "top_right": {
          "label": "Top right"
        },
        "center_left": {
          "label": "Center left"
        },
        "center_center": {
          "label": "Center center"
        },
        "center_right": {
          "label": "Center right"
        },
        "bottom_left": {
          "label": "Bottom left"
        },
        "bottom_center": {
          "label": "Bottom center"
        },
        "bottom_right": {
          "label": "Bottom right"
        }
      }
    },
    "color_palette": {
      "label": "Kleurenschema"
    },
    "buttons": {
      "header": "Knoppen"
    },
    "button": {
      "header": "Knop",
      "show_link": {
        "label": "Toon knop"
      },
      "link_text": {
        "label": "Label",
        "info": "Laat leeg om de knop te verbergen. Alleen van toepassing wanneer de instelling 'klikbare afbeeldingen' is ingeschakeld in thema-instellingen - toegankelijkheid."
      },
      "link_url": {
        "label": "Link"
      },
      "button_style": {
        "label": "Stijl",
        "group": {
          "plain": "Gevuld",
          "inv": "Niet gevuld",
          "link": "Link"
        },
        "primary": {
          "label": "Primair"
        },
        "secondary": {
          "label": "Secundair"
        },
        "tertiary": {
          "label": "Tertiair"
        },
        "positive": {
          "label": "Succes"
        },
        "buy_button": {
          "label": "Koopknop"
        },
        "dynamic_buy_button": {
          "label": "Dynamische koopknop"
        }
      }
    },
    "overlay": {
      "header": "Overlay link",
      "show_overlay_link": {
        "label": "Schakel overlay link in"
      },
      "overlay_url": {
        "label": "Link"
      }
    },
    "layout": {
      "label": "Lay-out",
      "width": {
        "label": "Breedte",
        "label_banner": "Bannerbreedte",
        "options__1": {
          "label": "Normale breedte"
        },
        "options__2": {
          "label": "Volledige breedte"
        }
      },
      "height": {
        "label": "Hoogte",
        "label_banner": "Banner hoogte",
        "options__1": {
          "label": "Extra klein"
        },
        "options__2": {
          "label": "Klein"
        },
        "options__3": {
          "label": "Gemiddeld"
        },
        "options__4": {
          "label": "Groot"
        },
        "options__5": {
          "label": "Extra groot"
        },
        "16_9": {
          "label": "16/9"
        },
        "1_1": {
          "label": "1/1"
        },
        "4_5": {
          "label": "4/5"
        },
        "32_9": {
          "label": "32/9"
        },
        "21_9": {
          "label": "21/9"
        },
        "adapt": {
          "label": "Aanpassen aan afbeelding"
        },
        "adapt_first": {
          "label": "Aanpassen aan eerste afbeelding"
        },
        "full": {
          "label": "Volledige hoogte"
        }
      },
      "enable_custom_height": {
        "label": "Schakel aangepaste minimale hoogte in"
      },
      "custom_height": {
        "label": "Aangepaste hoogte"
      }
    },
    "icon": {
      "label": "Icon",
      "info": "Icoon gebruikt primaire knop voor accenten",
      "options__default_check": {
        "label": "Check (groen)"
      },
      "options__1": {
        "label": "Geen icoon"
      },
      "options__2": {
        "label": "Groep"
      },
      "options__3": {
        "label": "Notificatie"
      },
      "options__4": {
        "label": "Cloud data"
      },
      "options__5": {
        "label": "Geverifieerd"
      },
      "options__6": {
        "label": "Vrachtwagen"
      },
      "options__7": {
        "label": "Afbeelding"
      },
      "options__8": {
        "label": "Telefoongesprek"
      },
      "options__9": {
        "label": "Filters"
      },
      "options__10": {
        "label": "Winkeltas"
      },
      "options__11": {
        "label": "Wereldwijde verzending"
      },
      "options__12": {
        "label": "Barcode"
      },
      "options__13": {
        "label": "Doos"
      },
      "options__14": {
        "label": "Levering doos"
      },
      "options__15": {
        "label": "Statistiek"
      },
      "options__16": {
        "label": "Review"
      },
      "options__17": {
        "label": "E-mail"
      },
      "options__18": {
        "label": "Munt"
      },
      "options__19": {
        "label": "24-uurs klok"
      },
      "options__20": {
        "label": "Vraag"
      },
      "options__21": {
        "label": "24/7 bereikbaar"
      },
      "options__22": {
        "label": "Tekstballonnen"
      },
      "options__23": {
        "label": "Coupon"
      },
      "options__24": {
        "label": "Mobiele betaling"
      },
      "options__25": {
        "label": "Calculator"
      },
      "options__26": {
        "label": "Veilig"
      }
    },
    "spacing": {
      "header": "Marge",
      "spacing_desktop": {
        "label": "Marge onder desktop"
      },
      "spacing_mobile": {
        "label": "Marge onder mobiel"
      }
    },
    "padding": {
      "header": "Padding",
      "mobile": "Padding mobiel",
      "padding_top": {
        "label": "Boven"
      },
      "padding_bottom": {
        "label": "Onder"
      },
      "padding_left": {
        "label": "Links"
      },
      "padding_right": {
        "label": "Rechts"
      }
    }
  },
  "settings_schema": {
    "accessibility": {
      "name": "Toegankelijkheid",
      "settings": {
        "favicon": {
          "label": "Favicon",
          "info": "32 x 32px .png aanbevolen [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208028-accessibility)"
        },
        "accessibility": {
          "header": "Accessibility",
          "show_accessibility": {
            "label": "Toon toegankelijkheidsmodus schakelaar in header",
            "info": "Schakel deze schakelaar in om meer contrast en grotere lettertypen aan je bezoekers te tonen."
          },
          "enable_accessibility_default": {
            "label": "Toegankelijkheidsmodus standaard inschakelen",
            "info": "Hierdoor wordt de schakelaar in de header verborgen en wordt de toegankelijkheidsmodus altijd weergegeven."
          },
          "rtl": {
            "label": "RTL-tekstrichting inschakelen",
            "info": "Arabisch, Hebreeuws en Perzisch zijn de meest voorkomende RTL-schrijfsystemen in de moderne tijd. Online winkels in RTL-taal worden aanbevolen om deze instelling in te schakelen. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208028-accessibility)"
          },
          "back_to_top_button": {
            "label": "'Terug naar boven' knop inschakelen"
          }
        }
      }
    },
    "logos": {
      "name": "Logo's",
      "settings": {
        "logo": {
          "label": "Logo afbeelding"
        },
        "logo_width": {
          "label": "Logo breedte",
          "info": "Om de headerlay-out zo geoptimaliseerd mogelijk te houden, heeft het logo een maximale hoogte van 130px."
        },
        "mobile": {
          "header": "Mobiele logo afbeelding",
          "mobile_logo": {
            "label": "Mobiele logo afbeelding",
            "info": "Laat leeg als je hetzelfde logo wilt gebruiken zoals geüpload voor desktop. Het logo is altijd gecentreerd op mobiel."
          },
          "logo_width_mobile": {
            "label": "Breedte mobiel logo",
            "info": "Om de headerlay-out zo geoptimaliseerd mogelijk te houden, heeft het logo een maximale hoogte van 88px."
          }
        }
      }
    },
    "layout": {
      "name": "Lay-out",
      "settings": {
        "width": {
          "label": "Content maximale breedte",
          "info": "Wanneer je 2000px kiest, zal de inhoud de volledige breedte in beslag nemen."
        }
      }
    },
    "colors": {
      "name": "Kleuren",
      "label": "Kleurenschema",
      "settings": {
        "color_scheme": {
          "background": {
            "header": "Achtergrond",
            "primary_bg": {
              "label": "Primaire achtergrond"
            },
            "primary_bg_gradient": {
              "label": "Primaire achtergrond kleurverloop",
              "info": "Primaire achtergrond kleurverloop vervangt primaire achtergrond waar mogelijk."
            },
            "secondary_bg": {
              "label": "Secundaire achtergrond"
            }
          },
          "typography": {
            "header": "Typografie",
            "title_color": {
              "label": "Titels"
            },
            "title_color_gradient": {
              "label": "Titels kleurverloop",
              "info": "Titels kleurverloop vervangt titelkleur waar mogelijk."
            },
            "primary_fg": {
              "label": "Text"
            }
          },
          "primary_button": {
            "header": "Primaire knop",
            "primary_button_bg": {
              "label": "Achtergrond"
            },
            "primary_button_fg": {
              "label": "Label"
            }
          },
          "secondary_button": {
            "header": "Secundaire knop",
            "secondary_button_bg": {
              "label": "Achtergrond"
            },
            "secondary_button_fg": {
              "label": "Label"
            }
          },
          "tertiary_button": {
            "header": "Tertiaire knop",
            "tertiary_button_bg": {
              "label": "Achtergrond"
            },
            "tertiary_button_fg": {
              "label": "Label"
            }
          },
          "inputs": {
            "header": "Invoervelden",
            "input_bg": {
              "label": "Achtergrond"
            },
            "input_fg": {
              "label": "Tekst"
            }
          },
          "general": {
            "header": "Algemeen",
            "primary_bd": {
              "label": "Randen"
            },
            "accent": {
              "label": "Accent"
            },
            "accent_gradient": {
              "label": "Accent kleurverloop",
              "info": "Accent kleurverloop vervangt accentkleur waar mogelijk."
            }
          }
        },
        "default_color_scheme": {
          "label": "Standaard kleurenschema",
          "info": "Dit kleurenschema wordt gebruikt voor alle secties en pagina's die geen specifiek kleurenschema hebben."
        },
        "general": {
          "header": "General",
          "positive_vibes": {
            "label": "Succes"
          },
          "negative_vibes": {
            "label": "Kritisch"
          }
        },
        "buttons": {
          "header": "Knoppen",
          "buy_button_color": {
            "label": "Koopknop"
          },
          "buy_button_text_color": {
            "label": "Koopknop label"
          },
          "dynamic_buy_button_color": {
            "label": "Dynamische koopknop"
          },
          "dynamic_buy_button_text_color": {
            "label": "Dynamische koopknop label"
          },
          "preorder_button_color": {
            "label": "Pre-order knop"
          },
          "preorder_button_text_color": {
            "label": "Pre-order knop label"
          },
          "unavailable_button_color": {
            "label": "Niet op voorraad knop"
          },
          "unavailable_button_text_color": {
            "label": "Niet op voorraad knop label"
          },
          "checkout_button_color_palette": {
            "label": "Checkout knop stijl"
          }
        },
        "dropdown_color": {
          "label": "Menu's en drawers",
          "info": "Dit is het kleurenschema van je megamenu of dropdown menu. Mobiel menu, zoekresultaten drawer, sidecart, filters, shop the look drawer, quickshop cart, overige drawers en dropdowns gebruiken automatisch dit kleurenschema."
        },
        "border_color": {
          "label": "Lijn kleur"
        },
        "product_prices": {
          "header": "Productprijzen",
          "price_color": {
            "label": "Prijs"
          },
          "compare_at_price_color": {
            "label": "Afprijzing"
          }
        },
        "product_label": {
          "header": "Productlabel",
          "paragraph": "Klik [hier](https://intercom.help/someoneyouknow/en/articles/8789852-products) voor informatie over het toevoegen van labels.",
          "product_label_color": {
            "label": "Achtergrond"
          },
          "product_label_text_color": {
            "label": "Label tekst"
          },
          "sale_label_color": {
            "label": "Sale label achtergrond"
          },
          "sale_label_text_color": {
            "label": "Sale label tekst"
          }
        }
      }
    },
    "typography": {
      "name": "Typografie",
      "settings": {
        "fonts": {
          "header": "Lettertypes",
          "primary_font": {
            "label": "Primair lettertype"
          },
          "secondary_font": {
            "label": "Secundair lettertype"
          },
          "paragraph": "Niet alle lettertypes ondersteunen meerdere letterdiktes."
        },
        "custom_fonts": {
          "header": "Aangepaste lettertypes",
          "paragraph": "Aangepaste lettertypes kunnen op twee manieren worden toegevoegd: door een lettertype bestand te uploaden of door een Adobe lettertype snippet te gebruiken. [Meer info](https://intercom.help/someoneyouknow/en/articles/8817864-typography-and-custom-fonts)",
          "enable_custom_primary_font": {
            "label": "Schakel aangepast primair lettertype in"
          },
          "custom_primary_font_name": {
            "label": "Aangepast primair lettertype naam"
          },
          "custom_primary_font_file": {
            "label": "Aangepast primair lettertype bestand",
            "info": "Upload je lettertype bestand in de 'Bestanden' sectie van je Shopify admin."
          },
          "custom_primary_font_snippet": {
            "label": "Aangepast primair lettertype snippet",
            "info": "De snippet ziet er zo uit: <link rel=\"stylesheet\" href=\"https://use.typekit.net/abc123.css\">"
          },
          "enable_custom_secondary_font": {
            "label": "Schakel aangepast secundair lettertype in"
          },
          "custom_secondary_font_name": {
            "label": "Aangepast secundair lettertype naam"
          },
          "custom_secondary_font_snippet": {
            "label": "Aangepast secundair lettertype snippet",
            "info": "De snippet ziet er zo uit: <link rel=\"stylesheet\" href=\"https://use.typekit.net/abc123.css\">"
          }
        },
        "headings": {
          "header": "Titels",
          "heading_line_height": {
            "label": "Titels line height"
          },
          "primary_case": {
            "label": "Primair lettertype letter",
            "options__1": {
              "label": "Standaard"
            },
            "options__2": {
              "label": "HOOFDLETTERS"
            }
          },
          "primary_letter_spacing": {
            "label": "Primair lettertype letter spacing"
          },
          "secondary_case": {
            "label": "Secundair lettertype letter",
            "options__1": {
              "label": "Standaard"
            },
            "options__2": {
              "label": "HOOFDLETTERS"
            }
          },
          "secondary_letter_spacing": {
            "label": "Secundair lettertype letter spacing"
          },
          "global_title_size": {
            "label": "Standaard titel lettergrootte",
            "info": "Van toepassing op statische pagina's zoals de account login en register pagina's."
          }
        },
        "body": {
          "header": "Body",
          "body_font": {
            "label": "Tekst lettertype",
            "options__1": {
              "label": "Primair lettertype"
            },
            "options__2": {
              "label": "Secundair lettertype"
            }
          },
          "body_font_size": {
            "label": "Tekst lettergrootte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            },
            "options__4": {
              "label": "Extra groot"
            }
          },
          "body_case": {
            "label": "Tekst lettertype letter",
            "options__1": {
              "label": "Standaard"
            },
            "options__2": {
              "label": "HOOFDLETTERS"
            }
          },
          "body_letter_spacing": {
            "label": "Letter spacing"
          },
          "body_line_height": {
            "label": "Line height"
          }
        },
        "prices": {
          "header": "Productprijzen",
          "prices_font": {
            "label": "Productprijzen lettertype",
            "options__1": {
              "label": "Primair lettertype"
            },
            "options__2": {
              "label": "Secundair lettertype"
            }
          },
          "prices_font_weight": {
            "label": "Productprijzen lettertype gewicht",
            "options__1": {
              "label": "Licht"
            },
            "options__2": {
              "label": "Vet"
            }
          }
        },
        "breadcrumbs": {
          "header": "Kruimelpad",
          "breadcrumbs_font": {
            "label": "Kruimelpad lettertype",
            "options__1": {
              "label": "Primair lettertype"
            },
            "options__2": {
              "label": "Secundair lettertype"
            }
          },
          "breadcrumbs_font_size": {
            "label": "Kruimelpad lettergrootte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            },
            "options__4": {
              "label": "Extra groot"
            }
          }
        },
        "drawers": {
          "header": "Drawers",
          "drawers_font_size_heading": {
            "label": "Titel lettergrootte"
          }
        },
        "hyphens": {
          "header": "Koppeltekens",
          "enable_hyphens": {
            "label": "Koppeltekens inschakelen",
            "info": "Dit voegt koppeltekens toe aan lange woorden om te voorkomen dat ze de lay-out breken."
          }
        },
        "mobile": {
          "header": "Mobiel",
          "body_font_size_mobile": {
            "label": "Tekst lettergrootte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            },
            "options__4": {
              "label": "Extra groot"
            }
          }
        }
      }
    },
    "images": {
      "name": "Afbeeldingen",
      "settings": {
        "product_images": {
          "header": "Productafbeeldingen",
          "product_image_ratio": {
            "label": "Afbeeldingverhouding",
            "options__1": {
              "label": "Portret"
            },
            "options__2": {
              "label": "Vierkant"
            },
            "options__3": {
              "label": "Landschap"
            }
          },
          "fill_product_images": {
            "label": "Afbeeldingen vullen"
          },
          "show_secondary_image": {
            "label": "Tweede afbeelding tonen als je de muis erboven houdt"
          }
        },
        "multiply": {
          "header": "Afbeeldingen doordrukken",
          "paragraph": "Pas dit effect toe op je product- of collectie afbeeldingen, het verbergt de witte achtergrond. Aanbevolen bij gebruik van een gekleurde algemene achtergrond en afbeeldingen met een witte achtergrond.",
          "multiply_product_images": {
            "label": "Productafbeeldingen doordrukken",
            "options__1": {
              "label": "Doordukken uitschakelen"
            },
            "options__2": {
              "label": "Doordukken inschakelen"
            },
            "options__3": {
              "label": "Doordukken met een achtergrondkleur inschakelen"
            }
          },
          "multiply_product_images_color_palette": {
            "label": "Achtergrondkleur voor doordrukken van productafbeeldingen"
          },
          "multiply_collection_images": {
            "label": "Collectie afbeeldingen doordrukken",
            "options__1": {
              "label": "Doordukken uitschakelen"
            },
            "options__2": {
              "label": "Doordukken inschakelen"
            },
            "options__3": {
              "label": "Doordukken met een lichte achtergrond inschakelen"
            }
          },
          "multiply_collection_images_color_palette": {
            "label": "Achtergrondkleur voor doordrukken van collectie afbeeldingen"
          }
        },
        "corner_radius": {
          "header": "Hoekradius",
          "everything_rounded": {
            "label": "Toon alle afbeeldingen met ronde hoeken"
          }
        }
      }
    },
    "buttons": {
      "name": "Knoppen",
      "settings": {
        "button_height": {
          "label": "Hoogte",
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Gemiddeld"
          },
          "options__3": {
            "label": "Groot"
          }
        },
        "button_style": {
          "label": "Styling",
          "options__1": {
            "label": "Gevuld, schaduw"
          },
          "options__2": {
            "label": "Gevuld, geen schaduw"
          },
          "options__3": {
            "label": "Niet gevuld"
          }
        },
        "button_rounded": {
          "label": "Vorm",
          "options__1": {
            "label": "Vierkant"
          },
          "options__2": {
            "label": "Afgerond"
          },
          "options__3": {
            "label": "Rond"
          }
        },
        "button_font": {
          "label": "Font",
          "options__1": {
            "label": "Primair font"
          },
          "options__2": {
            "label": "Secundair font"
          }
        },
        "button_font_weight": {
          "label": "Lettertype gewicht"
        },
        "button_case": {
          "label": "Letter",
          "options__1": {
            "label": "Standaard"
          },
          "options__2": {
            "label": "HOOFDLETTERS"
          }
        }
      }
    },
    "cart": {
      "name": "Winkelwagen drawer",
      "settings": {
        "enable_cart_drawer": {
          "label": "Winkelwagen drawer inschakelen",
          "info": "Bij het toevoegen van producten aan de winkelwagen, of bij klikken op het winkelwagenicoon in de header, wordt een winkelwagen side drawer geopend in plaats van direct naar de winkelwagenpagina te gaan."
        },
        "empty_cart_text": {
          "label": "Tekst voor lege winkelwagen"
        },
        "cart_drawer_undo": {
          "header": "Ongedaan maken verwijderde producten",
          "enable_cart_drawer_undo_remove": {
            "label": "Schakel het ongedaan maken van verwijderde producten in",
            "info": "Schakel dit in als je een bezoeker de mogelijkheid wilt geven als een product wordt verwijderd, dit ongedaan kan worden gemaakt."
          },
          "enable_cart_drawer_undo_remove_delay": {
            "label": "Timer voor ongedaan maken van verwijderde producten inschakelen",
            "info": "Dit zal de 'ongedaan maken' mogelijkheid na een bepaalde tijd verbergen."
          },
          "cart_drawer_undo_remove_delay": {
            "label": "Timer ongedaan maken van verwijderde producten"
          }
        },
        "cart_drawer_checkout_button": {
          "label": "Toon checkout knop"
        },
        "cart_drawer_order_notes": {
          "label": "Toon bestelnotities"
        },
        "upsell": {
          "header": "Winkelwagen upsell",
          "enable_cart_drawer_upsell_complementary": {
            "label": "Toon winkelwagen complementaire producten",
            "info": "Producten kunnen worden ingesteld in Shopify's Search & Discovery app."
          },
          "enable_cart_drawer_upsell_related": {
            "label": "Toon winkelwagen gerelateerde producten"
          },
          "enable_cart_drawer_upsell_variants": {
            "label": "Toon variantenkiezer",
            "info": "Niet van toepassing op producten met meer dan 250 varianten."
          }
        }
      }
    },
    "trustbadge": {
      "name": "Trustbadge",
      "settings": {
        "show_trustbadge": {
          "label": "Toon secure and safe checkout",
          "info": "Het wordt automatisch getoond in de winkelwagen drawer, winkelwagenpagina en het kan ook als een blok worden toegevoegd aan de productpagina en op het uitgelichte productgedeelte."
        },
        "trustbadge_image": {
          "label": "Payment gateway providers",
          "options__1": {
            "label": "Geen"
          },
          "options__2": {
            "label": "Amazon Pay"
          },
          "options__3": {
            "label": "Authorize.net"
          },
          "options__4": {
            "label": "Klarna"
          },
          "options__5": {
            "label": "Opayo"
          },
          "options__6": {
            "label": "PayPal"
          },
          "options__7": {
            "label": "Shop Pay"
          },
          "options__8": {
            "label": "Skrill"
          },
          "options__9": {
            "label": "Stripe"
          }
        },
        "trustbadge_custom_image": {
          "label": "Custom afbeelding",
          "info": "Upload je eigen afbeelding om naast de secure & safe checkout tekst weer te geven. .png- of .svg-bestand aanbevolen."
        },
        "trustbadge_text": {
          "label": "Secure & safe checkout tekst"
        }
      }
    },
    "search_drawer": {
      "name": "Zoekresultaten drawer",
      "settings": {
        "enable_search_drawer": {
          "label": "Zoekresultaten drawer inschakelen"
        },
        "search_behavour": {
          "search_drawer_enable_suggestions": {
            "label": "Schakel zoeksuggesties in"
          },
          "search_drawer_show_price": {
            "label": "Toon prijs"
          },
          "search_drawer_show_vendor": {
            "label": "Toon verkoper"
          }
        },
        "resources": {
          "header": "Zoek naar:",
          "search_drawer_enable_collections": {
            "label": "Collecties"
          },
          "search_drawer_enable_products": {
            "label": "Producten"
          },
          "search_drawer_enable_pages": {
            "label": "Pagina's"
          },
          "search_drawer_enable_articles": {
            "label": "Blogberichten"
          }
        },
        "popular_collections": {
          "header": "Populaire collecties",
          "search_drawer_popular_collections_title": {
            "label": "Titel",
            "info": "Promoot populaire collecties en/of belangrijke producten wanneer de zoekbalk leeg is."
          },
          "search_drawer_popular_collections": {
            "label": "Collecties"
          }
        },
        "popular_products": {
          "header": "Populaire producten",
          "search_drawer_popular_products_title": {
            "label": "Titel"
          },
          "search_drawer_popular_products": {
            "label": "Producten"
          }
        }
      }
    },
    "products": {
      "name": "Producten",
      "settings": {
        "productcards_text_alignment": {
          "label": "Productkaart tekstuitlijning",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          },
          "options__3": {
            "label": "Rechts"
          }
        },
        "labels": {
          "header": "Productlabels",
          "show_sale_label": {
            "label": "Toon 'Sale' label op producten"
          },
          "sale_label_price": {
            "label": "Toon afprijzing op 'Sale' label",
            "options__1": {
              "label": "Geen"
            },
            "options__2": {
              "label": "Percentage"
            },
            "options__3": {
              "label": "Prijsverschil"
            }
          },
          "show_stock_label": {
            "label": "Toon 'Bijna uitverkocht' op producten",
            "info": "Wanneer de voorraad van een product bijna op is, informeer dan je bezoekers hierover. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          },
          "stock_label_qty": {
            "label": "Voorraadniveau om het label 'Bijna uitverkocht' weer te geven"
          },
          "show_price_varies": {
            "label": "Toon prijsklasse op productkaarten",
            "info": "Wanneer verschillende varianten van een product een verschillende prijsklasse hebben, wordt dit weergegeven op je productkaart."
          },
          "product_custom_label": {
            "label": "Metaveld: aangepast label",
            "info": "Maak een metaveld voor een aangepast label en voer hier de naamruimte en sleutel in (bijv. custom.label) [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          }
        },
        "stock": {
          "header": "Productvoorraad",
          "show_product_stock": {
            "label": "Toon beschikbare productvoorraad"
          },
          "show_product_stock_qty": {
            "label": "Voorraadniveau om beschikbare productvoorraad weer te geven",
            "info": "Je kunt ook hieronder 'altijd beschikbare productvoorraad tonen' aanvinken om dit te overschrijven."
          },
          "show_product_stock_always": {
            "label": "Altijd beschikbare productvoorraad tonen"
          }
        },
        "ratings": {
          "header": "Productbeoordelingen",
          "show_product_rating": {
            "label": "Productbeoordelingen weergeven",
            "info": "De beoordelingen worden automatisch uit je beoordelings-app gehaald."
          }
        },
        "vendor": {
          "header": "Productverkoper",
          "show_vendor": {
            "label": "Toon productverkoper"
          }
        },
        "preorder": {
          "header": "Pre-order",
          "show_preorder": {
            "label": "Toon pre-order knop",
            "info": "Dit toont een pre-orderknop als koopknop. Alleen van toepassing wanneer het product de instelling 'doorverkopen indien niet op voorraad' actief heeft."
          },
          "show_preorder_inventory": {
            "label": "Toon voorraad voor pre-orderproducten"
          },
          "preorder_button_text": {
            "label": "Knoplabel"
          }
        },
        "short_product_description": {
          "header": "Korte productbeschrijving",
          "product_short_description": {
            "label": "Korte productbeschrijving weergeven",
            "info": "Laat een deel van de productbeschrijving zien of gebruik een eigen tekst.",
            "options__1": {
              "label": "Geen"
            },
            "options__2": {
              "label": "Onderdeel van de productbeschrijving"
            },
            "options__3": {
              "label": "Aangepaste tekst"
            }
          },
          "product_short_description_text": {
            "label": "Metaveld: aangepaste korte productbeschrijving",
            "info": "Maak een metaveld voor een aangepaste korte beschrijving en voer hier de naamruimte en sleutel in (bijv. custom.short_description) [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          }
        },
        "titles": {
          "header": "Titels",
          "product_titles_caps": {
            "label": "Toon producttitels in HOOFDLETTERS"
          }
        },
        "color_swatches": {
          "header": "Kleurstalen",
          "enable_color_swatches": {
            "label": "Kleurstalen inschakelen",
            "info": "[Meer informatie](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          },
          "color_swatch_name": {
            "label": "Kleurstalen naam",
            "info": "De optienaam die wordt gebruikt voor de kleurstalen, bijvoorbeeld 'Kleur' of 'Color'. Je kunt meerdere namen gebruiken door ze elk op een aparte regel in te voeren."
          },
          "color_swatch_hexes": {
            "label": "Handmatige kleuren",
            "info": "Voor elke kleur: voer op een aparte regel de naam van de kleur in, dan een dubbele punt (:) gevolgd door de hex code (bijvoorbeeld: Rood: #DC143C). Laat de hex code achterwege om een custom swatch afbeelding te gebruiken."
          },
          "color_swatches_variant_image": {
            "label": "Toon variantafbeeldingen in plaats van kleuren op productcards"
          }
        }
      }
    },
    "shipping_delivery": {
      "name": "Verzending en bezorging",
      "settings": {
        "free_shipping": {
          "header": "Gratis verzending",
          "enable_free_shipping": {
            "label": "Gratis verzending inschakelen",
            "info": "Laat je klanten weten wanneer gratis verzending is bereikt. Let op dat deze functie niet goed werkt wanneer meerdere valuta is ingeschakeld. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208035-shipping-and-delivery)"
          },
          "free_shipping_amount": {
            "label": "Minimaal orderbedrag tot gratis verzending"
          }
        },
        "deliverytime": {
          "header": "Bezorgtijd",
          "paragraph": "Gebruik metavelden om product levertijden te tonen. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208035-shipping-and-delivery)",
          "product_deliverytime_in_stock": {
            "label": "Metaveld - op voorraad"
          },
          "product_deliverytime_not_in_stock": {
            "label": "Metaveld - niet op voorraad"
          },
          "paragraph__2": "Wil je standaard levertijden weergeven voor producten waar geen levertijd-metaveld is ingesteld? Vul ze hieronder in.",
          "default_product_deliverytime_in_stock": {
            "label": "Standaard levertijd - op voorraad"
          },
          "default_product_deliverytime_not_in_stock": {
            "label": "Standaard levertijd - niet op voorraad"
          },
          "product_deliverytime_info": {
            "label": "Metaveld extra info voor levertijd"
          },
          "default_product_deliverytime_info": {
            "label": "Standaard extra info voor levertijd",
            "info": "Dit zal een info icoon tonen naast je levertijd op productkaarten en productpagina. De tekst wordt weergegeven in een pop-up."
          },
          "show_deliverytime_always": {
            "label": "Toon de levertijd van producten voor producten zonder voorraadtracking",
            "info": "Wanneer de voorraad van een product niet wordt bijgehouden, wordt de levertijd 'op voorraad' weergegeven."
          }
        },
        "shipping_timer": {
          "header": "Verzendtimer",
          "paragraph": "Als je 'verzending op dezelfde dag' aanbiedt, laat dan een timer zien welk aangeeft hoeveel tijd bezoekers nog hebben om je producten te bestellen die vandaag worden verzonden.",
          "shipping_timer_show_until": {
            "label": "Voor hoe laat moeten je klanten bestellen?",
            "options__1": {
              "label": "01:00"
            },
            "options__2": {
              "label": "02:00"
            },
            "options__3": {
              "label": "03:00"
            },
            "options__4": {
              "label": "04:00"
            },
            "options__5": {
              "label": "05:00"
            },
            "options__6": {
              "label": "06:00"
            },
            "options__7": {
              "label": "07:00"
            },
            "options__8": {
              "label": "08:00"
            },
            "options__9": {
              "label": "09:00"
            },
            "options__10": {
              "label": "10:00"
            },
            "options__11": {
              "label": "11:00"
            },
            "options__12": {
              "label": "12:00"
            },
            "options__13": {
              "label": "13:00"
            },
            "options__14": {
              "label": "14:00"
            },
            "options__15": {
              "label": "15:00"
            },
            "options__16": {
              "label": "16:00"
            },
            "options__17": {
              "label": "17:00"
            },
            "options__18": {
              "label": "18:00"
            },
            "options__19": {
              "label": "19:00"
            },
            "options__20": {
              "label": "20:00"
            },
            "options__21": {
              "label": "21:00"
            },
            "options__22": {
              "label": "22:00"
            },
            "options__23": {
              "label": "23:00"
            },
            "options__24": {
              "label": "23:59"
            }
          },
          "shipping_timer_show_from": {
            "label": "Vanaf hoe laat moet de timer op je pagina verschijnen?",
            "options__1": {
              "label": "00:00"
            },
            "options__2": {
              "label": "01:00"
            },
            "options__3": {
              "label": "02:00"
            },
            "options__4": {
              "label": "03:00"
            },
            "options__5": {
              "label": "04:00"
            },
            "options__6": {
              "label": "05:00"
            },
            "options__7": {
              "label": "06:00"
            },
            "options__8": {
              "label": "07:00"
            },
            "options__9": {
              "label": "08:00"
            },
            "options__10": {
              "label": "09:00"
            },
            "options__11": {
              "label": "10:00"
            },
            "options__12": {
              "label": "11:00"
            },
            "options__13": {
              "label": "12:00"
            },
            "options__14": {
              "label": "13:00"
            },
            "options__15": {
              "label": "14:00"
            },
            "options__16": {
              "label": "15:00"
            },
            "options__17": {
              "label": "16:00"
            },
            "options__18": {
              "label": "17:00"
            },
            "options__19": {
              "label": "18:00"
            },
            "options__20": {
              "label": "19:00"
            },
            "options__21": {
              "label": "20:00"
            },
            "options__22": {
              "label": "21:00"
            },
            "options__23": {
              "label": "22:00"
            },
            "options__24": {
              "label": "23:00"
            }
          },
          "shipping_timer_show_unavailable": {
            "label": "Toon timer wanneer het product niet beschikbaar is"
          },
          "paragraph__2": "Op welke dagen wil je de timer laten zien?",
          "shipping_timer_enable_monday": {
            "label": "Maandag"
          },
          "shipping_timer_enable_tuesday": {
            "label": "Dinsdag"
          },
          "shipping_timer_enable_wednesday": {
            "label": "Woensdag"
          },
          "shipping_timer_enable_thursday": {
            "label": "Donderdag"
          },
          "shipping_timer_enable_friday": {
            "label": "Vrijdag"
          },
          "shipping_timer_enable_saturday": {
            "label": "Zaterdag"
          },
          "shipping_timer_enable_sunday": {
            "label": "Zondag"
          }
        }
      }
    },
    "social_media": {
      "name": "Social media",
      "settings": {
        "whatsapp": {
          "label": "WhatsApp telefoonnummer"
        },
        "header": "Social media accounts",
        "paragraph": "Je kunt je sociale links instellen op de ['Merk' pagina](https://help.shopify.com/nl/manual/promoting-marketing/managing-brand-assets) in de Shopify-instellingen, maar je kunt ook kiezen om ze hier in te stellen.",
        "social_instagram": {
          "label": "Instagram"
        },
        "social_instagram_name": {
          "label": "Instagram naam"
        },
        "social_pinterest": {
          "label": "Pinterest"
        },
        "social_pinterest_name": {
          "label": "Pinterest naam"
        },
        "social_youtube": {
          "label": "YouTube"
        },
        "social_youtube_name": {
          "label": "YouTube naam"
        },
        "social_facebook": {
          "label": "Facebook"
        },
        "social_facebook_name": {
          "label": "Facebook naam"
        },
        "social_twitter": {
          "label": "X"
        },
        "social_twitter_name": {
          "label": "X naam"
        },
        "social_tiktok": {
          "label": "TikTok"
        },
        "social_tiktok_name": {
          "label": "TikTok naam"
        },
        "social_tumblr": {
          "label": "Tumblr"
        },
        "social_tumblr_name": {
          "label": "Tumblr naam"
        },
        "social_snapchat": {
          "label": "Snapchat"
        },
        "social_snapchat_name": {
          "label": "Snapchat naam"
        }
      }
    },
    "newsletter": {
      "name": "Nieuwsbrief pop-up",
      "settings": {
        "newsletter_popup": {
          "header": "Nieuwsbrief pop-up",
          "show_newsletterpopup": {
            "label": "Nieuwsbrief pop-up inschakelen"
          },
          "newsletter_popup_testmode": {
            "label": "Testmodus inschakelen",
            "info": "Dit toont de pop-up bij elke verversing, je zou deze alleen moeten gebruiken om de pop-up te bewerken. Zorg ervoor dat 'Testmodus' is uitgeschakeld bij het publiceren van je thema."
          },
          "newsletter_popup_seconds": {
            "label": "Toon de pop-up na"
          },
          "newsletter_popup_image": {
            "label": "Afbeelding",
            "info": "960 x 900px .png aanbevolen"
          }
        },
        "checkbox": {
          "header": "Selectievakje",
          "enable_newsletter_terms_checkbox": {
            "label": "Toon selectievakje"
          },
          "newsletter_terms_text": {
            "label": "Label voor het selectievakje",
            "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
          }
        }
      }
    },
    "age_verify_popup": {
      "name": "Leeftijdsverificatie pop-up",
      "settings": {
        "show_age_verify_popup": {
          "label": "Leeftijdsverificatie pop-up inschakelen",
          "info": "[Lees meer](https://intercom.help/someoneyouknow/en/articles/6686994-age-verification-popup)"
        },
        "age_verify_popup_testmode": {
          "label": "Testmodus inschakelen",
          "info": "Dit toont de pop-up bij elke verversing, je zou dit alleen moeten gebruiken om de pop-up te bewerken. Zorg ervoor dat 'Testmodus' is uitgeschakeld bij het publiceren van je thema."
        },
        "age_verify_popup_image": {
          "label": "Afbeelding",
          "info": "470 x 110px .png aanbevolen"
        },
        "verify": {
          "header": "Verificatie vraag",
          "age_verify_popup_title": {
            "label": "Titel"
          },
          "age_verify_popup_text": {
            "label": "Tekst"
          },
          "age_verify_popup_cookie_text": {
            "label": "Cookies tekst (optioneel)",
            "info": "De tekst wordt getoond aan bezoekers wanneer dit verplicht is. Zorg ervoor dat je 'Beperk tracking voor klanten in Europa' hebt ingeschakeld. [Lees meer](https://help.shopify.com/en/manual/your-account/privacy/cookies#tracking-european-customers-and-gdpr-compliance)"
          },
          "age_verify_popup_accept_text": {
            "label": "Goedkeuren knoplabel"
          },
          "age_verify_popup_deny_text": {
            "label": "Afwijzen knoplabel"
          }
        },
        "denied": {
          "header": "Afgewezen",
          "paragraph": "Dit wordt weergegeven wanneer de klant de leeftijdsverificatie heeft geweigerd.",
          "age_verify_popup_denied_title": {
            "label": "Titel"
          },
          "age_verify_popup_denied_text": {
            "label": "Tekst"
          }
        }
      }
    },
    "cookies": {
      "name": "Cookies",
      "settings": {
        "show_cookiebanner": {
          "label": "Toon cookiebanner/pop-up",
          "info": "De cookiebanner of pop-up wordt automatisch getoond aan bezoekers wanneer dit verplicht is, mits geen leeftijdsverificatie pop-up is ingeschakeld. Zorg ervoor dat je 'Beperk tracking voor klanten in Europa' hebt ingeschakeld. [Lees meer](https://help.shopify.com/en/manual/your-account/privacy/cookies#tracking-european-customers-and-gdpr-compliance)",
          "options__1": {
            "label": "Banner"
          },
          "options__2": {
            "label": "Pop-up"
          },
          "options__3": {
            "label": "Geen"
          }
        },
        "cookiebanner_testmode": {
          "label": "Testmodus inschakelen",
          "info": "Dit toont de cookiebanner/pop-up bij elke verversing, je zou dit alleen moeten gebruiken om te bewerken. Zorg ervoor dat 'Testmodus' is uitgeschakeld bij het publiceren van je thema."
        },
        "cookiebanner_image": {
          "label": "Afbeelding in de pop-up voor cookies",
          "info": "Alleen van toepassing in de pop-upversie van de cookiebanner. (580 x 540px .png aanbevolen)"
        },
        "cookiebanner_title": {
          "label": "Titel",
          "info": "Alleen van toepassing in de pop-upversie van de cookiebanner."
        },
        "cookiebanner_text": {
          "label": "Tekst",
          "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
        }
      }
    },
    "custom_scripts": {
      "name": "Handmatige scripts",
      "settings": {
        "custom_script_for_head": {
          "label": "Handmatig script voor head tag",
          "info": "Met deze instelling kun je scripts invoegen in de head-tag van de themacode."
        },
        "custom_script_for_body": {
          "label": "Handmatig script voor body tag",
          "info": "Met deze instelling kun je scripts invoegen in de body-tag van de themacode."
        }
      }
    },
    "shop_the_look": {
      "name": "Shop the look",
      "settings": {
        "paragraph": "Verander elke afbeeldingsbanner in de hele winkel in een shop the look-banner door simpelweg toe te voegen producten in de bannerinstellingen. Er gaat een la open met de geselecteerde productkaarten",
        "shop_the_look_title": {
          "label": "Heading"
        },
        "shop_the_look_subtitle": {
          "label": "Ondertitel"
        },
        "shop_the_look_size": {
          "label": "Titelgrootte",
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Gemiddeld"
          },
          "options__3": {
            "label": "Groot"
          },
          "options__4": {
            "label": "Extra groot"
          }
        },
        "shop_the_look_items": {
          "label": "Producten per rij",
          "info": "Als de lijst meer collecties bevat dan weergegeven, wordt dit automatisch een slider. Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/8789841-shop-the-look)"
        }
      }
    }
  },
  "sections": {
    "section": {
      "name": "Aangepaste sectie",
      "settings": {
        "overlay_opacity": {
          "label": "Achtergrond doorzichtigheid"
        }
      },
      "presets": {
        "custom_section": "Aangepaste sectie"
      }
    },
    "background": {
      "name": "Achtergrond",
      "settings": {
        "overlay_opacity": {
          "label": "Achtergrond doorzichtigheid"
        },
        "height": {
          "label": "Banner hoogte",
          "options__1": {
            "label": "Extra klein"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Gemiddeld"
          },
          "options__4": {
            "label": "Groot"
          }
        },
        "enable_custom_height": {
          "label": "Schakel aangepaste hoogte in."
        },
        "custom_height": {
          "label": "Aangepaste hoogte",
          "info": "Alleen van toepassing wanneer 'aangepaste hoogte' is ingeschakeld."
        },
        "mobile": {
          "height_mobile": {
            "label": "Banner hoogte",
            "options__1": {
              "label": "Extra klein"
            },
            "options__2": {
              "label": "Klein"
            },
            "options__3": {
              "label": "Gemiddeld"
            },
            "options__4": {
              "label": "Groot"
            }
          },
          "enable_custom_height_mobile": {
            "label": "Schakel aangepaste hoogte in."
          },
          "custom_height_mobile": {
            "label": "Aangepaste hoogte",
            "info": "Alleen van toepassing wanneer 'aangepaste hoogte' is ingeschakeld."
          }
        }
      },
      "presets": {
        "name": "Achtergrond"
      }
    },
    "grid": {
      "name": "Flexibel grid banners",
      "settings": {
        "number_of_columns": {
          "label": "Aantal kolommen",
          "options__1": {
            "label": "4"
          },
          "options__2": {
            "label": "6"
          }
        }
      },
      "blocks": {
        "image": {
          "settings": {
            "size": {
              "header": "Bannergrootte",
              "cols": {
                "label": "Kolommen",
                "info": "Als het aantal kolommen '4' is geselecteerd, worden waarden boven 4 genegeerd."
              },
              "rows": {
                "label": "Rijen"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Flexibel grid banners"
      }
    },
    "gallery": {
      "name": "Afbeeldingengalerij",
      "settings": {
        "items_layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Collage gespiegeld"
          },
          "options__3": {
            "label": "Kolommen"
          }
        },
        "items_width": {
          "label": "Aantal kolommen"
        },
        "space_between": {
          "label": "Ruimte tussen banners"
        },
        "height": {
          "info": "Gebaseerd op de eerste banner in elke rij."
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "image_zoom": {
          "label": "Zoomeffect weergeven bij zweven op bannerafbeeldingen"
        },
        "image_move": {
          "label": "Verplaatsingsanimatie weergeven bij zweven op banner",
          "info": "Niet van toepassing wanneer 'Ruimte tussen banners' kleiner is dan 10px."
        },
        "show_content_below": {
          "label": "Toon inhoud onder bannerafbeeldingen"
        },
        "show_content_on_hover": {
          "label": "Content weergeven bij zweven op bannerafbeeldingen"
        },
        "content_on_hover_color_palette": {
          "label": "Kleurenschema bij zweven op bannerafbeeldingen"
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "layout_mobile": {
            "label": "Lay-out",
            "options__1": {
              "label": "In rijen"
            },
            "options__2": {
              "label": "Swipe"
            }
          },
          "mobile_height": {
            "label": "Banner hoogte",
            "options__1": {
              "label": "Extra klein"
            },
            "options__2": {
              "label": "Klein"
            },
            "options__3": {
              "label": "Gemiddeld"
            },
            "options__4": {
              "label": "Groot"
            }
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Afbeeldingsbanner",
          "settings": {
            "image": {
              "label": "Afbeelding"
            },
            "video": {
              "label": "Video"
            },
            "fill_images": {
              "label": "Afbeelding/video vullen"
            },
            "overlay_opacity": {
              "label": "Afbeelding overlay doorzichtigheid"
            },
            "text_position": {
              "label": "Inhoud positie",
              "options__1": {
                "label": "Linksboven"
              },
              "options__2": {
                "label": "Midden boven"
              },
              "options__3": {
                "label": "Rechtsboven"
              },
              "options__4": {
                "label": "Midden links"
              },
              "options__5": {
                "label": "Midden gecentreerd"
              },
              "options__6": {
                "label": "Midden rechts"
              },
              "options__7": {
                "label": "Linksonder"
              },
              "options__8": {
                "label": "Middenonder"
              },
              "options__9": {
                "label": "Rechts onder"
              }
            },
            "mobile": {
              "header": "Mobiele lay-out",
              "image_mobile": {
                "label": "Mobiele afbeelding (optioneel)"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Afbeeldingengalerij"
      }
    },
    "promo_gallery": {
      "name": "Promo galerij",
      "settings": {
        "items_width": {
          "label": "Kolommen per rij"
        },
        "height": {
          "info": "Gebaseerd op de eerste afbeelding/video in elke rij."
        },
        "space_between": {
          "label": "Ruimte tussen banners"
        },
        "image_zoom": {
          "label": "Zoomeffect weergeven bij zweven op bannerafbeeldingen"
        },
        "shadow": {
          "label": "Schaduw weergeven bij bannerafbeeldingen"
        },
        "banners": {
          "header": "Promo banners",
          "title_size_blocks": {
            "label": "Titelgrootte"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "layout_mobile": {
            "label": "Lay-out",
            "options__1": {
              "label": "In rijen"
            },
            "options__2": {
              "label": "Swipe"
            }
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Promo banner",
          "settings": {
            "image": {
              "label": "Afbeelding"
            },
            "video": {
              "label": "Video"
            },
            "overlay_opacity": {
              "label": "Achtergrond doorzichtigheid"
            },
            "overlay_opacity_img": {
              "label": "Afbeelding/video overlay doorzichtigheid"
            },
            "text_position": {
              "label": "Inhoud positie",
              "options__1": {
                "label": "Boven links"
              },
              "options__2": {
                "label": "Boven gecentreerd"
              },
              "options__3": {
                "label": "Links gecentreerd"
              },
              "options__4": {
                "label": "Midden gecentreerd"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Promo galerij"
      }
    },
    "image_with_products": {
      "name": "Afbeeldingsbanner met producten",
      "settings": {
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Afbeelding links, producten rechts"
          },
          "options__2": {
            "label": "Producten links, afbeelding rechts"
          }
        },
        "text_position": {
          "label": "Inhoud positie",
          "options__1": {
            "label": "Linksboven"
          },
          "options__2": {
            "label": "Midden boven"
          },
          "options__3": {
            "label": "Rechtsboven"
          },
          "options__4": {
            "label": "Midden links"
          },
          "options__5": {
            "label": "Midden gecentreerd"
          },
          "options__6": {
            "label": "Midden rechts"
          },
          "options__7": {
            "label": "Linksonder"
          },
          "options__8": {
            "label": "Middenonder"
          },
          "options__9": {
            "label": "Rechts onder"
          }
        },
        "products": {
          "label": "Producten"
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "width_mobile": {
            "label": "Bannerbreedte",
            "options__1": {
              "label": "Volledige breedte"
            },
            "options__2": {
              "label": "Normale breedte"
            }
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "presets": {
        "name": "Afbeeldingsbanner met producten"
      }
    },
    "image_with_text": {
      "name": "Afbeelding met tekst",
      "settings": {
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Afbeelding links, content rechts"
          },
          "options__2": {
            "label": "Afbeelding rechts, content links"
          }
        },
        "text_position": {
          "label": "Inhoud positie",
          "options__1": {
            "label": "Linksboven"
          },
          "options__2": {
            "label": "Midden boven"
          },
          "options__3": {
            "label": "Rechtsboven"
          },
          "options__4": {
            "label": "Midden links"
          },
          "options__5": {
            "label": "Midden gecentreerd"
          },
          "options__6": {
            "label": "Midden rechts"
          },
          "options__7": {
            "label": "Linksonder"
          },
          "options__8": {
            "label": "Middenonder"
          },
          "options__9": {
            "label": "Rechts onder"
          }
        },
        "paragraph": "Pas de inhoud aan met blokken. De positie wordt automatisch geoptimaliseerd voor mobiel."
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst"
            }
          }
        },
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Informatie"
            }
          }
        },
        "buttons": {
          "name": "Knoppen",
          "settings": {
            "first_link": {
              "header": "Eerste knop"
            },
            "second_link": {
              "header": "Tweede knop"
            }
          }
        }
      },
      "presets": {
        "name": "Afbeelding met tekst"
      }
    },
    "video_with_text": {
      "name": "Videobanner",
      "settings": {
        "image": {
          "label": "Omslagfoto (optioneel)",
          "info": "Selecteer een afbeelding om de video in een pop-up af te laten spelen, laat leeg om video op de pagina te laten afspelen. 760 x 570px aanbevolen."
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Video links, content rechts"
          },
          "options__2": {
            "label": "Video rechts, content links"
          }
        },
        "text_position": {
          "label": "Inhoud positie",
          "options__1": {
            "label": "Linksboven"
          },
          "options__2": {
            "label": "Midden boven"
          },
          "options__3": {
            "label": "Rechtsboven"
          },
          "options__4": {
            "label": "Midden links"
          },
          "options__5": {
            "label": "Midden gecentreerd"
          },
          "options__6": {
            "label": "Midden rechts"
          },
          "options__7": {
            "label": "Linksonder"
          },
          "options__8": {
            "label": "Middenonder"
          },
          "options__9": {
            "label": "Rechts onder"
          }
        },
        "paragraph": "Pas de inhoud aan met blokken. De positie wordt automatisch geoptimaliseerd voor mobiel.",
        "video_url": {
          "label": "Video URL",
          "info": "Accepteert Vimeo of YouTube links. Je kunt ook een video uit je mediabibliotheek uploaden of selecteren door de instelling 'Video' hieronder te gebruiken."
        },
        "video_alt_text": {
          "label": "Video URL alt tekst",
          "info": "Beschrijf de video om deze toegankelijk te maken voor klanten die schermlezers gebruiken."
        },
        "video": {
          "label": "Video"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "title": {
          "name": "Titel"
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "text": {
              "label": "Tekst"
            }
          }
        },
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Informatie"
            }
          }
        },
        "buttons": {
          "name": "Knoppen",
          "settings": {
            "first_link": {
              "header": "Eerste knop"
            },
            "second_link": {
              "header": "Tweede knop"
            }
          }
        }
      },
      "presets": {
        "name": "Videobanner"
      }
    },
    "image_comparison": {
      "name": "Afbeelding vergelijking",
      "settings": {
        "image_before": {
          "label": "'Voor' afbeelding",
          "info": "Gebruik voor de beste resultaten een afbeelding met een beeldverhouding van 3:2. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6686981-section-image-comparison)"
        },
        "image_after": {
          "label": "'Na' afbeelding",
          "info": "Zorg ervoor dat de afmeting overeenkomt met de 'Voor' afbeelding."
        },
        "drag_color_palette": {
          "label": "Sleepknop/lijn kleurenschema"
        },
        "drag_position": {
          "label": "Initiële sleeppositie"
        },
        "labels": {
          "label": "Toon labels"
        },
        "label_before": {
          "label": "'Voor' afbeelding label"
        },
        "label_after": {
          "label": "'Na' afbeelding label"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        }
      },
      "presets": {
        "name": "Afbeelding vergelijking"
      }
    },
    "collection_list": {
      "name": "Collectielijst",
      "settings": {
        "collections": {
          "label": "Collecties"
        },
        "custom_image_ratio": {
          "label": "Afbeelding ratio",
          "info": "Selecteer 75% voor liggende afbeeldingen. Selecteer voor vierkant 100%. Selecteer 125% voor portret."
        },
        "show_in_circle": {
          "label": "Toon afbeeldingen in cirkel",
          "info": "Alleen van toepassing wanneer de beeldverhouding is ingesteld op 100%."
        },
        "image_ratio": {
          "label": "Afbeelding ratio",
          "info": "Voeg afbeeldingen toe door je collecties te bewerken [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208057-section-collection-list)",
          "options__1": {
            "label": "Portret"
          },
          "options__2": {
            "label": "Vierkant"
          },
          "options__3": {
            "label": "Landschap"
          }
        },
        "fill_images": {
          "label": "Afbeeldingen vullen"
        },
        "layout": {
          "label": "Lay-out",
          "info": "Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208057-section-collection-list)",
          "options__1": {
            "label": "Diavoorstelling"
          },
          "options__2": {
            "label": "Rijen"
          }
        },
        "number_of_items": {
          "label": "Collecties per rij"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "collection_heading": {
          "header": "Heading collections"
        },
        "collection_title_position": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "show_collection_titles_underline": {
          "label": "Toon collectietitels onderlijnd"
        },
        "show_collection_titles": {
          "label": "Toon collectietitels"
        },
        "collection_title_size": {
          "label": "Titelgrootte collecties",
          "options__1": {
            "label": "Extra klein"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Gemiddeld"
          },
          "options__4": {
            "label": "Groot"
          },
          "options__5": {
            "label": "Extra groot"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out"
        }
      },
      "presets": {
        "name": "Collectielijst"
      }
    },
    "slideshow": {
      "name": "Diavoorstelling",
      "settings": {
        "width": {
          "label": "Breedte",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "autoplay": {
          "label": "Automatisch afspelen inschakelen"
        },
        "autoplay_seconds": {
          "label": "Seconden tussen dia's"
        },
        "enable_controls": {
          "label": "Toon afspeel-/pauzeknop"
        },
        "mobile": {
          "header": "Mobiele lay-out"
        }
      },
      "blocks": {
        "slide": {
          "name": "Dia",
          "settings": {
            "image": {
              "label": "Afbeelding",
              "info": "Diavoorstelling kan in drie verschillende lay-outs worden gebruikt. Voor meer informatie en aanbevolen afbeeldingsformaten [klik hier](https://intercom.help/someoneyouknow/en/articles/6208058-section-slideshow)."
            },
            "layout": {
              "label": "Lay-out",
              "options__1": {
                "label": "Afbeelding links, content rechts"
              },
              "options__2": {
                "label": "Afbeelding rechts, content links"
              },
              "options__3": {
                "label": "Achtergrondafbeelding"
              }
            },
            "overlay_opacity": {
              "label": "Afbeelding overlay doorzichtigheid"
            },
            "video": {
              "label": "Video",
              "info": "Video wordt afgespeeld op de pagina."
            },
            "text_position": {
              "label": "Inhoud positie",
              "options__1": {
                "label": "Linksboven"
              },
              "options__2": {
                "label": "Midden boven"
              },
              "options__3": {
                "label": "Rechtsboven"
              },
              "options__4": {
                "label": "Midden links"
              },
              "options__5": {
                "label": "Midden gecentreerd"
              },
              "options__6": {
                "label": "Midden rechts"
              },
              "options__7": {
                "label": "Linksonder"
              },
              "options__8": {
                "label": "Middenonder"
              },
              "options__9": {
                "label": "Rechts onder"
              }
            },
            "buttons": {
              "heading": "Knoppen",
              "settings": {
                "first_link": {
                  "header": "Eerste knop"
                },
                "second_link": {
                  "header": "Tweede knop"
                }
              }
            },
            "mobile": {
              "header": "Mobiele lay-out",
              "image_mobile": {
                "label": "Mobiele afbeelding (optioneel)"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Diavoorstelling"
      }
    },
    "image_hotspots": {
      "name": "Afbeelding hotspot banner",
      "settings": {
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "hotspot_height": {
          "label": "Hotspot hoogte"
        },
        "hotspot_color_palette": {
          "label": "Hotspot kleur"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Afbeelding links, content rechts"
          },
          "options__2": {
            "label": "Afbeelding rechts, content links"
          }
        },
        "width": {
          "label": "Breedte",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "enable_custom_height": {
          "label": "Custom minimale hoogte inschakelen"
        },
        "custom_height": {
          "label": "Custom minimale hoogte"
        },
        "content": {
          "header": "Content"
        },
        "text_position": {
          "label": "Inhoud positie",
          "options__1": {
            "label": "Linksboven"
          },
          "options__2": {
            "label": "Midden boven"
          },
          "options__3": {
            "label": "Rechtsboven"
          },
          "options__4": {
            "label": "Midden links"
          },
          "options__5": {
            "label": "Midden gecentreerd"
          },
          "options__6": {
            "label": "Midden rechts"
          },
          "options__7": {
            "label": "Linksonder"
          },
          "options__8": {
            "label": "Middenonder"
          },
          "options__9": {
            "label": "Rechts onder"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "image_mobile": {
            "label": "Mobiele afbeelding (optioneel)"
          }
        }
      },
      "blocks": {
        "hotspot": {
          "name": "Hotspot",
          "settings": {
            "product": {
              "label": "Product (optioneel)",
              "info": "Kies een product of vul de content hieronder."
            },
            "content": {
              "header": "Content",
              "paragraph": "Alleen van toepassing wanneer er geen product is gekozen.",
              "text_position": {
                "label": "Inhoud positie",
                "options__1": {
                  "label": "Links"
                },
                "options__2": {
                  "label": "Midden"
                },
                "options__3": {
                  "label": "Rechts"
                }
              },
              "title": {
                "label": "Titel"
              },
              "text": {
                "label": "Tekst"
              }
            },
            "position": {
              "header": "Positie",
              "position_left": {
                "label": "Horizontale positie"
              },
              "position_top": {
                "label": "Verticale positie"
              },
              "position_left_mobile": {
                "label": "Horizontale positie mobiel"
              },
              "position_top_mobile": {
                "label": "Verticale positie mobiel"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Afbeelding hotspot banner"
      }
    },
    "image_details": {
      "name": "Afbeelding specificaties banner",
      "settings": {
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "hotspot_height": {
          "label": "Hotspot grootte"
        },
        "hotspot_style": {
          "label": "Hotspot style"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Afbeelding links, content rechts"
          },
          "options__2": {
            "label": "Afbeelding rechts, content links"
          }
        },
        "width": {
          "label": "Breedte",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "enable_custom_height": {
          "label": "Custom minimale hoogte inschakelen"
        },
        "custom_height": {
          "label": "Custom minimale hoogte"
        },
        "content": {
          "header": "Content"
        },
        "text_position": {
          "label": "Inhoud positie",
          "options__1": {
            "label": "Linksboven"
          },
          "options__2": {
            "label": "Midden boven"
          },
          "options__3": {
            "label": "Rechtsboven"
          },
          "options__4": {
            "label": "Midden links"
          },
          "options__5": {
            "label": "Midden gecentreerd"
          },
          "options__6": {
            "label": "Midden rechts"
          },
          "options__7": {
            "label": "Linksonder"
          },
          "options__8": {
            "label": "Middenonder"
          },
          "options__9": {
            "label": "Rechts onder"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "image_mobile": {
            "label": "Mobiele afbeelding (optioneel)"
          }
        }
      },
      "blocks": {
        "hotspot": {
          "name": "Hotspot",
          "settings": {
            "text": {
              "label": "Tekst"
            },
            "position": {
              "header": "Positie",
              "position_left": {
                "label": "Horizontale positie"
              },
              "position_top": {
                "label": "Verticale positie"
              },
              "position_left_mobile": {
                "label": "Horizontale positie mobiel"
              },
              "position_top_mobile": {
                "label": "Verticale positie mobiel"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Afbeelding specificaties banner"
      }
    },
    "contact_form": {
      "name": "Contactformulier",
      "settings": {
        "paragraph": "De volgende invoervelden zijn vereist om het formulier succesvol te verzenden: E-mail en een Bericht of Tekst invoerveld.",
        "alignment": {
          "label": "Positie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "show_placeholder": {
          "label": "Toon invoerveld placeholders"
        },
        "compact": {
          "label": "Compacte weergave"
        }
      },
      "blocks": {
        "text": {
          "name": "Tekst",
          "settings": {
            "title": {
              "label": "Tekst label"
            },
            "placeholder": {
              "label": "Tekst placeholder"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        },
        "textarea": {
          "name": "Bericht",
          "settings": {
            "title": {
              "label": "Bericht label"
            },
            "placeholder": {
              "label": "Bericht placeholder"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        },
        "email": {
          "name": "E-mail",
          "settings": {
            "title": {
              "label": "E-mail label"
            },
            "placeholder": {
              "label": "E-mail placeholder"
            }
          }
        },
        "tel": {
          "name": "Telefoon",
          "settings": {
            "title": {
              "label": "Telefoon label"
            },
            "placeholder": {
              "label": "Telefoon placeholder"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        },
        "checkbox": {
          "name": "Selectievakje",
          "settings": {
            "paragraph": "Je kunt maximaal 6 opties invullen. Wil je minder opties, vul dan minder in. Niet alle 6 zijn nodig.",
            "title": {
              "label": "Selectievakje label"
            },
            "options__1": {
              "label": "Eerste optie"
            },
            "options__2": {
              "label": "Tweede optie"
            },
            "options__3": {
              "label": "Derde optie"
            },
            "options__4": {
              "label": "Vierde optie"
            },
            "options__5": {
              "label": "Vijfde optie"
            },
            "options__6": {
              "label": "Zesde optie"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        },
        "radio": {
          "name": "Radio knoppen",
          "settings": {
            "paragraph": "Je kunt maximaal 6 opties invullen. Wil je minder opties, vul dan minder in. Niet alle 6 zijn nodig.",
            "title": {
              "label": "Radio knoppen label"
            },
            "options__1": {
              "label": "Eerste optie"
            },
            "options__2": {
              "label": "Tweede optie"
            },
            "options__3": {
              "label": "Derde optie"
            },
            "options__4": {
              "label": "Vierde optie"
            },
            "options__5": {
              "label": "Vijfde optie"
            },
            "options__6": {
              "label": "Zesde optie"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        },
        "select": {
          "name": "Dropdown",
          "settings": {
            "title": {
              "label": "Dropdown label"
            },
            "options__1": {
              "label": "Optie 1"
            },
            "options__2": {
              "label": "Optie 2"
            },
            "options__3": {
              "label": "Optie 3"
            },
            "options__4": {
              "label": "Optie 4"
            },
            "options__5": {
              "label": "Optie 5"
            },
            "options__6": {
              "label": "Optie 6"
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        }
      },
      "presets": {
        "name": "Contactformulier"
      }
    },
    "counters": {
      "name": "Tellers",
      "settings": {
        "image": {
          "label": "Achtergrondafbeelding",
          "info": "Gebruik voor de beste resultaten met volledige achtergrondinstelling een afbeelding met een beeldverhouding van 3:2. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208060-section-counters)"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "text_position": {
          "label": "Inhoud positie",
          "info": "Customize your content using blocks.",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          },
          "options__3": {
            "label": "Rechts"
          }
        },
        "numbers_boxed_overlay_opacity": {
          "label": "Achtergrond voor de tellers doorzichtigheid"
        },
        "numbers_accent_color": {
          "label": "Toon de tellercijfers in accentkleur"
        }
      },
      "blocks": {
        "counter": {
          "name": "Teller",
          "settings": {
            "number": {
              "label": "Titel"
            },
            "text": {
              "label": "Beschrijving"
            }
          }
        }
      },
      "presets": {
        "name": "Tellers"
      }
    },
    "countdown": {
      "name": "Countdown",
      "settings": {
        "image": {
          "label": "Afbeelding"
        },
        "video": {
          "label": "Video"
        },
        "overlay_opacity": {
          "label": "Afbeelding/video overlay doorzichtigheid"
        },
        "height": {
          "label": "Hoogte",
          "options__1": {
            "label": "Extra klein"
          },
          "options__2": {
            "label": "Klein"
          },
          "options__3": {
            "label": "Gemiddeld"
          },
          "options__4": {
            "label": "Groot"
          }
        },
        "width": {
          "label": "Breedte",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "colors": {
          "header": "Kleuren",
          "countdown_bg_color": {
            "label": "Timer"
          },
          "countdown_text_color": {
            "label": "Timer cijfer"
          }
        },
        "content": {
          "header": "Content",
          "text_position": {
            "label": "Inhoud positie",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Gecentreerd"
            }
          }
        },
        "countdown": {
          "header": "Countdown timer",
          "countdown_start": {
            "label": "Startdatum + tijd",
            "info": "Wanneer wil je dat de countdown begint? Voor die tijd wordt de banner niet getoond."
          },
          "countdown_end": {
            "label": "Einddatum + tijd"
          },
          "hide_banner_after_countdown": {
            "label": "Verberg banner na countdown"
          },
          "ended_title": {
            "label": "Titel na countdown"
          },
          "ended_text": {
            "label": "Beschrijving na countdown"
          }
        }
      },
      "presets": {
        "name": "Countdown"
      }
    },
    "logo_list": {
      "name": "Merklogo diavoorstelling",
      "settings": {
        "number_of_items": {
          "label": "Logo's per rij",
          "info": "Als de lijst meer logo's bevat dan weergegeven, wordt dit automatisch een slider. Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208061-section-logo-list)"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "logo": {
          "name": "Logo",
          "settings": {
            "image": {
              "label": "Logo",
              "info": "230 x 90px .png aanbevolen."
            },
            "image_svg": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier(https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Merklogo diavoorstelling"
      }
    },
    "newsletter": {
      "name": "Newsletter",
      "settings": {
        "image": {
          "label": "Achtergrondafbeelding (optional)",
          "info": "Gebruik voor de beste resultaten een afbeelding met een beeldverhouding van 3:2. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208062-section-newsletter)"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "checkbox": {
          "header": "Selectievakje",
          "enable_newsletter_terms_checkbox": {
            "label": "Toon custom selectievakje"
          },
          "newsletter_terms_text": {
            "label": "Label voor het selectievakje",
            "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "presets": {
        "name": "Newsletter"
      }
    },
    "recently_viewed_products": {
      "name": "Recent bekeken producten",
      "settings": {
        "number_of_items": {
          "label": "Producten per rij",
          "info": "Als de lijst meer producten bevat dan weergegeven, wordt dit automatisch een slider. Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208063-section-recently-viewed-products)"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "presets": {
        "name": "Recent bekeken producten"
      }
    },
    "blog_posts": {
      "name": "Blog berichten",
      "settings": {
        "blog": {
          "label": "Blog"
        },
        "number_of_items": {
          "label": "Blog berichten per rij",
          "info": "Vanaf 5 blogberichten genereert het een andere lay-out. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208065-section-blog-posts)"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "blog_posts": {
          "header": "Blog berichten",
          "show_image": {
            "label": "Toon uitgelichte afbeelding"
          },
          "show_date": {
            "label": "Toon datum"
          },
          "show_author": {
            "label": "Toon auteur"
          },
          "show_excerpt": {
            "label": "Toon uittreksel"
          },
          "button_style_post": {
            "label": "Knop styling"
          }
        }
      },
      "presets": {
        "name": "Blog berichten"
      }
    },
    "rich_text": {
      "name": "Tekst met opmaak",
      "settings": {
        "overlay_opacity": {
          "label": "Achtergrond doorzichtigheid"
        },
        "text_alignment": {
          "label": "Tekstuitlijning",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "content_width": {
          "label": "Inhoudsbreedte",
          "info": "Wanneer je 1280px kiest, wordt de inhoud volledig breed. De breedte van de afbeelding/video kan apart worden geselecteerd binnen de blokinstellingen."
        },
        "height": {
          "label": "Banner hoogte",
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Gemiddeld"
          },
          "options__3": {
            "label": "Groot"
          }
        },
        "width": {
          "label": "Bannerbreedte",
          "options__1": {
            "label": "Normale breedte"
          },
          "options__2": {
            "label": "Volledige breedte"
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "title": {
          "name": "Titel"
        },
        "content": {
          "name": "Content",
          "settings": {
            "page": {
              "label": "Content"
            },
            "text": {
              "label": "Tekst"
            }
          }
        },
        "button": {
          "name": "Knop"
        },
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        }
      },
      "presets": {
        "name": "Tekst met opmaak"
      }
    },
    "icon_text_blocks": {
      "name": "Meerdere kolommen",
      "settings": {
        "items_width": {
          "label": "Kolommen per rij"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "mobile": {
          "layout_mobile": {
            "label": "Lay-out",
            "options__1": {
              "label": "Slider"
            },
            "options__2": {
              "label": "In rijen"
            }
          }
        },
        "blocks": {
          "header": "Kolommen",
          "blocks_text_alignment": {
            "label": "Uitlijning kolommen",
            "info": "Pas deze sectie aan met blokken",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Gecentreerd"
            }
          },
          "blocks_title_size": {
            "label": "Titelgrootte kolommen"
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "text": {
          "name": "Kolom",
          "settings": {
            "icon": {
              "label": "Icon",
              "info": "Icoon gebruikt accentkleur voor accenten",
              "options__1": {
                "label": "Geen icoon"
              },
              "options__2": {
                "label": "Groep"
              },
              "options__3": {
                "label": "Notificatie"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Geverifieerd"
              },
              "options__6": {
                "label": "Vrachtwagen"
              },
              "options__7": {
                "label": "Afbeelding"
              },
              "options__8": {
                "label": "Telefoongesprek"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Winkeltas"
              },
              "options__11": {
                "label": "Wereldwijde verzending"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Doos"
              },
              "options__14": {
                "label": "Levering doos"
              },
              "options__15": {
                "label": "Statistiek"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "E-mail"
              },
              "options__18": {
                "label": "Munt"
              },
              "options__19": {
                "label": "24-uurs klok"
              },
              "options__20": {
                "label": "Vraag"
              },
              "options__21": {
                "label": "24/7 bereikbaar"
              },
              "options__22": {
                "label": "Tekstballonnen"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobiele betaling"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Veilig"
              }
            },
            "image": {
              "label": "Aangepaste afbeelding",
              "info": "110 x 110px .png aanbevolen"
            },
            "image_svg": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "image_height": {
              "label": "Afbeelding hoogte"
            },
            "title": {
              "label": "Titel"
            },
            "text": {
              "label": "Tekst"
            },
            "link_color": {
              "label": "Text link kleur",
              "options__1": {
                "label": "Tekst"
              },
              "options__2": {
                "label": "Primaire knop"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Meerdere kolommen"
      }
    },
    "testimonials": {
      "name": "Getuigenissen",
      "settings": {
        "layout": {
          "label": "Getuigenissen layout",
          "options__1": {
            "label": "Diavoorstelling"
          },
          "options__2": {
            "label": "Rijen"
          }
        },
        "items_width": {
          "label": "Getuigenissen per rij"
        },
        "autoplay": {
          "label": "Automatisch afspelen inschakelen"
        },
        "autoplay_seconds": {
          "label": "Seconden tussen dia's"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "testimonials": {
          "header": "Getuigenissen"
        },
        "testimonials_boxed_overlay_opacity": {
          "label": "Achtergrond doorzichtigheid"
        },
        "color_scheme_score": {
          "label": "Kleur score"
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "testimonial": {
          "name": "Getuigenis",
          "settings": {
            "text": {
              "label": "Tekst"
            },
            "score": {
              "label": "Score"
            },
            "author": {
              "label": "Auteur",
              "info": "Laat leeg als de auteur anoniem is."
            },
            "place": {
              "label": "Titel",
              "info": "Functie, plaats, land, rol, datum."
            }
          }
        }
      },
      "presets": {
        "name": "Getuigenissen"
      }
    },
    "featured_collection": {
      "name": "Uitgelichte collectie",
      "settings": {
        "collection": {
          "label": "Collectie"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "In Rijen"
          }
        },
        "limit": {
          "label": "Maximaal producten te tonen"
        },
        "number_of_items": {
          "label": "Producten per rij",
          "info": "Als de lijst meer producten bevat dan weergegeven, wordt dit automatisch een slider. Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208069-section-featured-collection)"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "info": "Als ‘Naast de producten’ is geselecteerd, worden er max. 3 producten per rij getoond.",
          "options__1": {
            "label": "Naast de producten"
          },
          "options__2": {
            "label": "Links"
          },
          "options__3": {
            "label": "Gecentreerd"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out"
        }
      },
      "presets": {
        "name": "Uitgelichte collectie"
      }
    },
    "featured_products": {
      "name": "Uitgelichte producten",
      "settings": {
        "products": {
          "label": "Producten"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "In Rijen"
          }
        },
        "number_of_items": {
          "label": "Producten per rij",
          "info": "Als de lijst meer producten bevat dan weergegeven, wordt dit automatisch een slider. Swipe wordt automatisch geactiveerd op mobiel. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208069-section-featured-collection)"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "info": "Als ‘Naast de producten’ is geselecteerd, worden er max. 3 producten per rij getoond.",
          "options__1": {
            "label": "Naast de producten"
          },
          "options__2": {
            "label": "Links"
          },
          "options__3": {
            "label": "Gecentreerd"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out"
        }
      },
      "presets": {
        "name": "Uitgelichte producten"
      }
    },
    "featured_product": {
      "name": "Uitgelicht product",
      "settings": {
        "product": {
          "label": "Product"
        }
      },
      "presets": {
        "name": "Uitgelicht product"
      }
    },
    "usp_bar": {
      "name": "Informatiebalk",
      "settings": {
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Diavoorstelling"
          },
          "options__2": {
            "label": "Rijen"
          }
        },
        "autoplay": {
          "label": "Automatisch afspelen inschakelen"
        },
        "autoplay_seconds": {
          "label": "Seconden tussen dia's"
        },
        "show_arrows": {
          "label": "Pijlen tonen"
        },
        "mobile": {
          "header": "Mobiele lay-out"
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Titel"
            }
          }
        }
      },
      "presets": {
        "name": "Informatiebalk"
      }
    },
    "events_calendar": {
      "name": "Evenementenkalendar",
      "settings": {
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "blocks": {
        "event": {
          "name": "Evenement",
          "settings": {
            "image": {
              "label": "Afbeelding"
            },
            "date_color_palette": {
              "label": "Datum label kleur"
            },
            "date": {
              "label": "Datum"
            },
            "time": {
              "label": "Tijd"
            },
            "location": {
              "label": "Locatie"
            },
            "location_url": {
              "label": "Locatie link"
            },
            "mobile": {
              "header": "Mobiele lay-out"
            }
          }
        }
      },
      "presets": {
        "name": "Evenementenkalendar"
      }
    },
    "faq": {
      "name": "Inklapbare inhoud",
      "settings": {
        "alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "compact": {
          "label": "Compacte weergave"
        }
      },
      "blocks": {
        "text": {
          "name": "Inklapbare rij",
          "settings": {
            "faq_question": {
              "label": "Titel",
              "info": "Voeg een kop toe die de inhoud uitlegt."
            },
            "icon": {
              "label": "Icon",
              "info": "Icoon gebruikt accentkleur voor accenten",
              "options__1": {
                "label": "Geen icoon"
              },
              "options__2": {
                "label": "Groep"
              },
              "options__3": {
                "label": "Notificatie"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Geverifieerd"
              },
              "options__6": {
                "label": "Vrachtwagen"
              },
              "options__7": {
                "label": "Afbeelding"
              },
              "options__8": {
                "label": "Telefoongesprek"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Winkeltas"
              },
              "options__11": {
                "label": "Wereldwijde verzending"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Doos"
              },
              "options__14": {
                "label": "Levering doos"
              },
              "options__15": {
                "label": "Statistiek"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "E-mail"
              },
              "options__18": {
                "label": "Munt"
              },
              "options__19": {
                "label": "24-uurs klok"
              },
              "options__20": {
                "label": "Vraag"
              },
              "options__21": {
                "label": "24/7 bereikbaar"
              },
              "options__22": {
                "label": "Tekstballonnen"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobiele betaling"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Veilig"
              }
            },
            "header_image": {
              "label": "Aangepaste afbeelding",
              "info": "110 x 110px .png aanbevolen"
            },
            "header_image_svg": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Maximale afbeeldingsbreedte"
            },
            "content": {
              "header": "Inhoud",
              "paragraph": "Alle inhoud kan statisch of met metavelden worden gevuld. Alle inhoud kan afzonderlijk of samen in dit blok worden gebruikt. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)",
              "faq_answer": {
                "label": "Tekst"
              },
              "page": {
                "label": "Pagina-inhoud"
              },
              "image": {
                "label": "Afbeelding"
              },
              "image_width": {
                "label": "Afbeeldingsbreedte"
              }
            },
            "custom_code": {
              "header": "Custom code",
              "liquid": {
                "label": "Liquid"
              },
              "html": {
                "label": "HTML"
              }
            },
            "form": {
              "header": "Contactformulier",
              "show_contact_form": {
                "label": "Toon contactformulier",
                "info": "Een standaard contactformulier zal onder de inhoud verschijnen."
              }
            }
          }
        }
      },
      "presets": {
        "name": "Inklapbare inhoud"
      }
    },
    "custom_liquid": {
      "name": "Aangepaste Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Aangepaste Liquid",
          "info": "Voeg app-fragmenten of andere Liquid-code toe om geavanceerde aanpassingen te maken."
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "presets": {
        "name": "Aangepaste Liquid"
      }
    },
    "custom_html_snippet": {
      "name": "Aangepaste HTML",
      "settings": {
        "custom_html_snippet": {
          "label": "Aangepaste HTML",
          "info": "Voeg app-fragmenten, embed-code of andere HTML-code toe om geavanceerde aanpassingen te maken."
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      },
      "presets": {
        "name": "Aangepaste HTML"
      }
    },
    "spacer": {
      "name": "Marge",
      "settings": {
        "height": {
          "label": "Hoogte"
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "height_mobile": {
            "label": "Hoogte"
          }
        }
      },
      "presets": {
        "name": "Spacing"
      }
    },
    "shop_the_look": {
      "name": "Shop the look",
      "settings": {
        "sticky": {
          "label": "Sticky scroll inschakelen"
        },
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "layout": {
          "label": "Lay-out",
          "options__1": {
            "label": "Afbeelding links, producten rechts"
          },
          "options__2": {
            "label": "Producten links, afbeelding rechts"
          }
        },
        "banner_width": {
          "label": "Afbeelding breedte",
          "options__1": {
            "label": "1/2"
          },
          "options__2": {
            "label": "2/3"
          },
          "options__3": {
            "label": "3/4"
          }
        },
        "products": {
          "label": "Producten"
        },
        "products_per_row": {
          "label": "Producten per rij",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out"
        }
      },
      "presets": {
        "name": "Shop the look"
      }
    },
    "image_with_banners": {
      "name": "Afbeelding met banners",
      "settings": {
        "design": {
          "label": "Design",
          "options__1": {
            "label": "Scheiding"
          },
          "options__2": {
            "label": "Achtergrond"
          }
        },
        "image": {
          "label": "Afbeelding"
        },
        "overlay_opacity_img": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "color_palette": {
          "label": "Afbeelding kleurenpalet"
        },
        "color_palette_banners": {
          "label": "Banners kleurenpalet",
          "info": "Alleen van toepassing voor de eerste banner en voor banners met een achtergrond."
        },
        "content": {
          "header": "Inhoud",
          "enable_content": {
            "label": "Toon inhoud op afbeelding",
            "info": "Dit zal de eerste banner overschrijven."
          },
          "text_position": {
            "label": "Inhoud positie",
            "options__1": {
              "label": "Linksboven"
            },
            "options__2": {
              "label": "Midden boven"
            },
            "options__3": {
              "label": "Rechtsboven"
            },
            "options__4": {
              "label": "Midden links"
            },
            "options__5": {
              "label": "Midden gecentreerd"
            },
            "options__6": {
              "label": "Midden rechts"
            },
            "options__7": {
              "label": "Linksonder"
            },
            "options__8": {
              "label": "Middenonder"
            },
            "options__9": {
              "label": "Rechts onder"
            }
          }
        },
        "layout_mobile": {
          "label": "Mobiele Lay-out",
          "mobile_image": {
            "label": "Mobiele afbeelding (optioneel)"
          },
          "mobile_height": {
            "info": "Alleen van toepassing wanneer de eerste banner is uitgeschakeld."
          },
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "In rijen"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Image banner",
          "show_banner": {
            "label": "Toon banner"
          },
          "product": {
            "label": "Product",
            "info": "Selecteer een product of de inhoud kan hieronder worden ingevuld."
          },
          "height": {
            "label": "Aspect ratio"
          }
        }
      },
      "presets": {
        "name": "Image with banners"
      }
    }
  },
  "static_sections": {
    "announcement_bar": {
      "name": "Aankondigingsbalk",
      "settings": {
        "paragraph": "De aankondigingsbalk blijft zichtbaar in de thema-editor na het klikken op het 'sluit'-icoon. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208055-header-settings)",
        "color_palette": {
          "label": "Kleurenschema",
          "options__1": {
            "label": "Lichtste"
          },
          "options__2": {
            "label": "Donkerste"
          },
          "options__3": {
            "label": "Licht"
          },
          "options__4": {
            "label": "Donker"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent licht"
          },
          "options__7": {
            "label": "Kleurverloop licht"
          },
          "options__8": {
            "label": "Kleurverloop donker"
          }
        },
        "text": {
          "label": "Tekst"
        }
      }
    },
    "header": {
      "name": "Header",
      "settings": {
        "layout": {
          "label": "Lay-out",
          "info": "Lay-out 7 en 8 tonen de menuknop niet, meer informatie over header lay-outs is [hier](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings) te vinden.",
          "options__1": {
            "label": "Logo links, uitgebreide zoekbalk",
            "group": "Logo links"
          },
          "options__2": {
            "label": "Logo links, variant uitgebreide zoekbalk",
            "group": "Logo links"
          },
          "options__3": {
            "label": "Logo links, compacte zoekbalk rechts",
            "group": "Logo links"
          },
          "options__4": {
            "label": "Logo links, uitgebreide zoekbalk rechts",
            "group": "Logo links"
          },
          "options__5": {
            "label": "Logo links, in-header navigatie, compacte zoekbalk",
            "group": "Logo links"
          },
          "options__6": {
            "label": "Logo gecentreerd, compacte zoekbalk",
            "group": "Logo gecentreerd"
          },
          "options__7": {
            "label": "Logo gecentreerd, uitgebreide zoekbalk",
            "group": "Logo gecentreerd"
          },
          "options__8": {
            "label": "Logo gecentreerd, in-header navigatie, compacte zoekbalk",
            "group": "Logo gecentreerd"
          }
        },
        "width": {
          "label": "Breedte",
          "info": "Wanneer je 2000px kiest, zal de header de volledige breedte in beslag nemen."
        },
        "header_height": {
          "label": "Minimale hoogte",
          "options__1": {
            "label": "Klein"
          },
          "options__2": {
            "label": "Gemiddeld"
          },
          "options__3": {
            "label": "Groot"
          }
        },
        "show_header_border": {
          "label": "Toon header lijn",
          "options__1": {
            "label": "Boven"
          },
          "options__2": {
            "label": "Onder"
          },
          "options__3": {
            "label": "Boven en onder"
          }
        },
        "header_border_opacity": {
          "label": "Header lijn doorzichtigheid"
        },
        "header_border_width": {
          "label": "Header lijnen breedte"
        },
        "header_shadow": {
          "label": "Toon schaduw"
        },
        "header_shadow_opacity": {
          "label": "Header schaduw doorzichtigheid"
        },
        "header_icons": {
          "label": "Toon tekst in plaats van iconen",
          "info": "Het account icoon, winkelwagen icoon, compacte zoek icoon, de valutakiezer en de taalkiezer (indien gebruikt) worden vervangen door tekst."
        },
        "colors": {
          "header": "Kleuren",
          "top_bar_color": {
            "label": "Bovenste balk kleurenschema"
          },
          "header_color": {
            "label": "Hoofdbalk kleurenschema"
          },
          "menubar_color": {
            "label": "Menu bar kleurenschema"
          }
        },
        "top_bar": {
          "header": "Bovenste balk",
          "show_top_bar": {
            "label": "Bovenste balk inschakelen"
          },
          "top_bar_height": {
            "label": "Balk hoogte",
            "options__1": {
              "label": "Extra klein"
            },
            "options__2": {
              "label": "Klein"
            },
            "options__3": {
              "label": "Gemiddeld"
            },
            "options__4": {
              "label": "Groot"
            }
          },
          "top_bar_font": {
            "label": "Bovenste balk lettertype"
          },
          "top_bar_font_size": {
            "label": "Bovenste balk lettergrootte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            },
            "options__4": {
              "label": "Extra groot"
            }
          },
          "enable_country_selector": {
            "label": "Landenkiezer weergeven"
          },
          "enable_language_selector": {
            "label": "Taalkiezer weergeven"
          },
          "navbar_menu_item_text": {
            "label": "Extra menu-item in bovenste balk"
          },
          "navbar_menu_item_icon": {
            "label": "Icoon"
          },
          "navbar_menu_item_link": {
            "label": "Link"
          }
        },
        "information": {
          "header": "Bovenste balk informatie",
          "enable_usp_slider": {
            "label": "Toon in diavoorstelling"
          },
          "enable_usp_slider_arrows": {
            "label": "Toon diavoorstelling pijlen"
          },
          "enable_usp_slider_autoplay": {
            "label": "Automatisch draaien inschakelen"
          },
          "usp_slider_autoplay_seconds": {
            "label": "Seconden tussen dia's"
          },
          "paragraph": "Voeg informatie toe met behulp van blokken in het sectiegebied."
        },
        "enable_country_selector": {
          "label": "Landenkiezer weergeven"
        },
        "enable_language_selector": {
          "label": "Taalkiezer weergeven"
        },
        "searchbar": {
          "header": "Zoekbalk",
          "searchbar_font_size": {
            "label": "Zoekbalk lettergrootte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            },
            "options__4": {
              "label": "Extra groot"
            }
          },
          "searchbar_height": {
            "label": "Zoekbalk hoogte"
          },
          "searchbar_width": {
            "label": "Zoekbalk breedte",
            "info": "Niet van toepassing voor een gecentreerde zoekbalk of een compact zoek-icoon. Wanneer je 600px kiest, zal de zoekbalk de volledige breedte in beslag nemen."
          },
          "show_searchbar_border": {
            "label": "Toon zoekbalk rand"
          }
        },
        "sticky_header": {
          "header": "Sticky header",
          "enable_sticky_header": {
            "label": "Sticky header inschakelen"
          },
          "sticky": {
            "label": "Sticky header of menu",
            "info": "Sticky menu is alleen van toepassing wanneer een lay-out met menubalk is ingeschakeld en 'Activeer het scrolleffect' is uitgeschakeld.",
            "options__1": {
              "label": "Sticky menu"
            },
            "options__2": {
              "label": "Sticky header"
            }
          }
        },
        "cart_icon": {
          "header": "Winkelwagen icoon",
          "cart_icon": {
            "label": "Winkelwagen icoon",
            "options__1": {
              "label": "Mand"
            },
            "options__2": {
              "label": "Tas"
            },
            "options__3": {
              "label": "Winkelwagen"
            }
          },
          "cart_icon_color": {
            "label": "Winkelwagen items indicator kleurenschema"
          },
          "show_cart_total": {
            "label": "Toon totaalprijs"
          }
        },
        "navigation_menu": {
          "header": "Navigatie menu",
          "menubar_menu": {
            "label": "Menu"
          },
          "menubar_height": {
            "label": "Menubalk hoogte",
            "options__1": {
              "label": "Klein"
            },
            "options__2": {
              "label": "Gemiddeld"
            },
            "options__3": {
              "label": "Groot"
            }
          },
          "menu_layout": {
            "label": "Menu type",
            "options__1": {
              "label": "Mega menu"
            },
            "options__2": {
              "label": "Dropdown menu"
            }
          },
          "menu_scroll": {
            "label": "Activeer het scrolleffect",
            "info": "Alleen van toepassing als 'Uitlijning menu items' is ingesteld op 'Links'."
          },
          "menu_compact": {
            "label": "Ruimte tussen menu-items verkleinen"
          },
          "show_dropdown_images": {
            "label": "Collectieafbeeldingen weergeven in de menu's",
            "info": "Toon de collectieafbeelding vóór de titel."
          },
          "show_dropdown_images_subs": {
            "label": "Toon collectieafbeeldingen in submenu's"
          },
          "show_dropdown_images_autofill": {
            "label": "Collectieafbeeldingen automatisch aanvullen",
            "info": "Gebruik automatisch de eerste productafbeelding wanneer de collectieafbeelding ontbreekt."
          },
          "show_dropdown_images_rounded": {
            "label": "Toon collectieafbeeldingen in een cirkel"
          },
          "menubar_alignment": {
            "label": "Uitlijning menu items",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Gecentreerd"
            },
            "options__3": {
              "label": "Rechts"
            },
            "options__4": {
              "label": "Verdeel gelijkmatig"
            }
          },
          "main_menu_items_clickable": {
            "label": "Hoofdmenu-items klikbaar"
          },
          "menu_show_chevrons": {
            "label": "Pijltjes weergeven voor submenu-items"
          },
          "sale_highlight": {
            "label": "Sale menu-item uitlichten"
          },
          "sale_highlight_item": {
            "label": "Sale menu-item"
          }
        },
        "megamenu": {
          "header": "Megamenu",
          "custom_menu_items_per_columns": {
            "label": "Handmatige kolomindeling",
            "info": "Standaard worden de items in het megamenu verdeeld in kolommen op basis van het aantal items, maar dit kan worden overschreven door hieronder een aangepast aantal items per kolom in te stellen."
          },
          "menu_items_limit": {
            "label": "Menu items per kolom",
            "info": "Let op dat submenu-items niet in afzonderlijke kolommen worden verdeeld."
          },
          "submenu_items_limit": {
            "label": "Submenu-items per kolom",
            "info": "Na dit submenu-item wordt er een 'toon meer' link gebruikt in het megamenu"
          },
          "main_menu_show_lines": {
            "label": "Kolomrandlijnen weergeven"
          }
        },
        "menu_typography": {
          "header": "Menu items",
          "menu_font": {
            "label": "Lettertype"
          },
          "menu_font_weight": {
            "label": "Lettertype gewicht"
          },
          "menu_font_size": {
            "label": "Lettergrootte"
          },
          "menu_letter_spacing": {
            "label": "Letter spacing"
          },
          "menubar_caps": {
            "label": "Menu-items in HOOFDLETTERS"
          }
        },
        "menu_subs_typography": {
          "header": "Submenu-items",
          "enable_menu_subs_font_size": {
            "label": "Aangepaste lettergrootte"
          }
        },
        "socials": {
          "header": "Socials",
          "enable_socials": {
            "label": "Social media-accounts weergeven",
            "info": "Voor meer informatie over hoe je jouw sociale media-accounts kunt koppelen, klik je [hier](https://intercom.help/someoneyouknow/en/articles/6208055-header-settings)."
          }
        },
        "trustmark": {
          "header": "Trustmark",
          "extra_image": {
            "label": "Afbeelding",
            "info": "Max. 150 x 90px .png of .svg aanbevolen."
          },
          "extra_image_width": {
            "label": "Maximale afbeeldingsbreedte"
          },
          "extra_image_link": {
            "label": "Trustmark link"
          }
        },
        "online_store": {
          "header": "Online winkel",
          "disable_online_store": {
            "label": "Winkelwagen en account uitschakelen"
          },
          "disable_account_dropdown": {
            "label": "Account dropdown uitschakelen"
          }
        },
        "button": {
          "enable_button": {
            "info": "Standaard linkt de knop naar de login pagina en overschrijft het account icoon, maar dit gedrag kan worden overschreven door een link aan de knop toe te voegen."
          },
          "button_url": {
            "label": "Link (optioneel)",
            "info": "Dit zal de knop link naar de login pagina overschrijven."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "layout_mobile": {
            "label": "Header lay-out",
            "options__1": {
              "label": "Logo links"
            },
            "options__2": {
              "label": "Logo gecentreerd"
            },
            "options__3": {
              "label": "Logo rechts"
            }
          },
          "mobile_menu_color": {
            "label": "Menu kleurenschema"
          },
          "menu_icon": {
            "label": "Menu icoon",
            "options__1": {
              "label": "Primair"
            },
            "options__2": {
              "label": "Secundair"
            },
            "options__3": {
              "label": "Tertiair"
            }
          },
          "search_compact_mobile": {
            "label": "Compact zoek-icoon inschakelen"
          },
          "search_mobile_bd": {
            "label": "Toon zoekbalk lijn",
            "options__1": {
              "label": "Geen"
            },
            "options__2": {
              "label": "Boven"
            },
            "options__3": {
              "label": "Onder"
            },
            "options__4": {
              "label": "Boven en onder"
            }
          },
          "searchbar_height_mobile": {
            "label": "Zoekbalk hoogte"
          },
          "spacing_mobile": {
            "info": "Alleen van toepassing op niet-transparante header en wanneer de eerste sectie geen volledige breedte sectie is."
          }
        }
      },
      "blocks": {
        "liquid": {
          "name": "Aangepaste Liquid",
          "settings": {
            "liquid": {
              "label": "Aangepaste Liquid",
              "info": "Voeg app-fragmenten of andere Liquid-code toe om geavanceerde aanpassingen te maken."
            },
            "location": {
              "label": "Locatie",
              "options__1": {
                "label": "Top bar"
              },
              "options__2": {
                "label": "Hoofdbalk"
              }
            },
            "show_mobile": {
              "label": "Toon op mobiel"
            }
          }
        },
        "usp": {
          "name": "Bovenste balk informatie",
          "settings": {
            "usp": {
              "label": "Titel",
              "info": "Wanneer de tekstregel langer is dan de breedte van een mobiel scherm aankan, wordt er een link 'meer info' weergegeven die een pop-up opent. Wrap je tekst tussen | om de accent kleur te gebruiken. Bijvoorbeeld: Titel |vervolg| tekst."
            },
            "show_check": {
              "label": "Toon vinkje icoon"
            },
            "image": {
              "label": "Aangepaste afbeelding"
            },
            "image_width": {
              "label": "Afbeelding breedte"
            }
          }
        },
        "button_menu": {
          "name": "Menuknop",
          "settings": {
            "show_mobile": {
              "label": "Gebruik dit menu in mobiele navigatie"
            },
            "button_menu": {
              "label": "Menu"
            },
            "button_color_palette": {
              "label": "Knop kleurenschema",
              "options__7": {
                "label": "Positief"
              },
              "options__8": {
                "label": "Koopknop"
              },
              "options__9": {
                "label": "Dynamische koopknop"
              }
            },
            "button_style": {
              "label": "Knop styling",
              "options__1": {
                "label": "Gevuld"
              },
              "options__2": {
                "label": "Niet gevuld"
              }
            }
          }
        },
        "promo_banners": {
          "name": "Menu subs met promo banner",
          "settings": {
            "menu_item": {
              "label": "Menu item",
              "info": "Kopieer en plak de naam van een item dat is aangemaakt in je menu om een dropdown-menu te make.n [Lees meer](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings#h_4fb96ad5c6)"
            },
            "text_alignment": {
              "label": "Titelpositie",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Gecentreerd"
              }
            },
            "layout": {
              "label": "Banner lay-out",
              "info": "Alleen van toepassing wanneer beide banners zijn ingeschakeld.",
              "options__1": {
                "label": "Kolommen"
              },
              "options__2": {
                "label": "Rijen"
              }
            },
            "banner_1": {
              "header": "Eerste banner",
              "enable_banner_1": {
                "label": "Eerste banner inschakelen"
              }
            },
            "banner_2": {
              "header": "Tweede banner",
              "enable_banner_2": {
                "label": "Tweede banner inschakelen"
              }
            }
          }
        },
        "promo_collection_list": {
          "name": "Menu promo collecties",
          "settings": {
            "menu_item": {
              "label": "Menu item",
              "info": "Kopieer en plak de naam van een item dat is aangemaakt in je menu om een dropdown-menu te maken. [Lees meer](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings#h_4fb96ad5c6)"
            }
          }
        }
      }
    },
    "footer": {
      "name": "Footer",
      "settings": {
        "footer_color": {
          "label": "Hoofdkleurenschema",
          "options__1": {
            "label": "Lichtste"
          },
          "options__2": {
            "label": "Donkerste"
          },
          "options__3": {
            "label": "Licht"
          },
          "options__4": {
            "label": "Donker"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent licht"
          },
          "options__7": {
            "label": "Kleurverloop licht"
          },
          "options__8": {
            "label": "Kleurverloop donker"
          }
        },
        "bottom_bar": {
          "header": "Onderste balk",
          "bottom_color": {
            "label": "Kleurenschema",
            "options__1": {
              "label": "Lichtste"
            },
            "options__2": {
              "label": "Donkerste"
            },
            "options__3": {
              "label": "Licht"
            },
            "options__4": {
              "label": "Accent licht"
            },
            "options__5": {
              "label": "Accent"
            },
            "options__6": {
              "label": "Donker"
            },
            "options__7": {
              "label": "Kleurverloop licht"
            },
            "options__8": {
              "label": "Kleurverloop donker"
            }
          },
          "bottom_img": {
            "label": "Afbeelding",
            "info": "Een logo of een keurmerk wordt vaak gebruikt voor de footer-onderbalk."
          },
          "bottom_img_width": {
            "label": "Afbeelding breedte"
          },
          "bottom_img_link": {
            "label": "Link"
          },
          "enable_country_selector": {
            "label": "Landenkiezer weergeven",
            "info": "Zorg ervoor dat 'landenkiezer weergeven' is ingeschakeld in de header instellingen."
          },
          "enable_language_selector": {
            "label": "Taalkiezer weergeven",
            "info": "Zorg ervoor dat 'taalkiezer weergeven' is ingeschakeld in de header instellingen."
          },
          "enable_follow_on_shop": {
            "label": "'Volgen op Shop' inschakelen",
            "info": "Klanten kunnen de winkel alleen vanuit de webshop volgen in de Shop-app als Shop Pay is ingeschakeld. [Meer informatie](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
          }
        },
        "payment_methods": {
          "header": "Betaalmethodes",
          "show_payment_methods": {
            "label": "Toon betaalmethodes"
          }
        }
      },
      "blocks": {
        "menu": {
          "name": "Menu",
          "settings": {
            "menu": {
              "label": "Menu"
            },
            "split_menu": {
              "label": "Splits menu op",
              "info": "Dit toont een groot menu in twee delen, waarbij de overige blokken altijd eerst getoond worden."
            }
          }
        },
        "text": {
          "name": "Tekst",
          "settings": {
            "title": {
              "label": "Titel"
            },
            "text": {
              "label": "Tekst"
            }
          }
        },
        "html": {
          "name": "HTML",
          "settings": {
            "html": {
              "label": "HTML"
            }
          }
        },
        "socials_newsletter": {
          "name": "Socials & nieuwsbrief",
          "settings": {
            "title": {
              "label": "Titel"
            },
            "show_socials": {
              "label": "Toon social media iconen"
            },
            "enable_follow_on_shop": {
              "label": "'Volgen op Shop' inschakelen",
              "info": "Klanten kunnen de winkel alleen vanuit de webshop volgen in de Shop-app als Shop Pay is ingeschakeld. [Meer informatie](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
            },
            "newsletter": {
              "header": "Nieuwsbrief"
            },
            "show_newsletter": {
              "label": "Nieuwsbrief tonen"
            },
            "footer_button_style": {
              "label": "Button stijl"
            },
            "checkbox": {
              "header": "Selectievakje",
              "enable_newsletter_terms_checkbox": {
                "label": "Toon custom selectievakje"
              },
              "newsletter_terms_text": {
                "label": "Label voor het selectievakje",
                "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
              }
            }
          }
        },
        "customer_support": {
          "name": "Klantenservice",
          "settings": {
            "title": {
              "label": "Titel"
            },
            "customer_support_img": {
              "label": "Afbeelding",
              "info": "Gebruik voor het beste resultaat een afbeelding met een max. breedte van 320 pixels."
            },
            "customer_support_img_width": {
              "label": "Afbeelding breedte"
            },
            "customer_support_offset_right": {
              "label": "Afbeelding afstand rechts"
            },
            "customer_support_offset_bottom": {
              "label": "Afbeelding afstand onder"
            },
            "extra_customer_support": {
              "label": "Tekst",
              "info": "Wil je andere informatie over je bedrijf tonen? Je kunt bijvoorbeeld de openingstijden laten zien."
            },
            "show_phone_link": {
              "label": "Toon belknop"
            },
            "show_mail_link": {
              "label": "Toon e-mailknop"
            },
            "show_whatsapp_link": {
              "label": "Toon WhatsApp-knop"
            },
            "mobile": {
              "header": "Mobiele lay-out",
              "customer_support_offset_right_mobile": {
                "label": "Afbeelding afstand rechts"
              },
              "customer_support_offset_bottom_mobile": {
                "label": "Afbeelding afstand onder"
              }
            }
          }
        }
      }
    },
    "breadcrumbs": {
      "name": "Kruimelpad",
      "settings": {
        "hide_mobile": {
          "label": "Verbergen op mobiel"
        }
      }
    },
    "product_recommendations": {
      "name": "Productaanbevelingen",
      "settings": {
        "paragraph": "Dynamische aanbevelingen gebruiken bestel- en productinformatie om in de loop van de tijd te veranderen en te verbeteren. [Lees meer](https://help.shopify.com/en/themes/development/recommended-products#recommendation-logic)",
        "recommended_products_qty": {
          "label": "Maximaal producten te tonen"
        },
        "number_of_items": {
          "label": "Producten per rij"
        },
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "mobile_collapse": {
            "label": "Standaard inklappen"
          }
        }
      }
    },
    "product_reviews": {
      "name": "Product reviews",
      "settings": {
        "text_alignment": {
          "label": "Titelpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "mobile_collapse": {
            "label": "Standaard inklappen"
          }
        }
      }
    },
    "page_service_menu": {
      "name": "Menu",
      "settings": {
        "menu": {
          "label": "Menu",
          "info": "Maak een menu met al je infopagina's. [Lees meer](https://intercom.help/someoneyouknow/en/collections/3431583-theme-xtra-2-0-for-shopify-manuals)"
        }
      }
    },
    "page_service_info_blocks": {
      "name": "Informatie banner",
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "information": {
          "name": "Informatie banner",
          "settings": {
            "color_palette": {
              "label": "Kleurenschema",
              "options__1": {
                "label": "Lichtste"
              },
              "options__2": {
                "label": "Donkerste"
              },
              "options__3": {
                "label": "Licht"
              },
              "options__4": {
                "label": "Accent licht"
              },
              "options__5": {
                "label": "Accent"
              },
              "options__6": {
                "label": "Donker"
              },
              "options__7": {
                "label": "Kleurverloop licht"
              },
              "options__8": {
                "label": "Kleurverloop donker"
              }
            },
            "title": {
              "label": "Titel"
            },
            "text": {
              "label": "Tekst"
            },
            "show_login_links": {
              "label": "Aanmelding- en registratielinks voor klanten weergeven"
            },
            "show_shop_address": {
              "label": "Toon winkeladres"
            },
            "show_maps_url": {
              "label": "Toon een link naar Google Maps na het adres"
            },
            "show_phone_link": {
              "label": "Toon belknop"
            },
            "show_mail_link": {
              "label": "Toon e-mailknop"
            },
            "show_whatsapp_link": {
              "label": "Toon WhatsApp-knop"
            }
          }
        }
      }
    },
    "google_maps": {
      "name": "Google Maps",
      "settings": {
        "google_maps_iframe": {
          "label": "Google Maps embed code",
          "info": "Toon de locatie van je winkel in Google Maps. De embed code vind je door in Google Maps op je winkel te zoeken, op 'Delen' te klikken, en dan voor 'Een kaart insluiten' te kiezen. [Lees meer](https://support.google.com/maps/answer/144361)"
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        }
      }
    },
    "sticky_add_to_cart": {
      "name": "Sticky winkelwagenbalk",
      "settings": {
        "enable": {
          "label": "Sticky winkelwagenbalk inschakelen",
          "info": "Niet van toepassing op producten met meer dan 250 varianten."
        },
        "position": {
          "label": "Positie",
          "options__1": {
            "label": "Boven"
          },
          "options__2": {
            "label": "Onder"
          }
        },
        "button_style": {
          "label": "Knop styling"
        },
        "show_variant_picker": {
          "label": "Toon variantenkiezer"
        },
        "show_unavailable_variants": {
          "label": "Toon niet-beschikbare varianten"
        },
        "show_amount_selection": {
          "label": "Toon aantal selectie"
        },
        "preorder": {
          "label": "Toon pre-order knop"
        },
        "show_tax": {
          "label": "Toon 'incl.- of excl. BTW' tekst"
        },
        "hide_in_theme_editor": {
          "label": "Verberg sticky winkelwagenbalk in de thema-editor"
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "show_mobile": {
            "label": "Toon op mobiel"
          }
        }
      }
    }
  },
  "main": {
    "product": {
      "name": "Product",
      "settings": {
        "thumbs": {
          "header": "Miniaturen",
          "show_thumbs_desktop": {
            "label": "Toon op desktop"
          },
          "show_thumbs_mobile": {
            "label": "Toon op mobiel"
          },
          "images_layout": {
            "label": "Miniaturen lay-out",
            "options__1": {
              "label": "Verticaal"
            },
            "options__2": {
              "label": "Horizontaal"
            }
          }
        },
        "product_description": {
          "header": "Productbeschrijving",
          "show_product_description": {
            "label": "Toon productbeschrijving"
          },
          "product_description_enable_read_more": {
            "label": "Toon 'lees meer' link"
          }
        },
        "pros_and_cons": {
          "header": "Voor- en nadelen",
          "pros": {
            "label": "Voordelen"
          },
          "cons": {
            "label": "Nadelen"
          },
          "paragraph": "Voor- en nadelen kan worden ingesteld met metavelden. Meer informatie over metavelden en deze functie is [hier](https://intercom.help/someoneyouknow/en/articles/6208042-productpage) te vinden."
        },
        "specifications": {
          "header": "Product specificaties",
          "specifications": {
            "label": "Specificaties"
          },
          "paragraph": "Productspecificaties kunnen worden ingesteld met metavelden. Meer informatie over metavelden en deze functie is [hier](https://intercom.help/someoneyouknow/en/articles/6208042-productpage) te vinden."
        },
        "spacing": {
          "header": "Marge",
          "spacing_bottom_desktop": {
            "label": "Marge onder desktop"
          },
          "spacing_bottom_mobile": {
            "label": "Marge onder mobiel"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "product_description_mobile_collapse": {
            "label": "Productbeschrijving standaard inklappen"
          },
          "mobile_tabs_title_size": {
            "label": "Tabbladen titelgrootte"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "custom_liquid": {
          "name": "Aangepaste Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Aangepaste Liquid",
              "info": "Voeg app-fragmenten of andere Liquid-code toe om geavanceerde aanpassingen te maken."
            }
          }
        },
        "custom_html_snippet": {
          "name": "Aangepaste HTML",
          "settings": {
            "custom_html_snippet": {
              "label": "Aangepaste HTML",
              "info": "Voeg app-fragmenten, embed-code of andere HTML-code toe om geavanceerde aanpassingen te maken."
            }
          }
        },
        "title": {
          "name": "Titel",
          "settings": {
            "layout": {
              "label": "Lay-out",
              "info": "Titel is alleen van toepassing als blok wanneer 'rechts' is gekozen.",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Rechts"
              }
            },
            "title_size": {
              "label": "Titelgrootte",
              "options__1": {
                "label": "Klein"
              },
              "options__2": {
                "label": "Gemiddeld"
              },
              "options__3": {
                "label": "Groot"
              },
              "options__4": {
                "label": "Extra groot"
              }
            },
            "title": {
              "label": "Aangepaste titel",
              "info": "Laat dit leeg om de producttitel weer te geven"
            },
            "vendor": {
              "header": "Verkoper",
              "show_vendor_brand": {
                "label": "Toon 'Verkoper' label"
              },
              "show_vendor_name": {
                "label": "Toon naam verkoper"
              },
              "paragraph": "Het wordt als volgt weergegeven wanneer beide zijn geselecteerd: Verkoper Naam verkoper"
            },
            "product_rating": {
              "header": "Productbeoordelingen",
              "show_product_rating": {
                "label": "Productbeoordelingen weergeven",
                "info": "De beoordelingen worden automatisch weergegeven vanuit je beoordelingsapp"
              }
            },
            "share_buttons": {
              "header": "Deel knoppen",
              "share_whatsapp": {
                "label": "Toon WhatsApp icoon"
              },
              "share_facebook": {
                "label": "Toon Facebook icoon"
              },
              "share_twitter": {
                "label": "Toon X icoon"
              },
              "share_pinterest": {
                "label": "Toon Pinterest icoon"
              },
              "share_messenger": {
                "label": "Toon Messenger icoon"
              },
              "share_email": {
                "label": "Toon E-mail icoon"
              }
            },
            "mobile": {
              "header": "Lay-out op mobiel",
              "info": "Dit verandert de positie van de producttitel op basis van de hoofdproductafbeelding.",
              "layout_mobile": {
                "label": "Lay-out",
                "options__1": {
                  "label": "Boven"
                },
                "options__2": {
                  "label": "Onder"
                }
              }
            }
          }
        },
        "quantity_rules": {
          "name": "Hoeveelheidsregels",
          "settings": {
            "paragraph": "Hoeveelheidsregels is alleen beschikbaar voor Shopify Plus merchants. [Lees meer](https://help.shopify.com/nl/manual/b2b/catalogs/quantity-pricing)",
            "paragraph-2": "Er wordt een tijdelijke tekst weergegeven in de thema-editor. De tekst wordt vervangen door de werkelijke hoeveelheidsregels wanneer het product is gepubliceerd."
          }
        },
        "volume_pricing": {
          "name": "Volumeprijsstelling",
          "settings": {
            "paragraph": "Volumeprijsstelling instellen is alleen beschikbaar voor Shopify Plus merchants. [Lees meer](https://help.shopify.com/nl/manual/b2b/catalogs/quantity-pricing)",
            "paragraph-2": "Er wordt een tijdelijke tekst weergegeven in de thema-editor. De tekst wordt vervangen door de werkelijke volumeprijsstelling wanneer het product is gepubliceerd."
          }
        },
        "short_description": {
          "name": "Korte productbeschrijving",
          "settings": {
            "show_read_more": {
              "label": "Toon een 'lees meer'-link die naar de productbeschrijving scrolt"
            },
            "paragraph": "Bekijk de algemene thema-instellingen om een aangepaste korte beschrijving in te stellen. Je vindt de instellingen onder het tabblad 'Producten'.",
            "text": {
              "label": "Aangepaste korte productbeschrijving",
              "info": "Laat dit leeg om de standaard korte beschrijvingstitel weer te geven"
            }
          }
        },
        "price": {
          "name": "Prijs",
          "settings": {
            "show_tax": {
              "label": "Toon 'incl.- of excl. BTW' tekst"
            }
          }
        },
        "codes": {
          "name": "Productcodes",
          "settings": {
            "show_sku": {
              "label": "Toon SKU"
            },
            "show_barcode": {
              "label": "Toon barcode"
            }
          }
        },
        "inventory": {
          "name": "Voorraad & levertijd",
          "settings": {
            "paragraph": "Je vindt de instellingen in de algemene thema-instellingen onder het tabblad 'Producten'."
          }
        },
        "upsell": {
          "name": "Bulk upsell",
          "settings": {
            "heading_products": "Producten",
            "heading_colors": "Kleuren",
            "color_scheme": {
              "label": "Kleurenschema",
              "lightest": {
                "label": "Lichtste"
              },
              "darkest": {
                "label": "Donkerste"
              },
              "light": {
                "label": "Licht"
              },
              "dark": {
                "label": "Donker"
              },
              "accent": {
                "label": "Accent"
              },
              "accent_light": {
                "label": "Accent licht"
              },
              "gradient_light": {
                "label": "Kleurverloop licht"
              },
              "gradient_dark": {
                "label": "Kleurverloop donker"
              }
            },
            "button_color_scheme": {
              "label": "Knop kleurenschema"
            },
            "variant_selector_color_scheme": {
              "label": "Variant kiezer kleurenschema"
            },
            "product_list": {
              "label": "Producten"
            },
            "visible_products": {
              "label": "Toon producten"
            },
            "show_out_of_stock_products": {
              "label": "Laat uitverkochten producten zien"
            },
            "variant_in_popup": {
              "label": "Toon varianten in pop-up",
              "info": "Hierdoor wordt een pop-up ‘Kies variant’ geopend in plaats van een variantkiezer op de productkaart."
            },
            "header": {
              "label": "Inhoud"
            },
            "heading_position": {
              "label": "Titelpositie",
              "options__left": {
                  "label": "Links"
              },
              "options__center": {
                  "label": "Rechts"
              }
            },
            "heading_size": {
              "label": "Titelgrootte",
              "options__small": {
                  "label": "Klein"
              },
              "options__medium": {
                  "label": "Gemiddeld"
              },
              "options__large": {
                  "label": "Groot"
              },
              "options__extra_large": {
                  "label": "Extra groot"
              }
            },
            "heading": {
              "label": "Titel"
            }
          }
        },
        "variant_selection": {
          "name": "Variantkiezer",
          "selection_type": {
            "label": "Kiezer type",
            "options__1": {
              "label": "Dropdown"
            },
            "options__2": {
              "label": "Knoppen"
            }
          },
          "options": {
            "label": "Losse optiekiezers inschakelen",
            "info": "[Combined listing](https://apps.shopify.com/combined-listings) producten en producten met meer dan 250 varianten tonen altijd losse optiekiezers."
          },
          "show_variant_images": {
            "label": "Afbeeldingen in varianten weergeven"
          },
          "show_unavailable_variants": {
            "label": "Toon niet-beschikbare varianten",
            "info": "Niet-beschikbare varianten kunnen alleen verborgen worden wanneer losse optiekiezers niet van toepassing zijn op het product."
          },
          "show_single_options": {
            "label": "Toon opties waarbij maar één variant beschikbaar is"
          },
          "enable_selling_plans": {
            "label": "Abonnementen inschakelen",
            "info": "Voor meer informatie over hoe abonnementen ingesteld kunnen worden [klik hier](https://help.shopify.com/nl/manual/products/purchase-options/subscriptions)."
          }
        },
        "urgency": {
          "name": "Voorraad urgentie",
          "settings": {
              "color_palette": {
                "lightest": {
                  "label": "Lichtste"
                },
                "darkest": {
                  "label": "Donkerste"
                },
                "light": {
                  "label": "Licht"
                },
                "dark": {
                  "label": "Donker"
                },
                "accent": {
                  "label": "Accent"
                },
                "accent_light": {
                  "label": "Accent licht"
                },
                "gradient_light": {
                  "label": "Kleurverloop licht"
                },
                "gradient_dark": {
                  "label": "Kleurverloop donker"
                }
            },
            "stock": {
              "header": "Voorraad",
              "product": {
                "label": "Product"
              },
              "min_stock": {
                "label": "Toon de schaarstebalk wanneer de variantinventaris eronder staat",
                "info": "De voorraadschaarstebalk wordt alleen weergegeven als het voorraadniveau onder dit bedrag ligt. Of gebruik onderstaande metavelden."
              },
              "min_stock_meta": {
                "label": "Metafield voorraad",
                "info": "Dit overschrijft ‘toon schaarstebalk wanneer de variantvoorraad lager is’ hierboven. Leer [hier](https://help.shopify.com/en/manual/metafields) hoe u dit metaveld kunt gebruiken."
              },
              "show_level": {
                "label": "Toon het huidige voorraadbedrag van de variant",
                "info": "Dit toont het huidige voorraadbedrag in de schaarstebalk. Tip: je kunt ook de variabele *inventaris* gebruiken in je onderstaande tekst."
              },
              "show_bar_unavailable": {
                "label": "Toon balk wanneer de variantvoorraad 0 of lager is",
                "info": "Wanneer de voorraad leeg is, kunt u ervoor kiezen een lege balk weer te geven of te verbergen. Wanneer u het toont, gebruik dan het onderstaande bericht dat het niet op voorraad is."
              },
              "show_bar_available_nostock": {
                "label": "Toon balk wanneer de variant niet op voorraad is en nog steeds te koop is.",
                "info": "Toon balk wanneer de productvariant 0 of lager is dan 0 en het selectievakje 'Doorgaan met verkopen wanneer niet op voorraad' is ingeschakeld in de productinstellingen in het Shopify-beheercentrum."
              }
            },
            "layout": {
              "header": "Indeling",
              "layout": {
                "label": "Indeling van voorraadschaarste",
                "small": {
                  "label": "Kleine balk"
                },
                "medium": {
                  "label": "Grootte balk"
                }
              },
              "position_stock_message": {
                "label": "Toon voorraadbericht",
                "above": {
                  "label": "Boven de balk"
                },
                "below": {
                  "label": "Onder de balk"
                }
              }
            },
            "colors": {
              "header": "Kleuren",
              "enable_background": {
                "label": "Achtergrond inschakelen"
              },
              "color_palette": {
                "label": "Kleurenschema"
              },
              "color_progressbar": {
                "label": "Bar voor voorraadschaarste",
                "info": "Gebruikt voor effen achtergrondkleur."
              },
              "gradient_progressbar": {
                "label": "De gradiënt van de voorraadschaarstebalk",
                "info": "Het verloop heeft voorrang op de effen achtergrondkleur."
              },
              "background_progress_bar": {
                "label": "Kleur voortgangsbalk",
                "info": "Dit is de achtergrondkleur van de voortgangsbalk."
              },
              "tooltip_background": {
                "label": "Tooltip-achtergrond"
              },
              "tooltip_text_color": {
                "label": "Tooltip-labeltekst"
              },
              "show_background": {
                "label": "Achtergrondkleur weergeven"
              }
            },
            "content": {
              "header": "Inhoud",
              "message": {
                "label": "Voorraad berichttekst",
                "info": "Gebruik de variabele *inventory* om uw huidige voorraadniveau weer te geven, zoals hierboven weergegeven in het standaardbericht."
              },
              "outofstock_message": {
                "label": "Berichttekst niet op voorraad"
              }
            }
          }
        },
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Informatie"
            }
          }
        },
        "trustbadge": {
          "name": "Trustbadge",
          "settings": {
            "paragraph": "Je vindt de instellingen in de algemene thema-instellingen onder het tabblad 'Trustbadge'."
          }
        },
        "shipping_timer": {
          "name": "Verzendtimer",
          "settings": {
            "paragraph": "Je vindt de instellingen in de algemene thema-instellingen onder het tabblad 'Verzending en bezorging'."
          }
        },
        "content": {
          "name": "Content",
          "settings": {
            "enable_tab": {
              "label": "Toon in accordeon"
            },
            "collapse": {
              "label": "Accordeon standaard inklappen"
            },
            "title_size": {
              "info": "Alleen van toepassing als 'Toon in accordeon' niet is ingeschakeld."
            },
            "title": {
              "label": "Titel"
            },
            "icon": {
              "label": "Icon",
              "info": "Icoon gebruikt accentkleur voor accenten",
              "options__1": {
                "label": "Geen icoon"
              },
              "options__2": {
                "label": "Groep"
              },
              "options__3": {
                "label": "Notificatie"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Geverifieerd"
              },
              "options__6": {
                "label": "Vrachtwagen"
              },
              "options__7": {
                "label": "Afbeelding"
              },
              "options__8": {
                "label": "Telefoongesprek"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Winkeltas"
              },
              "options__11": {
                "label": "Wereldwijde verzending"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Doos"
              },
              "options__14": {
                "label": "Levering doos"
              },
              "options__15": {
                "label": "Statistiek"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "E-mail"
              },
              "options__18": {
                "label": "Munt"
              },
              "options__19": {
                "label": "24-uurs klok"
              },
              "options__20": {
                "label": "Vraag"
              },
              "options__21": {
                "label": "24/7 bereikbaar"
              },
              "options__22": {
                "label": "Tekstballonnen"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobiele betaling"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Veilig"
              }
            },
            "header_image": {
              "label": "Aangepaste afbeelding",
              "info": "110 x 110px .png aanbevolen"
            },
            "header_image_svg": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Maximale afbeeldingsbreedte"
            },
            "content": {
              "header": "Inhoud",
              "paragraph": "Alle inhoud kan statisch of met metavelden worden gevuld. Alle inhoud kan afzonderlijk of samen in dit blok worden gebruikt. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)",
              "text": {
                "label": "Tekst"
              },
              "page": {
                "label": "Pagina-inhoud"
              },
              "image": {
                "label": "Afbeelding"
              },
              "image_width": {
                "label": "Afbeeldingsbreedte"
              }
            },
            "custom_code": {
              "header": "Custom code",
              "liquid": {
                "label": "Liquid"
              },
              "html": {
                "label": "HTML"
              }
            },
            "form": {
              "header": "Contactformulier",
              "show_contact_form": {
                "label": "Toon contactformulier",
                "info": "Een standaard contactformulier zal onder de inhoud verschijnen."
              }
            }
          }
        },
        "complementary_products": {
          "name": "Complementaire producten",
          "settings": {
            "paragraph": "Dit blok werkt alleen met de Shopify Search & Discovery-app. Als er geen aanvullende producten zijn ingesteld, worden nep-producten getoond (enkel in de theme-editor). Voeg de Search & Discovery-app toe om aanvullende producten te selecteren. [Lees meer](https://help.shopify.com/nl/manual/online-store/search-and-discovery/product-recommendations)",
            "enable_tab": {
              "label": "Toon in accordeon"
            },
            "collapse": {
              "label": "Accordeon standaard inklappen"
            },
            "title": {
              "label": "Titel"
            },
            "icon": {
              "label": "Icon",
              "info": "Icoon gebruikt accentkleur voor accenten",
              "options__1": {
                "label": "Geen icoon"
              },
              "options__2": {
                "label": "Groep"
              },
              "options__3": {
                "label": "Notificatie"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Geverifieerd"
              },
              "options__6": {
                "label": "Vrachtwagen"
              },
              "options__7": {
                "label": "Afbeelding"
              },
              "options__8": {
                "label": "Telefoongesprek"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Winkeltas"
              },
              "options__11": {
                "label": "Wereldwijde verzending"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Doos"
              },
              "options__14": {
                "label": "Levering doos"
              },
              "options__15": {
                "label": "Statistiek"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "E-mail"
              },
              "options__18": {
                "label": "Munt"
              },
              "options__19": {
                "label": "24-uurs klok"
              },
              "options__20": {
                "label": "Vraag"
              },
              "options__21": {
                "label": "24/7 bereikbaar"
              },
              "options__22": {
                "label": "Tekstballonnen"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobiele betaling"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Veilig"
              }
            },
            "header_image": {
              "label": "Aangepaste afbeelding",
              "info": "110 x 110px .png aanbevolen"
            },
            "header_image_svg": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Maximale afbeeldingsbreedte"
            },
            "products": {
              "header": "Producten",
              "layout": {
                "label": "Lay-out",
                "options__1": {
                  "label": "Raster / swipe"
                },
                "options__2": {
                  "label": "Lijst"
                }
              },
              "number_of_items": {
                "label": "Producten per rij"
              }
            },
            "quick_buy": {
              "header": "Quick shop",
              "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
              "enable_quick_buy_desktop": {
                "label": "Toon op desktop"
              },
              "enable_quick_buy_mobile": {
                "label": "Toon op mobiel"
              },
              "enable_quick_buy_drawer": {
                "label": "Quick shop drawer inschakelen",
                "info": "Dit opent een compacte versie van de productpagina in een side drawer."
              },
              "enable_quick_buy_qty_selector": {
                "label": "Toon een hoeveelheidkiezer",
                "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
              },
              "enable_color_picker": {
                "label": "Toon kleurenkiezer"
              },
              "enable_quick_buy_compact": {
                "label": "Compacte knop inschakelen",
                "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
              }
            },
            "products_layout": {
              "header": "Lay-out producten",
              "paragraph": "Toon deze informatie op de productkaarten wanneer van toepassing.",
              "products_show_image": {
                "label": "Toon afbeelding"
              },
              "products_show_labels": {
                "label": "Toon labels"
              },
              "products_show_vendor": {
                "label": "Toon verkoper"
              },
              "products_show_title": {
                "label": "Toon titel"
              },
              "products_show_rating": {
                "label": "Toon beoordelingen"
              },
              "products_show_price": {
                "label": "Toon prijs"
              },
              "products_show_stock": {
                "label": "Toon voorraad"
              }
            }
          }
        },
        "buy_button": {
          "name": "Koopknop",
          "settings": {
            "button_style": {
              "label": "Styling",
              "options__1": {
                "label": "Gevuld"
              },
              "options__2": {
                "label": "Niet gevuld"
              }
            },
            "show_amount_selection": {
              "label": "Toon aantal selectie"
            },
            "show_dynamic_buybutton": {
              "label": "Toon dynamische koopknop",
              "info": "Klanten zien hun favoriete betaalmethode, zoals PayPal of Apple Pay. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/dynamic-checkout)"
            },
            "button_style_dynamic_buybutton": {
              "label": "Dynamische koopknop styling",
              "info": "Niet van toepassing op alle dynamische koopknoppen."
            },
            "preorder": {
              "header": "Pre-order knop",
              "preorder": {
                "label": "Toon pre-order knop",
                "info": "Dit toont een pre-orderknop als koopknop. Alleen van toepassing wanneer het product de instelling 'doorverkopen indien niet op voorraad' actief heeft."
              },
              "preorder_button_text": {
                "label": "Knoplabel"
              },
              "preorder_label": {
                "label": "Bezorging label",
                "info": "Voeg een bezorgbericht toe aan de pre-orderknop met metavelden of algemene tekstinhoud. Als je deze informatie toe wilt voegen aan je e-mailnotificaties, klik [hier](https://community.shopify.com/c/shopify-design/product-pages-get-customization-information-for-products/td-p/616503) voor documentatie."
              },
              "preorder_label_color_palette": {
                "label": "Bezorging label kleurenschema",
                "options__1": {
                  "label": "Lichtste"
                },
                "options__2": {
                  "label": "Donkerste"
                },
                "options__3": {
                  "label": "Licht"
                },
                "options__4": {
                  "label": "Donker"
                },
                "options__5": {
                  "label": "Accent"
                },
                "options__6": {
                  "label": "Accent licht"
                },
                "options__7": {
                  "label": "Gewone knop"
                },
                "options__8": {
                  "label": "Koopknop"
                },
                "options__9": {
                  "label": "Dynamische koopknop"
                }
              },
              "preorder_show_dynamic_buybutton": {
                "label": "Toon dynamische koopknop voor pre-orders"
              }
            },
            "gift_card": {
              "header": "Cadeaubon",
              "show_gift_card_recipient": {
                "label": "Formulier voor gegevens van ontvanger weergeven voor cadeaubonnen",
                "info": "Klanten kunnen cadeaubonnen rechtstreeks naar de ontvanger laten sturen op een geplande datum met een persoonlijk bericht. [Meer informatie](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/add-gift-card-recipient-fields)"
              }
            }
          }
        }
      }
    },
    "collection": {
      "name": "Collection",
      "settings": {
        "layout": {
          "header": "Layout",
          "pagination_qty": {
            "label": "Producten per pagina"
          },
          "pagination_type": {
            "label": "Paginering",
            "options__1": {
              "label": "Pagina's"
            },
            "options__2": {
              "label": "'Toon meer' knop'"
            }
          },
          "show_amount_of_products_in_collection": {
            "label": "Toon totaal aantal producten"
          },
          "productview_type": {
            "label": "Productkaart lay-out",
            "options__1": {
              "label": "Rader"
            },
            "options__2": {
              "label": "Lijst"
            }
          },
          "products_per_row": {
            "label": "Producten per rij",
            "info": "Alleen van toepassing op de productkaart lay-out 'Raster'."
          }
        },
        "show_collection_image": {
          "label": "Toon collectie afbeelding"
        },
        "filters": {
          "header": "Filters en menu uitlijning",
          "enable_filters": {
            "label": "Toon filters"
          },
          "enable_menu": {
            "label": "Toon menu"
          },
          "enable_collections": {
            "label": "Toon collecties"
          },
          "filters_menu_alignment": {
            "label": "Uitlijning",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Rechts"
            }
          },
          "filter_menu": {
            "label": "Menu",
            "info": "Een menu dat zal worden gebruikt om gerelateerde collecties weer te geven. Klik [hier](https://intercom.help/someoneyouknow/en/articles/6208044-collections) voor meer informatie over het samenstellen van dit menu."
          },
          "collections_menu": {
            "label": "Collecties",
            "info": "Selecteer gerelateerde collecties en/of kies een menu hierboven. Gedupliceerde items worden automatisch verborgen."
          },
          "filter_sticky": {
            "label": "Sticky filters inschakelen"
          },
          "filters_show_more_limit": {
            "label": "Schakel de link 'Meer weergeven' in na aantal filters"
          }
        },
        "filter_swatches": {
          "header": "Visuele filters",
          "paragraph": "Voor meer informatie over het instellen van visuele filters [klik hier](https://help.shopify.com/nl/manual/online-store/search-and-discovery/filters#visual-filters).",
          "image_swatches_size": {
            "label": "Afbeeldingsgrootte",
            "options__1": {
              "label": "Extra klein"
            },
            "options__2": {
              "label": "Klein"
            },
            "options__3": {
              "label": "Gemiddeld"
            },
            "options__4": {
              "label": "Groot"
            }
          },
          "image_swatches_show_in_circle": {
            "label": "Toon afbeeldingen in cirkel"
          },
          "image_swatches_fill_images": {
            "label": "Afbeeldingen vullen"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "products_per_row_mobile": {
            "label": "Producten per rij",
            "info": "Alleen van toepassing op de productkaart lay-out 'Raster'.",
            "options__1": {
              "label": "1"
            },
            "options__2": {
              "label": "2"
            }
          },
          "filter_mobile_sticky": {
            "label": "Sticky filterknop inschakelen"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "text_alignment": {
              "label": "Uitlijning",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Rechts"
              }
            },
            "title": {
              "label": "Aangepaste titel",
              "info": "Laat dit leeg om de titel van de collectie weer te geven"
            }
          }
        },
        "description": {
          "name": "Beschrijving",
          "settings": {
            "text_alignment": {
              "label": "Uitlijning",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Rechts"
              }
            },
            "enable_read_more": {
              "label": "Toon 'lees meer' link"
            },
            "text": {
              "label": "Aangepaste beschrijving",
              "info": "Laat dit leeg om de collectiebeschrijving weer te geven"
            }
          }
        },
        "product_grid": {
          "name": "Producten"
        },
        "sorting": {
          "name": "Sorteeropties"
        }
      }
    },
    "search": {
      "name": "Zoekresultaten",
      "settings": {
        "layout": {
          "header": "Lay-out",
          "pagination_qty": {
            "label": "Zoekresultaten per pagina"
          },
          "pagination_type": {
            "label": "Paginering",
            "options__1": {
              "label": "Pagina's"
            },
            "options__2": {
              "label": "'Toon meer' knop'"
            }
          },
          "show_amount_of_search_results": {
            "label": "Toon aantal zoekresultaten"
          },
          "productview_type": {
            "label": "Productkaart lay-out",
            "options__1": {
              "label": "Raster"
            },
            "options__2": {
              "label": "Lijst"
            }
          },
          "products_per_row": {
            "label": "Producten per rij",
            "info": "Alleen van toepassing op de productkaart lay-out 'Raster'."
          }
        },
        "filters": {
          "header": "Filters en menu uitlijning",
          "enable_filters": {
            "label": "Toon filters"
          },
          "enable_menu": {
            "label": "Toon menu"
          },
          "enable_collections": {
            "label": "Toon collecties"
          },
          "filters_menu_alignment": {
            "label": "Uitlijning",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Rechts"
            }
          },
          "filter_menu": {
            "label": "Menu",
            "info": "Een menu dat zal worden gebruikt om gerelateerde collecties weer te geven. Klik [hier](https://intercom.help/someoneyouknow/en/articles/6208044-collections) voor meer informatie over het samenstellen van dit menu."
          },
          "collections_menu": {
            "label": "Collecties",
            "info": "Selecteer gerelateerde collecties en/of kies een menu hierboven. Gedupliceerde items worden automatisch verborgen."
          },
          "filter_sticky": {
            "label": "Sticky filters inschakelen"
          },
          "filters_show_more_limit": {
            "label": "Schakel de link 'Meer weergeven' in na aantal filters"
          }
        },
        "filter_swatches": {
          "header": "Visuele filters",
          "paragraph": "Voor meer informatie over het instellen van visuele filters [klik hier](https://help.shopify.com/nl/manual/online-store/search-and-discovery/filters#visual-filters).",
          "image_swatches_size": {
            "label": "Afbeeldingsgrootte",
            "options__1": {
              "label": "Extra klein"
            },
            "options__2": {
              "label": "Klein"
            },
            "options__3": {
              "label": "Gemiddeld"
            },
            "options__4": {
              "label": "Groot"
            }
          },
          "image_swatches_show_in_circle": {
            "label": "Toon afbeeldingen in cirkel"
          },
          "image_swatches_fill_images": {
            "label": "Afbeeldingen vullen"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "Hierdoor verschijnt er een knop 'toevoegen aan winkelwagen' op je productkaart.",
          "enable_quick_buy_desktop": {
            "label": "Toon op desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Toon op mobiel"
          },
          "enable_quick_buy_drawer": {
            "label": "Quick shop drawer inschakelen",
            "info": "Dit opent een compacte versie van de productpagina in een side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Toon een hoeveelheidkiezer",
            "info": "Niet van toepassing wanneer de quick shop drawer is ingeschakeld."
          },
          "enable_color_picker": {
            "label": "Toon kleurenkiezer"
          },
          "enable_quick_buy_compact": {
            "label": "Compacte knop inschakelen",
            "info": "Dit toont een winkelwagenicoon in plaats van 'Toevoegen aan winkelwagen', of alleen 'Opties' in plaats van 'Bekijk opties'."
          }
        },
        "blog_posts": {
          "header": "Blog berichten",
          "show_image": {
            "label": "Toon uitgelichte afbeelding"
          },
          "show_date": {
            "label": "Toon datum"
          },
          "show_author": {
            "label": "Toon auteur"
          },
          "show_excerpt": {
            "label": "Toon uittreksel"
          },
          "button_style_post": {
            "label": "Knop styling"
          }
        },
        "mobile": {
          "header": "Mobiele lay-out",
          "products_per_row_mobile": {
            "label": "Producten per rij",
            "info": "Alleen van toepassing op de productkaart lay-out 'Raster'.",
            "options__1": {
              "label": "1"
            },
            "options__2": {
              "label": "2"
            }
          },
          "filter_mobile_sticky": {
            "label": "Sticky filterknop inschakeln"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "text_alignment": {
              "label": "Tekst uitlijning",
              "options__1": {
                "label": "Links"
              },
              "options__2": {
                "label": "Gecentreerd"
              }
            }
          }
        },
        "results": {
          "name": "Zoekresultaten"
        },
        "sorting": {
          "name": "Sorteeropties"
        }
      }
    },
    "page": {
      "name": "Pagina",
      "settings": {
        "max_width": {
          "label": "Inhoud maximale breedte",
          "info": "Wanneer je 1280px kiest, zal de inhoud de volledige breedte in beslag nemen."
        },
        "content_alignment": {
          "label": "Inhoud uitlijning",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        },
        "text_alignment": {
          "label": "Tekstpositie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Gecentreerd"
          }
        }
      },
      "blocks": {
        "title": {
          "name": "Titel",
          "settings": {
            "title_size": {
              "label": "Titelgrootte",
              "options__1": {
                "label": "Klein"
              },
              "options__2": {
                "label": "Gemiddeld"
              },
              "options__3": {
                "label": "Groot"
              },
              "options__4": {
                "label": "Extra groot"
              }
            }
          }
        },
        "content": {
          "name": "Content"
        },
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        }
      }
    },
    "blog": {
      "name": "Blog berichten",
      "settings": {
        "title": {
          "label": "Aangepaste titel",
          "info": "Laat leeg om de blogtitel weer te geven."
        },
        "show_tags": {
          "label": "Toon blog tags"
        },
        "pagination_qty": {
          "label": "Blogberichten per pagina"
        },
        "blog_posts": {
          "header": "Blog berichten",
          "show_image": {
            "label": "Toon uitgelichte afbeelding"
          },
          "show_date": {
            "label": "Toon datum"
          },
          "show_author": {
            "label": "Toon auteur"
          },
          "show_excerpt": {
            "label": "Toon uittreksel"
          },
          "button_style_post": {
            "label": "Knop styling"
          }
        }
      }
    },
    "article": {
      "name": "Blogbericht",
      "settings": {
        "big_fontsize": {
          "label": "Grotere lettergrootte gebruiken"
        },
        "compact": {
          "label": "Compacte weergave"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "featured_image": {
          "name": "Uitgelichte afbeelding"
        },
        "excerpt": {
          "name": "Uittreksel"
        },
        "content": {
          "name": "Content"
        },
        "tags": {
          "name": "Tags"
        },
        "comments": {
          "name": "Opmerkingen"
        },
        "title": {
          "name": "Titel, auteur en datum",
          "settings": {
            "show_banner": {
              "label": "Titel weergeven in banner over volledige breedte",
              "info": "Hierdoor wordt je titel in een banner weergegeven op de uitgelichte afbeelding van het blogbericht."
            },
            "overlay_opacity": {
              "label": "Afbeelding overlay doorzichtigheid"
            },
            "color_palette": {
              "label": "Volledige breedte banner kleurenschema",
              "options__1": {
                "label": "Lichtste"
              },
              "options__2": {
                "label": "Donkerste"
              },
              "options__3": {
                "label": "Licht"
              },
              "options__4": {
                "label": "Donker"
              },
              "options__5": {
                "label": "Accent"
              },
              "options__6": {
                "label": "Accent licht"
              },
              "options__7": {
                "label": "Kleurverloop licht"
              },
              "options__8": {
                "label": "Kleurverloop donker"
              }
            },
            "show_date": {
              "label": "Toon datum"
            },
            "show_author": {
              "label": "Toon auteur"
            },
            "show_article_reading_time": {
              "label": "Toon geschatte leestijd"
            }
          }
        },
        "share_buttons": {
          "name": "Knoppen voor sociaal delen",
          "settings": {
            "share_whatsapp": {
              "label": "Toon WhatsApp icoon"
            },
            "share_facebook": {
              "label": "Toon Facebook icoon"
            },
            "share_twitter": {
              "label": "Toon X icoon"
            },
            "share_pinterest": {
              "label": "Toon Pinterest icoon"
            },
            "share_messenger": {
              "label": "Toon Messenger icoon"
            },
            "share_email": {
              "label": "Toon E-mail icoon"
            }
          }
        }
      }
    },
    "list_collection": {
      "name": "Collectielijst",
      "settings": {
        "image_ratio": {
          "label": "Afbeelding ratio",
          "options__1": {
            "label": "Portret"
          },
          "options__2": {
            "label": "Vierkant"
          },
          "options__3": {
            "label": "Landschap"
          }
        },
        "fill_images": {
          "label": "Afbeeldingen vullen"
        },
        "pagination_qty": {
          "label": "Collecties per pagina"
        },
        "number_of_items": {
          "label": "Collecties per rij"
        }
      }
    },
    "cart": {
      "name": "Winkelwagen",
      "settings": {
        "show_dynamic_buybutton": {
          "label": "Toon dynamische koopknop",
          "info": "Klanten zien hun favoriete betaalmethode, zoals PayPal of Apple Pay. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/dynamic-checkout)"
        },
        "enable_discount_tab": {
          "label": "Toon kortingscode inwisselknop"
        },
        "show_payment_methods": {
          "label": "Betaalmethoden tonen"
        },
        "show_order_notes": {
          "label": "Bestelnotities tonen"
        },
        "order_notes_alignment": {
          "label": "Bestelnotities positie",
          "options__1": {
            "label": "Links"
          },
          "options__2": {
            "label": "Rechts"
          }
        },
        "checkbox": {
          "header": "Selectievakje",
          "enable_terms_checkbox": {
            "label": "Toon selectievakje"
          },
          "terms_text": {
            "label": "Label voor het selectievakje",
            "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
          }
        }
      },
      "blocks": {
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Titel"
            }
          }
        },
        "tab": {
          "name": "Tab",
          "settings": {
            "title": {
              "label": "Titel"
            },
            "text": {
              "label": "Inhoud"
            }
          }
        },
        "shipping_timer": {
          "name": "Verzendtimer",
          "settings": {
            "paragraph": "Je vindt de instellingen in de algemene thema-instellingen onder het tabblad 'Verzending en bezorging'."
          }
        }
      }
    },
    "account_dashboard": {
      "name": "Klantendashboard",
      "settings": {
        "show_breadcrumbs": {
          "label": "Toon kruimelpad",
          "options__1": {
            "label": "Op desktop"
          },
          "options__2": {
            "label": "Op mobiel"
          },
          "options__3": {
            "label": "Op desktop en mobiel"
          },
          "options__4": {
            "label": "Niet tonen"
          }
        },
        "banner": {
          "header": "Banner",
          "image": {
            "label": "Afbeelding"
          },
          "overlay_opacity": {
            "label": "Afbeelding overlay doorzichtigheid"
          },
          "color_palette": {
            "label": "Kleurenschema",
            "options__1": {
              "label": "Lichtste"
            },
            "options__2": {
              "label": "Donkerste"
            },
            "options__3": {
              "label": "Licht"
            },
            "options__4": {
              "label": "Donker"
            },
            "options__5": {
              "label": "Accent"
            },
            "options__6": {
              "label": "Accent licht"
            },
            "options__7": {
              "label": "Kleurverloop licht"
            },
            "options__8": {
              "label": "Kleurverloop donker"
            }
          },
          "text_position": {
            "label": "Inhoud positie",
            "options__1": {
              "label": "Links"
            },
            "options__2": {
              "label": "Gecentreerd"
            },
            "options__3": {
              "label": "Rechts"
            }
          }
        },
        "customer_service": {
          "header": "Klantenservice",
          "text": {
            "label": "Tekst"
          },
          "show_phone_link": {
            "label": "Toon belknop"
          },
          "show_mail_link": {
            "label": "Toon e-mailknop"
          },
          "show_whatsapp_link": {
            "label": "Toon WhatsApp-knop"
          }
        }
      }
    },
    "account_login": {
      "name": "Login",
      "blocks": {
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Titel"
            }
          }
        }
      }
    },
    "account_register": {
      "name": "Registreer",
      "blocks": {
        "usp": {
          "name": "Informatie",
          "settings": {
            "usp": {
              "label": "Titel"
            }
          }
        },
        "newsletter_checkbox": {
          "name": "Nieuwsbrief selectievakje",
          "settings": {
            "paragraph": "Pas je formulierteksten aan in de themavertalingen. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "custom_checkbox": {
          "name": "Aangepast selectievakje",
          "settings": {
            "custom_checkbox_text": {
              "label": "Label voor het selectievakje",
              "info": "Plaats bijvoorbeeld een link naar je privacybeleid."
            },
            "required": {
              "label": "Verplicht veld"
            }
          }
        }
      }
    },
    "404": {
      "name": "404 pagina",
      "settings": {
        "image": {
          "label": "Achtergrond",
          "info": "Gebruik voor de beste resultaten een afbeelding met een beeldverhouding van 3:2. [Lees meer](https://intercom.help/someoneyouknow/en/articles/6208050-404-page)"
        },
        "overlay_opacity": {
          "label": "Afbeelding overlay doorzichtigheid"
        },
        "color_palette": {
          "label": "Kleurenschema",
          "options__1": {
            "label": "Lichtste"
          },
          "options__2": {
            "label": "Donkerste"
          },
          "options__3": {
            "label": "Licht"
          },
          "options__4": {
            "label": "Donker"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent licht"
          },
          "options__7": {
            "label": "Kleurverloop licht"
          },
          "options__8": {
            "label": "Kleurverloop donker"
          }
        },
        "video": {
          "label": "Video",
          "info": "Video wordt afgespeeld op de pagina."
        },
        "link_text": {
          "label": "Knop label"
        },
        "link_url": {
          "label": "Link"
        },
        "button_color_palette": {
          "label": "Knop kleurenschema",
          "options__1": {
            "label": "Lichtste"
          },
          "options__2": {
            "label": "Donkerste"
          },
          "options__3": {
            "label": "Licht"
          },
          "options__4": {
            "label": "Donker"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent licht"
          }
        },
        "show_link": {
          "label": "Toon link in plaats van knop"
        }
      }
    },
    "password": {
      "name": "Wachtwoordpagina",
      "settings": {
        "image": {
          "label": "Afbeelding",
          "info": "Aanbevolen afbeeldingverhouding: 2:3"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Marge",
          "settings": {
            "height": {
              "label": "Hoogte"
            }
          }
        },
        "logo": {
          "name": "Logo",
          "settings": {
            "logo": {
              "label": "Logo afbeelding"
            },
            "logo_width": {
              "label": "Logo breedte"
            },
            "svg_logo": {
              "label": "Gebruik .svg-formaat",
              "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            }
          }
        },
        "social_sharing": {
          "name": "Social media iconen"
        },
        "message": {
          "name": "Bericht",
          "settings": {
            "paragraph": "Pas je bericht aan in het gedeelte 'Voorkeuren' van je online winkel. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/password-page#add-password-protection-to-your-online-store)"
          }
        },
        "newsletter_form": {
          "name": "Nieuwsbrief formulier",
          "settings": {
            "paragraph": "Pas je formulierteksten aan in de themavertalingen. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "password_form": {
          "name": "Wachtwoord formulier",
          "settings": {
            "paragraph": "Pas je formulierteksten aan in de themavertalingen. [Lees meer](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "title": {
          "name": "Titel"
        }
      }
    },
    "giftcard": {
      "name": "Cadeaukaart",
      "settings": {
        "image": {
          "label": "Afbeelding",
          "info": "Aanbevolen afbeeldingverhouding: 2:3"
        },
        "logo": {
          "show_logo": {
            "label": "Toon logo / winkelnaam"
          },
          "header": "Logo",
          "logo": {
            "label": "Logo afbeelding"
          },
          "logo_width": {
            "label": "Logo breedte"
          },
          "svg_logo": {
            "label": "Gebruik .svg-formaat",
            "info": "Upload je .svg-bestand in 'Bestanden' in de Shopify-beheerder en plak de url hier in het bestand. Meer informatie over het uploaden van het bestand vind je [hier](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
          },
          "show_logo_giftcard": {
            "label": "Toon logo / winkelnaam op de cadeaukaart"
          },
          "logo_width_on_giftcard": {
            "label": "Logo breedte op de cadeaukaart"
          }
        },
        "giftcard": {
          "header": "Cadeaukaart",
          "giftcard_background_color": {
            "label": "Cadeaukaart achtergrond"
          },
          "giftcard_text_color": {
            "label": "Cadeaukaart tekst"
          }
        }
      }
    }
  }
}
