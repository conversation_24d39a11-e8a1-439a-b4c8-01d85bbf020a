/*!
	Swiper
	Copyright	<PERSON>
	License		MIT
	Version		11.1.15

	https://github.com/nolimits4web/swiper
*/
var Swiper=function(){"use strict";function e(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function t(i,s){void 0===i&&(i={}),void 0===s&&(s={}),Object.keys(s).forEach(l=>{void 0===i[l]?i[l]=s[l]:e(s[l])&&e(i[l])&&Object.keys(s[l]).length>0&&t(i[l],s[l])})}let i={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function s(){let e="undefined"!=typeof document?document:{};return t(e,i),e}let l={document:i,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function e(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function r(){let e="undefined"!=typeof window?window:{};return t(e,l),e}function a(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function n(){return Date.now()}function o(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function p(){let e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){let s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&!d(s)){let l=Object.keys(Object(s)).filter(e=>0>t.indexOf(e));for(let r=0,a=l.length;r<a;r+=1){let n=l[r],c=Object.getOwnPropertyDescriptor(s,n);void 0!==c&&c.enumerable&&(o(e[n])&&o(s[n])?s[n].__swiper__?e[n]=s[n]:p(e[n],s[n]):!o(e[n])&&o(s[n])?(e[n]={},s[n].__swiper__?e[n]=s[n]:p(e[n],s[n])):e[n]=s[n])}}}return e}function c(e,t,i){e.style.setProperty(t,i)}function u(e){let{swiper:t,targetPosition:i,side:s}=e,l=r(),a=-t.translate,n=null,o,d=t.params.speed;t.wrapperEl.style.scrollSnapType="none",l.cancelAnimationFrame(t.cssModeFrameID);let p=i>a?"next":"prev",c=(e,t)=>"next"===p&&e>=t||"prev"===p&&e<=t,u=()=>{o=new Date().getTime(),null===n&&(n=o);let e=Math.max(Math.min((o-n)/d,1),0),r=a+(.5-Math.cos(e*Math.PI)/2)*(i-a);if(c(r,i)&&(r=i),t.wrapperEl.scrollTo({[s]:r}),c(r,i)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[s]:r})}),l.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=l.requestAnimationFrame(u)};u()}function h(e,t){void 0===t&&(t="");let i=[...e.children];return(e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t)?i.filter(e=>e.matches(t)):i}function f(e){try{console.warn(e);return}catch(t){}}function m(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function g(e,t){let i=r();return i.getComputedStyle(e,null).getPropertyValue(t)}function v(e){let t=e,i;if(t){for(i=0;null!==(t=t.previousSibling);)1===t.nodeType&&(i+=1);return i}}function $(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function w(e,t,i){let s=r();return e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom"))}function _(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}let b;function y(){return b||(b=function e(){let t=r(),i=s();return{smoothScroll:i.documentElement&&i.documentElement.style&&"scrollBehavior"in i.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&i instanceof t.DocumentTouch)}}()),b}let T;function S(e){return void 0===e&&(e={}),T||(T=function e(t){let{userAgent:i}=void 0===t?{}:t,s=y(),l=r(),a=l.navigator.platform,n=i||l.navigator.userAgent,o={ios:!1,android:!1},d=l.screen.width,p=l.screen.height,c=n.match(/(Android);?[\s\/]+([\d.]+)?/),u=n.match(/(iPad).*OS\s([\d_]+)/),h=n.match(/(iPod)(.*OS\s([\d_]+))?/),f=!u&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===a;return!u&&m&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${d}x${p}`)>=0&&((u=n.match(/(Version)\/([\d.]+)/))||(u=[0,1,"13_0_0"]),m=!1),c&&"Win32"!==a&&(o.os="android",o.android=!0),(u||f||h)&&(o.os="ios",o.ios=!0),o}(e)),T}let E,x=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},C=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},L=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let s=i.querySelector(`.${e.params.lazyPreloaderClass}`);!s&&e.isElement&&(i.shadowRoot?s=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(s=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&s.remove()})),s&&s.remove()}},P=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},M=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),l=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let r=l,a=[r-t];a.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,i)=>{a.includes(t.column)&&P(e,i)});return}let n=l+s-1;if(e.params.rewind||e.params.loop)for(let o=l-t;o<=n+t;o+=1){let d=(o%i+i)%i;(d<l||d>n)&&P(e,d)}else for(let p=Math.max(l-t,0);p<=Math.min(n+t,i-1);p+=1)p!==l&&(p>n||p<l)&&P(e,p)};function k(e){let{swiper:t,runCallbacks:i,direction:s,step:l}=e,{activeIndex:r,previousIndex:a}=t,n=s;if(n||(n=r>a?"next":r<a?"prev":"reset"),t.emit(`transition${l}`),i&&r!==a){if("reset"===n){t.emit(`slideResetTransition${l}`);return}t.emit(`slideChangeTransition${l}`),"next"===n?t.emit(`slideNextTransition${l}`):t.emit(`slidePrevTransition${l}`)}}function A(e,t,i){let s=r(),{params:l}=e,a=l.edgeSwipeDetection,n=l.edgeSwipeThreshold;return!a||!(i<=n)&&!(i>=s.innerWidth-n)||"prevent"===a&&(t.preventDefault(),!0)}function I(e){let t=this,i=s(),l=e;l.originalEvent&&(l=l.originalEvent);let a=t.touchEventsData;if("pointerdown"===l.type){if(null!==a.pointerId&&a.pointerId!==l.pointerId)return;a.pointerId=l.pointerId}else"touchstart"===l.type&&1===l.targetTouches.length&&(a.touchId=l.targetTouches[0].identifier);if("touchstart"===l.type){A(t,l,l.targetTouches[0].pageX);return}let{params:o,touches:d,enabled:p}=t;if(!p||!o.simulateTouch&&"mouse"===l.pointerType||t.animating&&o.preventInteractionOnTransition)return;!t.animating&&o.cssMode&&o.loop&&t.loopFix();let c=l.target;if("wrapper"===o.touchEventsTarget&&!function e(t,i){let s=i.contains(t);if(!s&&i instanceof HTMLSlotElement){let l=[...i.assignedElements()];return l.includes(t)}return s}(c,t.wrapperEl)||"which"in l&&3===l.which||"button"in l&&l.button>0||a.isTouched&&a.isMoved)return;let u=!!o.noSwipingClass&&""!==o.noSwipingClass,h=l.composedPath?l.composedPath():l.path;u&&l.target&&l.target.shadowRoot&&h&&(c=h[0]);let f=o.noSwipingSelector?o.noSwipingSelector:`.${o.noSwipingClass}`,m=!!(l.target&&l.target.shadowRoot);if(o.noSwiping&&(m?function e(t,i){return void 0===i&&(i=this),function e(i){if(!i||i===s()||i===r())return null;i.assignedSlot&&(i=i.assignedSlot);let l=i.closest(t);return l||i.getRootNode?l||e(i.getRootNode().host):null}(i)}(f,c):c.closest(f))){t.allowClick=!0;return}if(o.swipeHandler&&!c.closest(o.swipeHandler))return;d.currentX=l.pageX,d.currentY=l.pageY;let g=d.currentX,v=d.currentY;if(!A(t,l,g))return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),d.startX=g,d.startY=v,a.touchStartTime=n(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,o.threshold>0&&(a.allowThresholdMove=!1);let $=!0;c.matches(a.focusableElements)&&($=!1,"SELECT"===c.nodeName&&(a.isTouched=!1)),i.activeElement&&i.activeElement.matches(a.focusableElements)&&i.activeElement!==c&&("mouse"===l.pointerType||"mouse"!==l.pointerType&&!c.matches(a.focusableElements))&&i.activeElement.blur();let w=$&&t.allowTouchMove&&o.touchStartPreventDefault;(o.touchStartForcePreventDefault||w)&&!c.isContentEditable&&l.preventDefault(),o.freeMode&&o.freeMode.enabled&&t.freeMode&&t.animating&&!o.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",l)}function z(e){let t=s(),i=this,l=i.touchEventsData,{params:r,touches:a,rtlTranslate:o,enabled:d}=i;if(!d||!r.simulateTouch&&"mouse"===e.pointerType)return;let p=e;if(p.originalEvent&&(p=p.originalEvent),"pointermove"===p.type){if(null!==l.touchId)return;let c=p.pointerId;if(c!==l.pointerId)return}let u;if("touchmove"===p.type){if(!(u=[...p.changedTouches].filter(e=>e.identifier===l.touchId)[0])||u.identifier!==l.touchId)return}else u=p;if(!l.isTouched){l.startMoving&&l.isScrolling&&i.emit("touchMoveOpposite",p);return}let h=u.pageX,f=u.pageY;if(p.preventedByNestedSwiper){a.startX=h,a.startY=f;return}if(!i.allowTouchMove){p.target.matches(l.focusableElements)||(i.allowClick=!1),l.isTouched&&(Object.assign(a,{startX:h,startY:f,currentX:h,currentY:f}),l.touchStartTime=n());return}if(r.touchReleaseOnEdges&&!r.loop){if(i.isVertical()){if(f<a.startY&&i.translate<=i.maxTranslate()||f>a.startY&&i.translate>=i.minTranslate()){l.isTouched=!1,l.isMoved=!1;return}}else if(h<a.startX&&i.translate<=i.maxTranslate()||h>a.startX&&i.translate>=i.minTranslate())return}if(t.activeElement&&t.activeElement.matches(l.focusableElements)&&t.activeElement!==p.target&&"mouse"!==p.pointerType&&t.activeElement.blur(),t.activeElement&&p.target===t.activeElement&&p.target.matches(l.focusableElements)){l.isMoved=!0,i.allowClick=!1;return}l.allowTouchCallbacks&&i.emit("touchMove",p),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=h,a.currentY=f;let m=a.currentX-a.startX,g=a.currentY-a.startY;if(i.params.threshold&&Math.sqrt(m**2+g**2)<i.params.threshold)return;if(void 0===l.isScrolling){let v;i.isHorizontal()&&a.currentY===a.startY||i.isVertical()&&a.currentX===a.startX?l.isScrolling=!1:m*m+g*g>=25&&(v=180*Math.atan2(Math.abs(g),Math.abs(m))/Math.PI,l.isScrolling=i.isHorizontal()?v>r.touchAngle:90-v>r.touchAngle)}if(l.isScrolling&&i.emit("touchMoveOpposite",p),void 0===l.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(l.startMoving=!0),l.isScrolling||"touchmove"===p.type&&l.preventTouchMoveFromPointerMove){l.isTouched=!1;return}if(!l.startMoving)return;i.allowClick=!1,!r.cssMode&&p.cancelable&&p.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&p.stopPropagation();let $=i.isHorizontal()?m:g,w=i.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;r.oneWayMovement&&($=Math.abs($)*(o?1:-1),w=Math.abs(w)*(o?1:-1)),a.diff=$,$*=r.touchRatio,o&&($=-$,w=-w);let _=i.touchesDirection;i.swipeDirection=$>0?"prev":"next",i.touchesDirection=w>0?"prev":"next";let b=i.params.loop&&!r.cssMode,y="next"===i.touchesDirection&&i.allowSlideNext||"prev"===i.touchesDirection&&i.allowSlidePrev;if(!l.isMoved){if(b&&y&&i.loopFix({direction:i.swipeDirection}),l.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){let T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});i.wrapperEl.dispatchEvent(T)}l.allowMomentumBounce=!1,r.grabCursor&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!0),i.emit("sliderFirstMove",p)}let S;if(new Date().getTime(),l.isMoved&&l.allowThresholdMove&&_!==i.touchesDirection&&b&&y&&Math.abs($)>=1){Object.assign(a,{startX:h,startY:f,currentX:h,currentY:f,startTranslate:l.currentTranslate}),l.loopSwapReset=!0,l.startTranslate=l.currentTranslate;return}i.emit("sliderMove",p),l.isMoved=!0,l.currentTranslate=$+l.startTranslate;let E=!0,x=r.resistanceRatio;if(r.touchReleaseOnEdges&&(x=0),$>0?(b&&y&&!S&&l.allowThresholdMove&&l.currentTranslate>(r.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-("auto"!==r.slidesPerView&&i.slides.length-r.slidesPerView>=2?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),l.currentTranslate>i.minTranslate()&&(E=!1,r.resistance&&(l.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+l.startTranslate+$)**x))):$<0&&(b&&y&&!S&&l.allowThresholdMove&&l.currentTranslate<(r.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+("auto"!==r.slidesPerView&&i.slides.length-r.slidesPerView>=2?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===r.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(r.slidesPerView,10)))}),l.currentTranslate<i.maxTranslate()&&(E=!1,r.resistance&&(l.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-l.startTranslate-$)**x))),E&&(p.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&l.currentTranslate<l.startTranslate&&(l.currentTranslate=l.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&l.currentTranslate>l.startTranslate&&(l.currentTranslate=l.startTranslate),i.allowSlidePrev||i.allowSlideNext||(l.currentTranslate=l.startTranslate),r.threshold>0){if(Math.abs($)>r.threshold||l.allowThresholdMove){if(!l.allowThresholdMove){l.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,l.currentTranslate=l.startTranslate,a.diff=i.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{l.currentTranslate=l.startTranslate;return}}r.followFinger&&!r.cssMode&&((r.freeMode&&r.freeMode.enabled&&i.freeMode||r.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),r.freeMode&&r.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(l.currentTranslate),i.setTranslate(l.currentTranslate))}function O(e){let t=this,i=t.touchEventsData,s=e;s.originalEvent&&(s=s.originalEvent);let l,r="touchend"===s.type||"touchcancel"===s.type;if(r){if(!(l=[...s.changedTouches].filter(e=>e.identifier===i.touchId)[0])||l.identifier!==i.touchId)return}else{if(null!==i.touchId||s.pointerId!==i.pointerId)return;l=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)){let o=["pointercancel","contextmenu"].includes(s.type)&&(t.browser.isSafari||t.browser.isWebView);if(!o)return}i.pointerId=null,i.touchId=null;let{params:d,touches:p,rtlTranslate:c,slidesGrid:u,enabled:h}=t;if(!h||!d.simulateTouch&&"mouse"===s.pointerType)return;if(i.allowTouchCallbacks&&t.emit("touchEnd",s),i.allowTouchCallbacks=!1,!i.isTouched){i.isMoved&&d.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,i.startMoving=!1;return}d.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);let f=n(),m=f-i.touchStartTime;if(t.allowClick){let g=s.path||s.composedPath&&s.composedPath();t.updateClickedSlide(g&&g[0]||s.target,g),t.emit("tap click",s),m<300&&f-i.lastClickTime<300&&t.emit("doubleTap doubleClick",s)}if(i.lastClickTime=n(),a(()=>{t.destroyed||(t.allowClick=!0)}),!i.isTouched||!i.isMoved||!t.swipeDirection||0===p.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset){i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;return}i.isTouched=!1,i.isMoved=!1,i.startMoving=!1;let v;if(v=d.followFinger?c?t.translate:-t.translate:-i.currentTranslate,d.cssMode)return;if(d.freeMode&&d.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:v});return}let $=v>=-t.maxTranslate()&&!t.params.loop,w=0,_=t.slidesSizesGrid[0];for(let b=0;b<u.length;b+=b<d.slidesPerGroupSkip?1:d.slidesPerGroup){let y=b<d.slidesPerGroupSkip-1?1:d.slidesPerGroup;void 0!==u[b+y]?($||v>=u[b]&&v<u[b+y])&&(w=b,_=u[b+y]-u[b]):($||v>=u[b])&&(w=b,_=u[u.length-1]-u[u.length-2])}let T=null,S=null;d.rewind&&(t.isBeginning?S=d.virtual&&d.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(T=0));let E=(v-u[w])/_,x=w<d.slidesPerGroupSkip-1?1:d.slidesPerGroup;if(m>d.longSwipesMs){if(!d.longSwipes){t.slideTo(t.activeIndex);return}"next"===t.swipeDirection&&(E>=d.longSwipesRatio?t.slideTo(d.rewind&&t.isEnd?T:w+x):t.slideTo(w)),"prev"===t.swipeDirection&&(E>1-d.longSwipesRatio?t.slideTo(w+x):null!==S&&E<0&&Math.abs(E)>d.longSwipesRatio?t.slideTo(S):t.slideTo(w))}else{if(!d.shortSwipes){t.slideTo(t.activeIndex);return}let C=t.navigation&&(s.target===t.navigation.nextEl||s.target===t.navigation.prevEl);C?s.target===t.navigation.nextEl?t.slideTo(w+x):t.slideTo(w):("next"===t.swipeDirection&&t.slideTo(null!==T?T:w+x),"prev"===t.swipeDirection&&t.slideTo(null!==S?S:w))}}function D(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:l,snapGrid:r}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let n=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=l,e.allowSlideNext=s,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function G(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function B(){let e=this,{wrapperEl:t,rtlTranslate:i,enabled:s}=e;if(!s)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let l,r=e.maxTranslate()-e.minTranslate();(l=0===r?0:(e.translate-e.minTranslate())/r)!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function N(e){L(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function V(){let e=this;!e.documentTouchHandlerProceeded&&(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}let H=(e,t)=>{let i=s(),{params:l,el:r,wrapperEl:a,device:n}=e,o=!!l.nested,d="on"===t?"addEventListener":"removeEventListener",p=t;r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(l.preventClicks||l.preventClicksPropagation)&&r[d]("click",e.onClick,!0),l.cssMode&&a[d]("scroll",e.onScroll),l.updateOnWindowResize?e[p](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",D,!0):e[p]("observerUpdate",D,!0),r[d]("load",e.onLoad,{capture:!0}))},R=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var F={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let Y={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let l=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][l](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function l(){s.off(e,l),l.__emitterProxy&&delete l.__emitterProxy;for(var i=arguments.length,r=Array(i),a=0;a<i;a++)r[a]=arguments[a];t.apply(s,r)}return l.__emitterProxy=t,s.on(e,l,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,l)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(l,1)})}),i},emit(){let e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,i,s;for(var l=arguments.length,r=Array(l),a=0;a<l;a++)r[a]=arguments[a];"string"==typeof r[0]||Array.isArray(r[0])?(t=r[0],i=r.slice(1,r.length),s=e):(t=r[0].events,i=r[0].data,s=r[0].context||e),i.unshift(s);let n=Array.isArray(t)?t:t.split(" ");return n.forEach(t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(e=>{e.apply(s,[t,...i])}),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach(e=>{e.apply(s,i)})}),e}},update:{updateSize:function e(){let t,i,s=this.el;t=void 0!==this.params.width&&null!==this.params.width?this.params.width:s.clientWidth,i=void 0!==this.params.height&&null!==this.params.height?this.params.height:s.clientHeight,!(0===t&&this.isHorizontal()||0===i&&this.isVertical())&&(t=t-parseInt(g(s,"padding-left")||0,10)-parseInt(g(s,"padding-right")||0,10),i=i-parseInt(g(s,"padding-top")||0,10)-parseInt(g(s,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(i)&&(i=0),Object.assign(this,{width:t,height:i,size:this.isHorizontal()?t:i}))},updateSlides:function e(){let t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:l,slidesEl:r,size:a,rtlTranslate:n,wrongRTL:o}=t,d=t.virtual&&s.virtual.enabled,p=d?t.virtual.slides.length:t.slides.length,u=h(r,`.${t.params.slideClass}, swiper-slide`),f=d?t.virtual.slides.length:u.length,m=[],v=[],$=[],_=s.slidesOffsetBefore;"function"==typeof _&&(_=s.slidesOffsetBefore.call(t));let b=s.slidesOffsetAfter;"function"==typeof b&&(b=s.slidesOffsetAfter.call(t));let y=t.snapGrid.length,T=t.slidesGrid.length,S=s.spaceBetween,E=-_,x=0,C=0;if(void 0===a)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*a:"string"==typeof S&&(S=parseFloat(S)),t.virtualSize=-S,u.forEach(e=>{n?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&(c(l,"--swiper-centered-offset-before",""),c(l,"--swiper-centered-offset-after",""));let L=s.grid&&s.grid.rows>1&&t.grid;L?t.grid.initSlides(u):t.grid&&t.grid.unsetSlides();let P,M="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let k=0;k<f;k+=1){P=0;let A;if(u[k]&&(A=u[k]),L&&t.grid.updateSlide(k,A,u),!u[k]||"none"!==g(A,"display")){if("auto"===s.slidesPerView){M&&(u[k].style[t.getDirectionLabel("width")]="");let I=getComputedStyle(A),z=A.style.transform,O=A.style.webkitTransform;if(z&&(A.style.transform="none"),O&&(A.style.webkitTransform="none"),s.roundLengths)P=t.isHorizontal()?w(A,"width"):w(A,"height");else{let D=i(I,"width"),G=i(I,"padding-left"),B=i(I,"padding-right"),N=i(I,"margin-left"),V=i(I,"margin-right"),H=I.getPropertyValue("box-sizing");if(H&&"border-box"===H)P=D+N+V;else{let{clientWidth:R,offsetWidth:F}=A;P=D+G+B+N+V+(F-R)}}z&&(A.style.transform=z),O&&(A.style.webkitTransform=O),s.roundLengths&&(P=Math.floor(P))}else P=(a-(s.slidesPerView-1)*S)/s.slidesPerView,s.roundLengths&&(P=Math.floor(P)),u[k]&&(u[k].style[t.getDirectionLabel("width")]=`${P}px`);u[k]&&(u[k].swiperSlideSize=P),$.push(P),s.centeredSlides?(E=E+P/2+x/2+S,0===x&&0!==k&&(E=E-a/2-S),0===k&&(E=E-a/2-S),.001>Math.abs(E)&&(E=0),s.roundLengths&&(E=Math.floor(E)),C%s.slidesPerGroup==0&&m.push(E),v.push(E)):(s.roundLengths&&(E=Math.floor(E)),(C-Math.min(t.params.slidesPerGroupSkip,C))%t.params.slidesPerGroup==0&&m.push(E),v.push(E),E=E+P+S),t.virtualSize+=P+S,x=P,C+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+b,n&&o&&("slide"===s.effect||"coverflow"===s.effect)&&(l.style.width=`${t.virtualSize+S}px`),s.setWrapperSize&&(l.style[t.getDirectionLabel("width")]=`${t.virtualSize+S}px`),L&&t.grid.updateWrapperSize(P,m),!s.centeredSlides){let Y=[];for(let X=0;X<m.length;X+=1){let W=m[X];s.roundLengths&&(W=Math.floor(W)),m[X]<=t.virtualSize-a&&Y.push(W)}m=Y,Math.floor(t.virtualSize-a)-Math.floor(m[m.length-1])>1&&m.push(t.virtualSize-a)}if(d&&s.loop){let q=$[0]+S;if(s.slidesPerGroup>1){let j=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),U=q*s.slidesPerGroup;for(let K=0;K<j;K+=1)m.push(m[m.length-1]+U)}for(let Z=0;Z<t.virtual.slidesBefore+t.virtual.slidesAfter;Z+=1)1===s.slidesPerGroup&&m.push(m[m.length-1]+q),v.push(v[v.length-1]+q),t.virtualSize+=q}if(0===m.length&&(m=[0]),0!==S){let J=t.isHorizontal()&&n?"marginLeft":t.getDirectionLabel("marginRight");u.filter((e,t)=>!s.cssMode||!!s.loop||t!==u.length-1).forEach(e=>{e.style[J]=`${S}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let Q=0;$.forEach(e=>{Q+=e+(S||0)}),Q-=S;let ee=Q>a?Q-a:0;m=m.map(e=>e<=0?-_:e>ee?ee+b:e)}if(s.centerInsufficientSlides){let et=0;$.forEach(e=>{et+=e+(S||0)}),et-=S;let ei=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(et+ei<a){let es=(a-et-ei)/2;m.forEach((e,t)=>{m[t]=e-es}),v.forEach((e,t)=>{v[t]=e+es})}}if(Object.assign(t,{slides:u,snapGrid:m,slidesGrid:v,slidesSizesGrid:$}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){c(l,"--swiper-centered-offset-before",`${-m[0]}px`),c(l,"--swiper-centered-offset-after",`${t.size/2-$[$.length-1]/2}px`);let el=-t.snapGrid[0],er=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(e=>e+el),t.slidesGrid=t.slidesGrid.map(e=>e+er)}if(f!==p&&t.emit("slidesLengthChange"),m.length!==y&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),v.length!==T&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!d&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let ea=`${s.containerModifierClass}backface-hidden`,en=t.el.classList.contains(ea);f<=s.maxBackfaceHiddenSlides?en||t.el.classList.add(ea):en&&t.el.classList.remove(ea)}},updateAutoHeight:function e(t){let i=this,s=[],l=i.virtual&&i.params.virtual.enabled,r=0,a;"number"==typeof t?i.setTransition(t):!0===t&&i.setTransition(i.params.speed);let n=e=>l?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(a=0;a<Math.ceil(i.params.slidesPerView);a+=1){let o=i.activeIndex+a;if(o>i.slides.length&&!l)break;s.push(n(o))}}else s.push(n(i.activeIndex));for(a=0;a<s.length;a+=1)if(void 0!==s[a]){let d=s[a].offsetHeight;r=d>r?d:r}(r||0===r)&&(i.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function e(){let t=this.slides,i=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=(this.isHorizontal()?t[s].offsetLeft:t[s].offsetTop)-i-this.cssOverflowAdjustment()},updateSlidesProgress:function e(t){void 0===t&&(t=this&&this.translate||0);let i=this,s=i.params,{slides:l,rtlTranslate:r,snapGrid:a}=i;if(0===l.length)return;void 0===l[0].swiperSlideOffset&&i.updateSlidesOffset();let n=-t;r&&(n=t),i.visibleSlidesIndexes=[],i.visibleSlides=[];let o=s.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*i.size:"string"==typeof o&&(o=parseFloat(o));for(let d=0;d<l.length;d+=1){let p=l[d],c=p.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(c-=l[0].swiperSlideOffset);let u=(n+(s.centeredSlides?i.minTranslate():0)-c)/(p.swiperSlideSize+o),h=(n-a[0]+(s.centeredSlides?i.minTranslate():0)-c)/(p.swiperSlideSize+o),f=-(n-c),m=f+i.slidesSizesGrid[d],g=f>=0&&f<=i.size-i.slidesSizesGrid[d],v=f>=0&&f<i.size-1||m>1&&m<=i.size||f<=0&&m>=i.size;v&&(i.visibleSlides.push(p),i.visibleSlidesIndexes.push(d)),x(p,v,s.slideVisibleClass),x(p,g,s.slideFullyVisibleClass),p.progress=r?-u:u,p.originalProgress=r?-h:h}},updateProgress:function e(t){if(void 0===t){let i=this.rtlTranslate?-1:1;t=this&&this.translate&&this.translate*i||0}let s=this.params,l=this.maxTranslate()-this.minTranslate(),{progress:r,isBeginning:a,isEnd:n,progressLoop:o}=this,d=a,p=n;if(0===l)r=0,a=!0,n=!0;else{r=(t-this.minTranslate())/l;let c=1>Math.abs(t-this.minTranslate()),u=1>Math.abs(t-this.maxTranslate());a=c||r<=0,n=u||r>=1,c&&(r=0),u&&(r=1)}if(s.loop){let h=this.getSlideIndexByData(0),f=this.getSlideIndexByData(this.slides.length-1),m=this.slidesGrid[h],g=this.slidesGrid[f],v=this.slidesGrid[this.slidesGrid.length-1],$=Math.abs(t);(o=$>=m?($-m)/v:($+v-g)/v)>1&&(o-=1)}Object.assign(this,{progress:r,progressLoop:o,isBeginning:a,isEnd:n}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&this.updateSlidesProgress(t),a&&!d&&this.emit("reachBeginning toEdge"),n&&!p&&this.emit("reachEnd toEdge"),(d&&!a||p&&!n)&&this.emit("fromEdge"),this.emit("progress",r)},updateSlidesClasses:function e(){let{slides:t,params:i,slidesEl:s,activeIndex:l}=this,r=this.virtual&&i.virtual.enabled,a=this.grid&&i.grid&&i.grid.rows>1,n=e=>h(s,`.${i.slideClass}${e}, swiper-slide${e}`)[0],o,d,p;if(r){if(i.loop){let c=l-this.virtual.slidesBefore;c<0&&(c=this.virtual.slides.length+c),c>=this.virtual.slides.length&&(c-=this.virtual.slides.length),o=n(`[data-swiper-slide-index="${c}"]`)}else o=n(`[data-swiper-slide-index="${l}"]`)}else a?(o=t.filter(e=>e.column===l)[0],p=t.filter(e=>e.column===l+1)[0],d=t.filter(e=>e.column===l-1)[0]):o=t[l];o&&!a&&(p=function e(t,i){let s=[];for(;t.nextElementSibling;){let l=t.nextElementSibling;i?l.matches(i)&&s.push(l):s.push(l),t=l}return s}(o,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!p&&(p=t[0]),d=function e(t,i){let s=[];for(;t.previousElementSibling;){let l=t.previousElementSibling;i?l.matches(i)&&s.push(l):s.push(l),t=l}return s}(o,`.${i.slideClass}, swiper-slide`)[0],i.loop),t.forEach(e=>{C(e,e===o,i.slideActiveClass),C(e,e===p,i.slideNextClass),C(e,e===d,i.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function e(t){let i=this,s=i.rtlTranslate?i.translate:-i.translate,{snapGrid:l,params:r,activeIndex:a,realIndex:n,snapIndex:o}=i,d=t,p,c=e=>{let t=e-i.virtual.slidesBefore;return t<0&&(t=i.virtual.slides.length+t),t>=i.virtual.slides.length&&(t-=i.virtual.slides.length),t};if(void 0===d&&(d=function e(t){let{slidesGrid:i,params:s}=t,l=t.rtlTranslate?t.translate:-t.translate,r;for(let a=0;a<i.length;a+=1)void 0!==i[a+1]?l>=i[a]&&l<i[a+1]-(i[a+1]-i[a])/2?r=a:l>=i[a]&&l<i[a+1]&&(r=a+1):l>=i[a]&&(r=a);return s.normalizeSlideIndex&&(r<0||void 0===r)&&(r=0),r}(i)),l.indexOf(s)>=0)p=l.indexOf(s);else{let u=Math.min(r.slidesPerGroupSkip,d);p=u+Math.floor((d-u)/r.slidesPerGroup)}if(p>=l.length&&(p=l.length-1),d===a&&!i.params.loop){p!==o&&(i.snapIndex=p,i.emit("snapIndexChange"));return}if(d===a&&i.params.loop&&i.virtual&&i.params.virtual.enabled){i.realIndex=c(d);return}let h=i.grid&&r.grid&&r.grid.rows>1,f;if(i.virtual&&r.virtual.enabled&&r.loop)f=c(d);else if(h){let m=i.slides.filter(e=>e.column===d)[0],g=parseInt(m.getAttribute("data-swiper-slide-index"),10);Number.isNaN(g)&&(g=Math.max(i.slides.indexOf(m),0)),f=Math.floor(g/r.grid.rows)}else if(i.slides[d]){let v=i.slides[d].getAttribute("data-swiper-slide-index");f=v?parseInt(v,10):d}else f=d;Object.assign(i,{previousSnapIndex:o,snapIndex:p,previousRealIndex:n,realIndex:f,previousIndex:a,activeIndex:d}),i.initialized&&M(i),i.emit("activeIndexChange"),i.emit("snapIndexChange"),(i.initialized||i.params.runCallbacksOnInit)&&(n!==f&&i.emit("realIndexChange"),i.emit("slideChange"))},updateClickedSlide:function e(t,i){let s=this,l=s.params,r=t.closest(`.${l.slideClass}, swiper-slide`);!r&&s.isElement&&i&&i.length>1&&i.includes(t)&&[...i.slice(i.indexOf(t)+1,i.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${l.slideClass}, swiper-slide`)&&(r=e)});let a=!1,n;if(r){for(let o=0;o<s.slides.length;o+=1)if(s.slides[o]===r){a=!0,n=o;break}}if(r&&a)s.clickedSlide=r,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=n;else{s.clickedSlide=void 0,s.clickedIndex=void 0;return}l.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}},translate:{getTranslate:function e(t){void 0===t&&(t=this.isHorizontal()?"x":"y");let{params:i,rtlTranslate:s,translate:l,wrapperEl:a}=this;if(i.virtualTranslate)return s?-l:l;if(i.cssMode)return l;let n=function e(t,i){void 0===i&&(i="x");let s=r(),l,a,n,o=function e(t){let i=r(),s;return i.getComputedStyle&&(s=i.getComputedStyle(t,null)),!s&&t.currentStyle&&(s=t.currentStyle),s||(s=t.style),s}(t);return s.WebKitCSSMatrix?((a=o.transform||o.webkitTransform).split(",").length>6&&(a=a.split(", ").map(e=>e.replace(",",".")).join(", ")),n=new s.WebKitCSSMatrix("none"===a?"":a)):l=(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===i&&(a=s.WebKitCSSMatrix?n.m41:16===l.length?parseFloat(l[12]):parseFloat(l[4])),"y"===i&&(a=s.WebKitCSSMatrix?n.m42:16===l.length?parseFloat(l[13]):parseFloat(l[5])),a||0}(a,t);return n+=this.cssOverflowAdjustment(),s&&(n=-n),n||0},setTranslate:function e(t,i){let s=this,{rtlTranslate:l,params:r,wrapperEl:a,progress:n}=s,o=0,d=0;s.isHorizontal()?o=l?-t:t:d=t,r.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?o:d,r.cssMode?a[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-o:-d:r.virtualTranslate||(s.isHorizontal()?o-=s.cssOverflowAdjustment():d-=s.cssOverflowAdjustment(),a.style.transform=`translate3d(${o}px, ${d}px, 0px)`);let p,c=s.maxTranslate()-s.minTranslate();(p=0===c?0:(t-s.minTranslate())/c)!==n&&s.updateProgress(t),s.emit("setTranslate",s.translate,i)},minTranslate:function e(){return-this.snapGrid[0]},maxTranslate:function e(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function e(t,i,s,l,r){void 0===t&&(t=0),void 0===i&&(i=this.params.speed),void 0===s&&(s=!0),void 0===l&&(l=!0);let a=this,{params:n,wrapperEl:o}=a;if(a.animating&&n.preventInteractionOnTransition)return!1;let d=a.minTranslate(),p=a.maxTranslate(),c;if(c=l&&t>d?d:l&&t<p?p:t,a.updateProgress(c),n.cssMode){let h=a.isHorizontal();if(0===i)o[h?"scrollLeft":"scrollTop"]=-c;else{if(!a.support.smoothScroll)return u({swiper:a,targetPosition:-c,side:h?"left":"top"}),!0;o.scrollTo({[h?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===i?(a.setTransition(0),a.setTranslate(c),s&&(a.emit("beforeTransitionStart",i,r),a.emit("transitionEnd"))):(a.setTransition(i),a.setTranslate(c),s&&(a.emit("beforeTransitionStart",i,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function e(t){a&&!a.destroyed&&t.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,s&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function e(t,i){let s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${t}ms`,s.wrapperEl.style.transitionDelay=0===t?"0ms":""),s.emit("setTransition",t,i)},transitionStart:function e(t,i){void 0===t&&(t=!0);let{params:s}=this;s.cssMode||(s.autoHeight&&this.updateAutoHeight(),k({swiper:this,runCallbacks:t,direction:i,step:"Start"}))},transitionEnd:function e(t,i){void 0===t&&(t=!0);let s=this,{params:l}=s;s.animating=!1,l.cssMode||(s.setTransition(0),k({swiper:s,runCallbacks:t,direction:i,step:"End"}))}},slide:{slideTo:function e(t,i,s,l,r){void 0===t&&(t=0),void 0===s&&(s=!0),"string"==typeof t&&(t=parseInt(t,10));let a=this,n=t;n<0&&(n=0);let{params:o,snapGrid:d,slidesGrid:p,previousIndex:c,activeIndex:h,rtlTranslate:f,wrapperEl:m,enabled:g}=a;if(!g&&!l&&!r||a.destroyed||a.animating&&o.preventInteractionOnTransition)return!1;void 0===i&&(i=a.params.speed);let v=Math.min(a.params.slidesPerGroupSkip,n),$=v+Math.floor((n-v)/a.params.slidesPerGroup);$>=d.length&&($=d.length-1);let w=-d[$];if(o.normalizeSlideIndex)for(let _=0;_<p.length;_+=1){let b=-Math.floor(100*w),y=Math.floor(100*p[_]),T=Math.floor(100*p[_+1]);void 0!==p[_+1]?b>=y&&b<T-(T-y)/2?n=_:b>=y&&b<T&&(n=_+1):b>=y&&(n=_)}if(a.initialized&&n!==h&&(!a.allowSlideNext&&(f?w>a.translate&&w>a.minTranslate():w<a.translate&&w<a.minTranslate())||!a.allowSlidePrev&&w>a.translate&&w>a.maxTranslate()&&(h||0)!==n))return!1;n!==(c||0)&&s&&a.emit("beforeSlideChangeStart"),a.updateProgress(w);let S;S=n>h?"next":n<h?"prev":"reset";let E=a.virtual&&a.params.virtual.enabled;if(!(E&&r)&&(f&&-w===a.translate||!f&&w===a.translate))return a.updateActiveIndex(n),o.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==o.effect&&a.setTranslate(w),"reset"!==S&&(a.transitionStart(s,S),a.transitionEnd(s,S)),!1;if(o.cssMode){let x=a.isHorizontal(),C=f?w:-w;if(0===i)E&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),E&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[x?"scrollLeft":"scrollTop"]=C})):m[x?"scrollLeft":"scrollTop"]=C,E&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return u({swiper:a,targetPosition:C,side:x?"left":"top"}),!0;m.scrollTo({[x?"left":"top"]:C,behavior:"smooth"})}return!0}return a.setTransition(i),a.setTranslate(w),a.updateActiveIndex(n),a.updateSlidesClasses(),a.emit("beforeTransitionStart",i,l),a.transitionStart(s,S),0===i?a.transitionEnd(s,S):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function e(t){a&&!a.destroyed&&t.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(s,S))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function e(t,i,s,l){if(void 0===t&&(t=0),void 0===s&&(s=!0),"string"==typeof t){let r=parseInt(t,10);t=r}let a=this;if(a.destroyed)return;void 0===i&&(i=a.params.speed);let n=a.grid&&a.params.grid&&a.params.grid.rows>1,o=t;if(a.params.loop){if(a.virtual&&a.params.virtual.enabled)o+=a.virtual.slidesBefore;else{let d;if(n){let p=o*a.params.grid.rows;d=a.slides.filter(e=>1*e.getAttribute("data-swiper-slide-index")===p)[0].column}else d=a.getSlideIndexByData(o);let c=n?Math.ceil(a.slides.length/a.params.grid.rows):a.slides.length,{centeredSlides:u}=a.params,h=a.params.slidesPerView;"auto"===h?h=a.slidesPerViewDynamic():(h=Math.ceil(parseFloat(a.params.slidesPerView,10)),u&&h%2==0&&(h+=1));let f=c-d<h;if(u&&(f=f||d<Math.ceil(h/2)),l&&u&&"auto"!==a.params.slidesPerView&&!n&&(f=!1),f){let m=u?d<a.activeIndex?"prev":"next":d-a.activeIndex-1<a.params.slidesPerView?"next":"prev";a.loopFix({direction:m,slideTo:!0,activeSlideIndex:"next"===m?d+1:d-c+1,slideRealIndex:"next"===m?a.realIndex:void 0})}if(n){let g=o*a.params.grid.rows;o=a.slides.filter(e=>1*e.getAttribute("data-swiper-slide-index")===g)[0].column}else o=a.getSlideIndexByData(o)}}return requestAnimationFrame(()=>{a.slideTo(o,i,s,l)}),a},slideNext:function e(t,i,s){void 0===i&&(i=!0);let l=this,{enabled:r,params:a,animating:n}=l;if(!r||l.destroyed)return l;void 0===t&&(t=l.params.speed);let o=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(o=Math.max(l.slidesPerViewDynamic("current",!0),1));let d=l.activeIndex<a.slidesPerGroupSkip?1:o,p=l.virtual&&a.virtual.enabled;if(a.loop){if(n&&!p&&a.loopPreventsSliding)return!1;if(l.loopFix({direction:"next"}),l._clientLeft=l.wrapperEl.clientLeft,l.activeIndex===l.slides.length-1&&a.cssMode)return requestAnimationFrame(()=>{l.slideTo(l.activeIndex+d,t,i,s)}),!0}return a.rewind&&l.isEnd?l.slideTo(0,t,i,s):l.slideTo(l.activeIndex+d,t,i,s)},slidePrev:function e(t,i,s){void 0===i&&(i=!0);let l=this,{params:r,snapGrid:a,slidesGrid:n,rtlTranslate:o,enabled:d,animating:p}=l;if(!d||l.destroyed)return l;void 0===t&&(t=l.params.speed);let c=l.virtual&&r.virtual.enabled;if(r.loop){if(p&&!c&&r.loopPreventsSliding)return!1;l.loopFix({direction:"prev"}),l._clientLeft=l.wrapperEl.clientLeft}let u=o?l.translate:-l.translate;function h(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let f=h(u),m=a.map(e=>h(e)),g=a[m.indexOf(f)-1];if(void 0===g&&r.cssMode){let v;a.forEach((e,t)=>{f>=e&&(v=t)}),void 0!==v&&(g=a[v>0?v-1:v])}let $=0;if(void 0!==g&&(($=n.indexOf(g))<0&&($=l.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&($=Math.max($=$-l.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&l.isBeginning){let w=l.params.virtual&&l.params.virtual.enabled&&l.virtual?l.virtual.slides.length-1:l.slides.length-1;return l.slideTo(w,t,i,s)}return r.loop&&0===l.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{l.slideTo($,t,i,s)}),!0):l.slideTo($,t,i,s)},slideReset:function e(t,i,s){if(void 0===i&&(i=!0),!this.destroyed)return void 0===t&&(t=this.params.speed),this.slideTo(this.activeIndex,t,i,s)},slideToClosest:function e(t,i,s,l){if(void 0===i&&(i=!0),void 0===l&&(l=.5),this.destroyed)return;void 0===t&&(t=this.params.speed);let r=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,r),n=a+Math.floor((r-a)/this.params.slidesPerGroup),o=this.rtlTranslate?this.translate:-this.translate;if(o>=this.snapGrid[n]){let d=this.snapGrid[n],p=this.snapGrid[n+1];o-d>(p-d)*l&&(r+=this.params.slidesPerGroup)}else{let c=this.snapGrid[n-1],u=this.snapGrid[n];o-c<=(u-c)*l&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,t,i,s)},slideToClickedSlide:function e(){let t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,l="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,r=t.clickedIndex,n,o=t.isElement?"swiper-slide":`.${i.slideClass}`;if(i.loop){if(t.animating)return;n=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?r<t.loopedSlides-l/2||r>t.slides.length-t.loopedSlides+l/2?(t.loopFix(),r=t.getSlideIndex(h(s,`${o}[data-swiper-slide-index="${n}"]`)[0]),a(()=>{t.slideTo(r)})):t.slideTo(r):r>t.slides.length-l?(t.loopFix(),r=t.getSlideIndex(h(s,`${o}[data-swiper-slide-index="${n}"]`)[0]),a(()=>{t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}},loop:{loopCreate:function e(t){let i=this,{params:s,slidesEl:l}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;let r=()=>{let e=h(l,`.${s.slideClass}, swiper-slide`);e.forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})},a=i.grid&&s.grid&&s.grid.rows>1,n=s.slidesPerGroup*(a?s.grid.rows:1),o=i.slides.length%n!=0,d=a&&i.slides.length%s.grid.rows!=0,p=e=>{for(let t=0;t<e;t+=1){let l=i.isElement?m("swiper-slide",[s.slideBlankClass]):m("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(l)}};if(o){if(s.loopAddBlankSlides){let c=n-i.slides.length%n;p(c),i.recalcSlides(),i.updateSlides()}else f("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(d){if(s.loopAddBlankSlides){let u=s.grid.rows-i.slides.length%s.grid.rows;p(u),i.recalcSlides(),i.updateSlides()}else f("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();i.loopFix({slideRealIndex:t,direction:s.centeredSlides?void 0:"next"})},loopFix:function e(t){let{slideRealIndex:i,slideTo:s=!0,direction:l,setTranslate:r,activeSlideIndex:a,byController:n,byMousewheel:o}=void 0===t?{}:t,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:p,allowSlidePrev:c,allowSlideNext:u,slidesEl:h,params:m}=d,{centeredSlides:g}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled){s&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=c,d.allowSlideNext=u,d.emit("loopFix");return}let v=m.slidesPerView;"auto"===v?v=d.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),g&&v%2==0&&(v+=1));let $=m.slidesPerGroupAuto?v:m.slidesPerGroup,w=$;w%$!=0&&(w+=$-w%$),w+=m.loopAdditionalSlides,d.loopedSlides=w;let _=d.grid&&m.grid&&m.grid.rows>1;p.length<v+w?f("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):_&&"row"===m.grid.fill&&f("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let b=[],y=[],T=d.activeIndex;void 0===a?a=d.getSlideIndex(p.filter(e=>e.classList.contains(m.slideActiveClass))[0]):T=a;let S="next"===l||!l,E="prev"===l||!l,x=0,C=0,L=_?Math.ceil(p.length/m.grid.rows):p.length,P=_?p[a].column:a,M=P+(g&&void 0===r?-v/2+.5:0);if(M<w){x=Math.max(w-M,$);for(let k=0;k<w-M;k+=1){let A=k-Math.floor(k/L)*L;if(_){let I=L-A-1;for(let z=p.length-1;z>=0;z-=1)p[z].column===I&&b.push(z)}else b.push(L-A-1)}}else if(M+v>L-w){C=Math.max(M-(L-2*w),$);for(let O=0;O<C;O+=1){let D=O-Math.floor(O/L)*L;_?p.forEach((e,t)=>{e.column===D&&y.push(t)}):y.push(D)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),E&&b.forEach(e=>{p[e].swiperLoopMoveDOM=!0,h.prepend(p[e]),p[e].swiperLoopMoveDOM=!1}),S&&y.forEach(e=>{p[e].swiperLoopMoveDOM=!0,h.append(p[e]),p[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():_&&(b.length>0&&E||y.length>0&&S)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),m.watchSlidesProgress&&d.updateSlidesOffset(),s){if(b.length>0&&E){if(void 0===i){let G=d.slidesGrid[T],B=d.slidesGrid[T+x],N=B-G;o?d.setTranslate(d.translate-N):(d.slideTo(T+Math.ceil(x),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-N,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-N))}else if(r){let V=_?b.length/m.grid.rows:b.length;d.slideTo(d.activeIndex+V,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(y.length>0&&S){if(void 0===i){let H=d.slidesGrid[T],R=d.slidesGrid[T-C],F=R-H;o?d.setTranslate(d.translate-F):(d.slideTo(T-C,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-F,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-F))}else{let Y=_?y.length/m.grid.rows:y.length;d.slideTo(d.activeIndex-Y,0,!1,!0)}}}if(d.allowSlidePrev=c,d.allowSlideNext=u,d.controller&&d.controller.control&&!n){let X={slideRealIndex:i,direction:l,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(e=>{!e.destroyed&&e.params.loop&&e.loopFix({...X,slideTo:e.params.slidesPerView===m.slidesPerView&&s})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...X,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&s})}d.emit("loopFix")},loopDestroy:function e(){let{params:t,slidesEl:i}=this;if(!t.loop||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let s=[];this.slides.forEach(e=>{let t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;s[t]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),s.forEach(e=>{i.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function e(t){let i=this;if(!i.params.simulateTouch||i.params.watchOverflow&&i.isLocked||i.params.cssMode)return;let s="container"===i.params.touchEventsTarget?i.el:i.wrapperEl;i.isElement&&(i.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=t?"grabbing":"grab",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1})},unsetGrabCursor:function e(){let t=this;(!t.params.watchOverflow||!t.isLocked)&&!t.params.cssMode&&(t.isElement&&(t.__preventObserver__=!0),t["container"===t.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}},events:{attachEvents:function e(){let t=this,{params:i}=t;t.onTouchStart=I.bind(t),t.onTouchMove=z.bind(t),t.onTouchEnd=O.bind(t),t.onDocumentTouchStart=V.bind(t),i.cssMode&&(t.onScroll=B.bind(t)),t.onClick=G.bind(t),t.onLoad=N.bind(t),H(t,"on")},detachEvents:function e(){H(this,"off")}},breakpoints:{setBreakpoint:function e(){let t=this,{realIndex:i,initialized:s,params:l,el:r}=t,a=l.breakpoints;if(!a||a&&0===Object.keys(a).length)return;let n=t.getBreakpoint(a,t.params.breakpointsBase,t.el);if(!n||t.currentBreakpoint===n)return;let o=n in a?a[n]:void 0,d=o||t.originalParams,c=R(t,l),u=R(t,d),h=t.params.grabCursor,f=d.grabCursor,m=l.enabled;c&&!u?(r.classList.remove(`${l.containerModifierClass}grid`,`${l.containerModifierClass}grid-column`),t.emitContainerClasses()):!c&&u&&(r.classList.add(`${l.containerModifierClass}grid`),(d.grid.fill&&"column"===d.grid.fill||!d.grid.fill&&"column"===l.grid.fill)&&r.classList.add(`${l.containerModifierClass}grid-column`),t.emitContainerClasses()),h&&!f?t.unsetGrabCursor():!h&&f&&t.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(e=>{if(void 0===d[e])return;let i=l[e]&&l[e].enabled,s=d[e]&&d[e].enabled;i&&!s&&t[e].disable(),!i&&s&&t[e].enable()});let g=d.direction&&d.direction!==l.direction,v=l.loop&&(d.slidesPerView!==l.slidesPerView||g),$=l.loop;g&&s&&t.changeDirection(),p(t.params,d);let w=t.params.enabled,_=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),m&&!w?t.disable():!m&&w&&t.enable(),t.currentBreakpoint=n,t.emit("_beforeBreakpoint",d),s&&(v?(t.loopDestroy(),t.loopCreate(i),t.updateSlides()):!$&&_?(t.loopCreate(i),t.updateSlides()):$&&!_&&t.loopDestroy()),t.emit("breakpoint",d)},getBreakpoint:function e(t,i,s){if(void 0===i&&(i="window"),!t||"container"===i&&!s)return;let l=!1,a=r(),n="window"===i?a.innerHeight:s.clientHeight,o=Object.keys(t).map(e=>{if("string"==typeof e&&0===e.indexOf("@")){let t=parseFloat(e.substr(1));return{value:n*t,point:e}}return{value:e,point:e}});o.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let d=0;d<o.length;d+=1){let{point:p,value:c}=o[d];"window"===i?a.matchMedia(`(min-width: ${c}px)`).matches&&(l=p):c<=s.clientWidth&&(l=p)}return l||"max"}},checkOverflow:{checkOverflow:function e(){let t=this,{isLocked:i,params:s}=t,{slidesOffsetBefore:l}=s;if(l){let r=t.slides.length-1,a=t.slidesGrid[r]+t.slidesSizesGrid[r]+2*l;t.isLocked=t.size>a}else t.isLocked=1===t.snapGrid.length;!0===s.allowSlideNext&&(t.allowSlideNext=!t.isLocked),!0===s.allowSlidePrev&&(t.allowSlidePrev=!t.isLocked),i&&i!==t.isLocked&&(t.isEnd=!1),i!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}},classes:{addClasses:function e(){let{classNames:t,params:i,rtl:s,el:l,device:r}=this,a=function e(t,i){let s=[];return t.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(t=>{e[t]&&s.push(i+t)}):"string"==typeof e&&s.push(i+e)}),s}(["initialized",i.direction,{"free-mode":this.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);t.push(...a),l.classList.add(...t),this.emitContainerClasses()},removeClasses:function e(){let{el:t,classNames:i}=this;t&&"string"!=typeof t&&(t.classList.remove(...i),this.emitContainerClasses())}}},X={};class W{constructor(){let e,t;for(var i=arguments.length,l=Array(i),a=0;a<i;a++)l[a]=arguments[a];1===l.length&&l[0].constructor&&"Object"===Object.prototype.toString.call(l[0]).slice(8,-1)?t=l[0]:[e,t]=l,t||(t={}),t=p({},t),e&&!t.el&&(t.el=e);let n=s();if(t.el&&"string"==typeof t.el&&n.querySelectorAll(t.el).length>1){let o=[];return n.querySelectorAll(t.el).forEach(e=>{let i=p({},t,{el:e});o.push(new W(i))}),o}let d=this;d.__swiper__=!0,d.support=y(),d.device=S({userAgent:t.userAgent}),d.browser=(E||(E=function e(){let t=r(),i=S(),s=!1;function l(){let e=t.navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&0>e.indexOf("chrome")&&0>e.indexOf("android")}if(l()){let a=String(t.navigator.userAgent);if(a.includes("Version/")){let[n,o]=a.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));s=n<16||16===n&&o<2}}let d=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),p=l(),c=p||d&&i.ios;return{isSafari:s||p,needPerspectiveFix:s,need3dFix:c,isWebView:d}}()),E),d.eventsListeners={},d.eventsAnyListeners=[],d.modules=[...d.__modules__],t.modules&&Array.isArray(t.modules)&&d.modules.push(...t.modules);let c={};d.modules.forEach(e=>{var i,s;e({params:t,swiper:d,extendParams:(i=t,s=c,function e(t){void 0===t&&(t={});let l=Object.keys(t)[0],r=t[l];if("object"!=typeof r||null===r||(!0===i[l]&&(i[l]={enabled:!0}),"navigation"===l&&i[l]&&i[l].enabled&&!i[l].prevEl&&!i[l].nextEl&&(i[l].auto=!0),["pagination","scrollbar"].indexOf(l)>=0&&i[l]&&i[l].enabled&&!i[l].el&&(i[l].auto=!0),!(l in i&&"enabled"in r))){p(s,t);return}"object"!=typeof i[l]||"enabled"in i[l]||(i[l].enabled=!0),i[l]||(i[l]={enabled:!1}),p(s,t)}),on:d.on.bind(d),once:d.once.bind(d),off:d.off.bind(d),emit:d.emit.bind(d)})});let u=p({},F,c);return d.params=p({},u,X,t),d.originalParams=p({},d.params),d.passedParams=p({},t),d.params&&d.params.on&&Object.keys(d.params.on).forEach(e=>{d.on(e,d.params.on[e])}),d.params&&d.params.onAny&&d.onAny(d.params.onAny),Object.assign(d,{enabled:d.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===d.params.direction,isVertical:()=>"vertical"===d.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:d.params.allowSlideNext,allowSlidePrev:d.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:d.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:d.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),d.emit("_swiper"),d.params.init&&d.init(),d}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=h(t,`.${i.slideClass}, swiper-slide`),l=v(s[0]);return v(e)-l}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>1*t.getAttribute("data-swiper-slide-index")===e)[0])}recalcSlides(){let e=this,{slidesEl:t,params:i}=e;e.slides=h(t,`.${i.slideClass}, swiper-slide`)}enable(){let e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){let e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=this.maxTranslate(),l=(s-i)*e+i;this.translateTo(l,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:l,slidesSizesGrid:r,size:a,activeIndex:n}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let d=s[n]?Math.ceil(s[n].swiperSlideSize):0,p;for(let c=n+1;c<s.length;c+=1)s[c]&&!p&&(d+=Math.ceil(s[c].swiperSlideSize),o+=1,d>a&&(p=!0));for(let u=n-1;u>=0;u-=1)s[u]&&!p&&(d+=s[u].swiperSlideSize,o+=1,d>a&&(p=!0))}else if("current"===e)for(let h=n+1;h<s.length;h+=1){let f=t?l[h]+r[h]-l[n]<a:l[h]-l[n]<a;f&&(o+=1)}else for(let m=n-1;m>=0;m-=1){let g=l[n]-l[m]<a;g&&(o+=1)}return o}update(){let e=this;if(!e||e.destroyed)return;let{snapGrid:t,params:i}=e;function s(){let t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(t=>{t.complete&&L(e,t)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();let l;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)s(),i.autoHeight&&e.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){let r=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;l=e.slideTo(r.length-1,0,!1,!0)}else l=e.slideTo(e.activeIndex,0,!1,!0);l||s()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this,s=i.params.direction;return e||(e="horizontal"===s?"vertical":"horizontal"),e===s||"horizontal"!==e&&"vertical"!==e||(i.el.classList.remove(`${i.params.containerModifierClass}${s}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){let t=this;(!t.rtl||"rtl"!==e)&&(t.rtl||"ltr"!==e)&&(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,l=(()=>{if(i&&i.shadowRoot&&i.shadowRoot.querySelector){let e=i.shadowRoot.querySelector(s());return e}return h(i,s())[0]})();return!l&&t.params.createElements&&(l=m("div",t.params.wrapperClass),i.append(l),h(i,`.${t.params.slideClass}`).forEach(e=>{l.append(e)})),Object.assign(t,{el:i,wrapperEl:l,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:l,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===g(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===g(i,"direction")),wrongRTL:"-webkit-box"===g(l,"display")}),!0}init(e){let t=this;if(t.initialized)return t;let i=t.mount(e);if(!1===i)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach(e=>{e.complete?L(t,e):e.addEventListener("load",e=>{L(t,e.target)})}),M(t),t.initialized=!0,M(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:l,wrapperEl:r,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),l&&"string"!=typeof l&&l.removeAttribute("style"),r&&r.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),function e(t){let i=t;Object.keys(i).forEach(e=>{try{i[e]=null}catch(t){}try{delete i[e]}catch(s){}})}(i)),i.destroyed=!0),null}static extendDefaults(e){p(X,e)}static get extendedDefaults(){return X}static get defaults(){return F}static installModule(e){W.prototype.__modules__||(W.prototype.__modules__=[]);let t=W.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>W.installModule(e)),W):(W.installModule(e),W)}}function q(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(l=>{if(!i[l]&&!0===i.auto){let r=h(e.el,`.${s[l]}`)[0];r||((r=m("div",s[l])).className=s[l],e.el.append(r)),i[l]=r,t[l]=r}}),i}function j(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function U(e){let{params:t,slidesEl:i}=this;t.loop&&this.loopDestroy();let s=e=>{if("string"==typeof e){let t=document.createElement("div");t.innerHTML=e,i.append(t.children[0]),t.innerHTML=""}else i.append(e)};if("object"==typeof e&&"length"in e)for(let l=0;l<e.length;l+=1)e[l]&&s(e[l]);else s(e);this.recalcSlides(),t.loop&&this.loopCreate(),(!t.observer||this.isElement)&&this.update()}function K(e){let{params:t,activeIndex:i,slidesEl:s}=this;t.loop&&this.loopDestroy();let l=i+1,r=e=>{if("string"==typeof e){let t=document.createElement("div");t.innerHTML=e,s.prepend(t.children[0]),t.innerHTML=""}else s.prepend(e)};if("object"==typeof e&&"length"in e){for(let a=0;a<e.length;a+=1)e[a]&&r(e[a]);l=i+e.length}else r(e);this.recalcSlides(),t.loop&&this.loopCreate(),(!t.observer||this.isElement)&&this.update(),this.slideTo(l,0,!1)}function Z(e,t){let{params:i,activeIndex:s,slidesEl:l}=this,r=s;i.loop&&(r-=this.loopedSlides,this.loopDestroy(),this.recalcSlides());let a=this.slides.length;if(e<=0){this.prependSlide(t);return}if(e>=a){this.appendSlide(t);return}let n=r>e?r+1:r,o=[];for(let d=a-1;d>=e;d-=1){let p=this.slides[d];p.remove(),o.unshift(p)}if("object"==typeof t&&"length"in t){for(let c=0;c<t.length;c+=1)t[c]&&l.append(t[c]);n=r>e?r+t.length:r}else l.append(t);for(let u=0;u<o.length;u+=1)l.append(o[u]);this.recalcSlides(),i.loop&&this.loopCreate(),(!i.observer||this.isElement)&&this.update(),i.loop?this.slideTo(n+this.loopedSlides,0,!1):this.slideTo(n,0,!1)}function J(e){let{params:t,activeIndex:i}=this,s=i;t.loop&&(s-=this.loopedSlides,this.loopDestroy());let l=s,r;if("object"==typeof e&&"length"in e){for(let a=0;a<e.length;a+=1)r=e[a],this.slides[r]&&this.slides[r].remove(),r<l&&(l-=1);l=Math.max(l,0)}else r=e,this.slides[r]&&this.slides[r].remove(),r<l&&(l-=1),l=Math.max(l,0);this.recalcSlides(),t.loop&&this.loopCreate(),(!t.observer||this.isElement)&&this.update(),t.loop?this.slideTo(l+this.loopedSlides,0,!1):this.slideTo(l,0,!1)}function Q(){let e=[];for(let t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}Object.keys(Y).forEach(e=>{Object.keys(Y[e]).forEach(t=>{W.prototype[t]=Y[e][t]})}),W.use([function e(t){let{swiper:i,on:s,emit:l}=t,a=r(),n=null,o=null,d=()=>{i&&!i.destroyed&&i.initialized&&(l("beforeResize"),l("resize"))},p=()=>{i&&!i.destroyed&&i.initialized&&(n=new ResizeObserver(e=>{o=a.requestAnimationFrame(()=>{let{width:t,height:s}=i,l=t,r=s;e.forEach(e=>{let{contentBoxSize:t,contentRect:s,target:a}=e;a&&a!==i.el||(l=s?s.width:(t[0]||t).inlineSize,r=s?s.height:(t[0]||t).blockSize)}),(l!==t||r!==s)&&d()})})).observe(i.el)},c=()=>{o&&a.cancelAnimationFrame(o),n&&n.unobserve&&i.el&&(n.unobserve(i.el),n=null)},u=()=>{i&&!i.destroyed&&i.initialized&&l("orientationchange")};s("init",()=>{if(i.params.resizeObserver&&void 0!==a.ResizeObserver){p();return}a.addEventListener("resize",d),a.addEventListener("orientationchange",u)}),s("destroy",()=>{c(),a.removeEventListener("resize",d),a.removeEventListener("orientationchange",u)})},function e(t){let{swiper:i,extendParams:s,on:l,emit:a}=t,n=[],o=r(),d=function(e,t){void 0===t&&(t={});let s=o.MutationObserver||o.WebkitMutationObserver,l=new s(e=>{if(i.__preventObserver__)return;if(1===e.length){a("observerUpdate",e[0]);return}let t=function t(){a("observerUpdate",e[0])};o.requestAnimationFrame?o.requestAnimationFrame(t):o.setTimeout(t,0)});l.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:i.isElement||(void 0===t.childList||t).childList,characterData:void 0===t.characterData||t.characterData}),n.push(l)},p=()=>{if(i.params.observer){if(i.params.observeParents){let e=$(i.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(i.hostEl,{childList:i.params.observeSlideChildren}),d(i.wrapperEl,{attributes:!1})}},c=()=>{n.forEach(e=>{e.disconnect()}),n.splice(0,n.length)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),l("init",p),l("destroy",c)}]);let ee=[function e(t){let{swiper:i,extendParams:l,on:r,emit:a}=t;l({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});let n,o=s();i.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};let d=o.createElement("div");function p(e,t){let s=i.params.virtual;if(s.cache&&i.virtual.cache[t])return i.virtual.cache[t];let l;return s.renderSlide?"string"==typeof(l=s.renderSlide.call(i,e,t))&&(d.innerHTML=l,l=d.children[0]):l=i.isElement?m("swiper-slide"):m("div",i.params.slideClass),l.setAttribute("data-swiper-slide-index",t),s.renderSlide||(l.innerHTML=e),s.cache&&(i.virtual.cache[t]=l),l}function u(e,t){let{slidesPerView:s,slidesPerGroup:l,centeredSlides:r,loop:n,initialSlide:o}=i.params;if(t&&!n&&o>0)return;let{addSlidesBefore:d,addSlidesAfter:c}=i.params.virtual,{from:u,to:f,slides:m,slidesGrid:g,offset:v}=i.virtual;i.params.cssMode||i.updateActiveIndex();let $=i.activeIndex||0,w;w=i.rtlTranslate?"right":i.isHorizontal()?"left":"top";let _,b;r?(_=Math.floor(s/2)+l+c,b=Math.floor(s/2)+l+d):(_=s+(l-1)+c,b=(n?s:l)+d);let y=$-b,T=$+_;n||(y=Math.max(y,0),T=Math.min(T,m.length-1));let S=(i.slidesGrid[y]||0)-(i.slidesGrid[0]||0);function E(){i.updateSlides(),i.updateProgress(),i.updateSlidesClasses(),a("virtualUpdate")}if(n&&$>=b?(y-=b,r||(S+=i.slidesGrid[0])):n&&$<b&&(y=-b,r&&(S+=i.slidesGrid[0])),Object.assign(i.virtual,{from:y,to:T,offset:S,slidesGrid:i.slidesGrid,slidesBefore:b,slidesAfter:_}),u===y&&f===T&&!e){i.slidesGrid!==g&&S!==v&&i.slides.forEach(e=>{e.style[w]=`${S-Math.abs(i.cssOverflowAdjustment())}px`}),i.updateProgress(),a("virtualUpdate");return}if(i.params.virtual.renderExternal){i.params.virtual.renderExternal.call(i,{offset:S,from:y,to:T,slides:function e(){let t=[];for(let i=y;i<=T;i+=1)t.push(m[i]);return t}()}),i.params.virtual.renderExternalUpdate?E():a("virtualUpdate");return}let x=[],C=[],L=e=>{let t=e;return e<0?t=m.length+e:t>=m.length&&(t-=m.length),t};if(e)i.slides.filter(e=>e.matches(`.${i.params.slideClass}, swiper-slide`)).forEach(e=>{e.remove()});else for(let P=u;P<=f;P+=1)if(P<y||P>T){let M=L(P);i.slides.filter(e=>e.matches(`.${i.params.slideClass}[data-swiper-slide-index="${M}"], swiper-slide[data-swiper-slide-index="${M}"]`)).forEach(e=>{e.remove()})}let k=n?-m.length:0,A=n?2*m.length:m.length;for(let I=k;I<A;I+=1)if(I>=y&&I<=T){let z=L(I);void 0===f||e?C.push(z):(I>f&&C.push(z),I<u&&x.push(z))}if(C.forEach(e=>{i.slidesEl.append(p(m[e],e))}),n)for(let O=x.length-1;O>=0;O-=1){let D=x[O];i.slidesEl.prepend(p(m[D],D))}else x.sort((e,t)=>t-e),x.forEach(e=>{i.slidesEl.prepend(p(m[e],e))});h(i.slidesEl,".swiper-slide, swiper-slide").forEach(e=>{e.style[w]=`${S-Math.abs(i.cssOverflowAdjustment())}px`}),E()}r("beforeInit",()=>{if(!i.params.virtual.enabled)return;let e;if(void 0===i.passedParams.virtual.slides){let t=[...i.slidesEl.children].filter(e=>e.matches(`.${i.params.slideClass}, swiper-slide`));t&&t.length&&(i.virtual.slides=[...t],e=!0,t.forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t),i.virtual.cache[t]=e,e.remove()}))}e||(i.virtual.slides=i.params.virtual.slides),i.classNames.push(`${i.params.containerModifierClass}virtual`),i.params.watchSlidesProgress=!0,i.originalParams.watchSlidesProgress=!0,u(!1,!0)}),r("setTranslate",()=>{i.params.virtual.enabled&&(i.params.cssMode&&!i._immediateVirtual?(clearTimeout(n),n=setTimeout(()=>{u()},100)):u())}),r("init update resize",()=>{i.params.virtual.enabled&&i.params.cssMode&&c(i.wrapperEl,"--swiper-virtual-size",`${i.virtualSize}px`)}),Object.assign(i.virtual,{appendSlide:function e(t){if("object"==typeof t&&"length"in t)for(let s=0;s<t.length;s+=1)t[s]&&i.virtual.slides.push(t[s]);else i.virtual.slides.push(t);u(!0)},prependSlide:function e(t){let s=i.activeIndex,l=s+1,r=1;if(Array.isArray(t)){for(let a=0;a<t.length;a+=1)t[a]&&i.virtual.slides.unshift(t[a]);l=s+t.length,r=t.length}else i.virtual.slides.unshift(t);if(i.params.virtual.cache){let n=i.virtual.cache,o={};Object.keys(n).forEach(e=>{let t=n[e],i=t.getAttribute("data-swiper-slide-index");i&&t.setAttribute("data-swiper-slide-index",parseInt(i,10)+r),o[parseInt(e,10)+r]=t}),i.virtual.cache=o}u(!0),i.slideTo(l,0)},removeSlide:function e(t){if(null==t)return;let s=i.activeIndex;if(Array.isArray(t))for(let l=t.length-1;l>=0;l-=1)i.params.virtual.cache&&(delete i.virtual.cache[t[l]],Object.keys(i.virtual.cache).forEach(e=>{e>t&&(i.virtual.cache[e-1]=i.virtual.cache[e],i.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete i.virtual.cache[e])})),i.virtual.slides.splice(t[l],1),t[l]<s&&(s-=1),s=Math.max(s,0);else i.params.virtual.cache&&(delete i.virtual.cache[t],Object.keys(i.virtual.cache).forEach(e=>{e>t&&(i.virtual.cache[e-1]=i.virtual.cache[e],i.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete i.virtual.cache[e])})),i.virtual.slides.splice(t,1),t<s&&(s-=1),s=Math.max(s,0);u(!0),i.slideTo(s,0)},removeAllSlides:function e(){i.virtual.slides=[],i.params.virtual.cache&&(i.virtual.cache={}),u(!0),i.slideTo(0,0)},update:u})},function e(t){let{swiper:i,extendParams:l,on:a,emit:n}=t,o=s(),d=r();function p(e){if(!i.enabled)return;let{rtlTranslate:t}=i,l=e;l.originalEvent&&(l=l.originalEvent);let a=l.keyCode||l.charCode,p=i.params.keyboard.pageUpDown,c=p&&33===a,u=p&&34===a,h=37===a,f=39===a,m=38===a,g=40===a;if(!i.allowSlideNext&&(i.isHorizontal()&&f||i.isVertical()&&g||u)||!i.allowSlidePrev&&(i.isHorizontal()&&h||i.isVertical()&&m||c))return!1;if(!l.shiftKey&&!l.altKey&&!l.ctrlKey&&!l.metaKey&&(!o.activeElement||!o.activeElement.nodeName||"input"!==o.activeElement.nodeName.toLowerCase()&&"textarea"!==o.activeElement.nodeName.toLowerCase())){if(i.params.keyboard.onlyInViewport&&(c||u||h||f||m||g)){let v=!1;if($(i.el,`.${i.params.slideClass}, swiper-slide`).length>0&&0===$(i.el,`.${i.params.slideActiveClass}`).length)return;let w=i.el,_=w.clientWidth,b=w.clientHeight,y=d.innerWidth,T=d.innerHeight,S=function e(t){let i=r(),l=s(),a=t.getBoundingClientRect(),n=l.body,o=t.clientTop||n.clientTop||0,d=t.clientLeft||n.clientLeft||0,p=t===i?i.scrollY:t.scrollTop,c=t===i?i.scrollX:t.scrollLeft;return{top:a.top+p-o,left:a.left+c-d}}(w);t&&(S.left-=w.scrollLeft);let E=[[S.left,S.top],[S.left+_,S.top],[S.left,S.top+b],[S.left+_,S.top+b]];for(let x=0;x<E.length;x+=1){let C=E[x];if(C[0]>=0&&C[0]<=y&&C[1]>=0&&C[1]<=T){if(0===C[0]&&0===C[1])continue;v=!0}}if(!v)return}i.isHorizontal()?((c||u||h||f)&&(l.preventDefault?l.preventDefault():l.returnValue=!1),((u||f)&&!t||(c||h)&&t)&&i.slideNext(),((c||h)&&!t||(u||f)&&t)&&i.slidePrev()):((c||u||m||g)&&(l.preventDefault?l.preventDefault():l.returnValue=!1),(u||g)&&i.slideNext(),(c||m)&&i.slidePrev()),n("keyPress",a)}}function c(){i.keyboard.enabled||(o.addEventListener("keydown",p),i.keyboard.enabled=!0)}function u(){i.keyboard.enabled&&(o.removeEventListener("keydown",p),i.keyboard.enabled=!1)}i.keyboard={enabled:!1},l({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),a("init",()=>{i.params.keyboard.enabled&&c()}),a("destroy",()=>{i.keyboard.enabled&&u()}),Object.assign(i.keyboard,{enable:c,disable:u})},function e(t){let{swiper:i,extendParams:s,on:l,emit:o}=t,d=r();s({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),i.mousewheel={enabled:!1};let p,c=n(),u,h=[];function f(){i.enabled&&(i.mouseEntered=!0)}function m(){i.enabled&&(i.mouseEntered=!1)}function g(e){return!(i.params.mousewheel.thresholdDelta&&e.delta<i.params.mousewheel.thresholdDelta||i.params.mousewheel.thresholdTime&&n()-c<i.params.mousewheel.thresholdTime)&&(!!(e.delta>=6&&n()-c<60)||(e.direction<0?i.isEnd&&!i.params.loop||i.animating||(i.slideNext(),o("scroll",e.raw)):i.isBeginning&&!i.params.loop||i.animating||(i.slidePrev(),o("scroll",e.raw)),c=new d.Date().getTime(),!1))}function v(e){var t;let s=e,l=!0;if(!i.enabled||e.target.closest(`.${i.params.mousewheel.noMousewheelClass}`))return;let r=i.params.mousewheel;i.params.cssMode&&s.preventDefault();let d=i.el;"container"!==i.params.mousewheel.eventsTarget&&(d=document.querySelector(i.params.mousewheel.eventsTarget));let c=d&&d.contains(s.target);if(!i.mouseEntered&&!c&&!r.releaseOnEdges)return!0;s.originalEvent&&(s=s.originalEvent);let f=0,m=i.rtlTranslate?-1:1,v,$,w,_,b=(t=s,v=0,$=0,w=0,_=0,"detail"in t&&($=t.detail),"wheelDelta"in t&&($=-t.wheelDelta/120),"wheelDeltaY"in t&&($=-t.wheelDeltaY/120),"wheelDeltaX"in t&&(v=-t.wheelDeltaX/120),"axis"in t&&t.axis===t.HORIZONTAL_AXIS&&(v=$,$=0),w=10*v,_=10*$,"deltaY"in t&&(_=t.deltaY),"deltaX"in t&&(w=t.deltaX),t.shiftKey&&!w&&(w=_,_=0),(w||_)&&t.deltaMode&&(1===t.deltaMode?(w*=40,_*=40):(w*=800,_*=800)),w&&!v&&(v=w<1?-1:1),_&&!$&&($=_<1?-1:1),{spinX:v,spinY:$,pixelX:w,pixelY:_});if(r.forceToAxis){if(i.isHorizontal()){if(!(Math.abs(b.pixelX)>Math.abs(b.pixelY)))return!0;f=-b.pixelX*m}else{if(!(Math.abs(b.pixelY)>Math.abs(b.pixelX)))return!0;f=-b.pixelY}}else f=Math.abs(b.pixelX)>Math.abs(b.pixelY)?-b.pixelX*m:-b.pixelY;if(0===f)return!0;r.invert&&(f=-f);let y=i.getTranslate()+f*r.sensitivity;if(y>=i.minTranslate()&&(y=i.minTranslate()),y<=i.maxTranslate()&&(y=i.maxTranslate()),(l=!!i.params.loop||!(y===i.minTranslate()||y===i.maxTranslate()))&&i.params.nested&&s.stopPropagation(),i.params.freeMode&&i.params.freeMode.enabled){let T={time:n(),delta:Math.abs(f),direction:Math.sign(f)},S=u&&T.time<u.time+500&&T.delta<=u.delta&&T.direction===u.direction;if(!S){u=void 0;let E=i.getTranslate()+f*r.sensitivity,x=i.isBeginning,C=i.isEnd;if(E>=i.minTranslate()&&(E=i.minTranslate()),E<=i.maxTranslate()&&(E=i.maxTranslate()),i.setTransition(0),i.setTranslate(E),i.updateProgress(),i.updateActiveIndex(),i.updateSlidesClasses(),(!x&&i.isBeginning||!C&&i.isEnd)&&i.updateSlidesClasses(),i.params.loop&&i.loopFix({direction:T.direction<0?"next":"prev",byMousewheel:!0}),i.params.freeMode.sticky){clearTimeout(p),p=void 0,h.length>=15&&h.shift();let L=h.length?h[h.length-1]:void 0,P=h[0];if(h.push(T),L&&(T.delta>L.delta||T.direction!==L.direction))h.splice(0);else if(h.length>=15&&T.time-P.time<500&&P.delta-T.delta>=1&&T.delta<=6){let M=f>0?.8:.2;u=T,h.splice(0),p=a(()=>{!i.destroyed&&i.params&&i.slideToClosest(i.params.speed,!0,void 0,M)},0)}p||(p=a(()=>{!i.destroyed&&i.params&&(u=T,h.splice(0),i.slideToClosest(i.params.speed,!0,void 0,.5))},500))}if(S||o("scroll",s),i.params.autoplay&&i.params.autoplayDisableOnInteraction&&i.autoplay.stop(),r.releaseOnEdges&&(E===i.minTranslate()||E===i.maxTranslate()))return!0}}else{let k={time:n(),delta:Math.abs(f),direction:Math.sign(f),raw:e};h.length>=2&&h.shift();let A=h.length?h[h.length-1]:void 0;if(h.push(k),A?(k.direction!==A.direction||k.delta>A.delta||k.time>A.time+150)&&g(k):g(k),function e(t){let s=i.params.mousewheel;if(t.direction<0){if(i.isEnd&&!i.params.loop&&s.releaseOnEdges)return!0}else if(i.isBeginning&&!i.params.loop&&s.releaseOnEdges)return!0;return!1}(k))return!0}return s.preventDefault?s.preventDefault():s.returnValue=!1,!1}function $(e){let t=i.el;"container"!==i.params.mousewheel.eventsTarget&&(t=document.querySelector(i.params.mousewheel.eventsTarget)),t[e]("mouseenter",f),t[e]("mouseleave",m),t[e]("wheel",v)}function w(){return i.params.cssMode?(i.wrapperEl.removeEventListener("wheel",v),!0):!i.mousewheel.enabled&&($("addEventListener"),i.mousewheel.enabled=!0,!0)}function _(){return i.params.cssMode?(i.wrapperEl.addEventListener(event,v),!0):!!i.mousewheel.enabled&&($("removeEventListener"),i.mousewheel.enabled=!1,!0)}l("init",()=>{!i.params.mousewheel.enabled&&i.params.cssMode&&_(),i.params.mousewheel.enabled&&w()}),l("destroy",()=>{i.params.cssMode&&w(),i.mousewheel.enabled&&_()}),Object.assign(i.mousewheel,{enable:w,disable:_})},function e(t){let{swiper:i,extendParams:s,on:l,emit:r}=t;function a(e){let t;return e&&"string"==typeof e&&i.isElement&&(t=i.el.querySelector(e)||i.hostEl.querySelector(e))?t:(e&&("string"==typeof e&&(t=[...document.querySelectorAll(e)]),i.params.uniqueNavElements&&"string"==typeof e&&t&&t.length>1&&1===i.el.querySelectorAll(e).length?t=i.el.querySelector(e):t&&1===t.length&&(t=t[0])),e&&!t)?e:t}function n(e,t){let s=i.params.navigation;(e=_(e)).forEach(e=>{e&&(e.classList[t?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=t),i.params.watchOverflow&&i.enabled&&e.classList[i.isLocked?"add":"remove"](s.lockClass))})}function o(){let{nextEl:e,prevEl:t}=i.navigation;if(i.params.loop){n(t,!1),n(e,!1);return}n(t,i.isBeginning&&!i.params.rewind),n(e,i.isEnd&&!i.params.rewind)}function d(e){e.preventDefault(),(!i.isBeginning||i.params.loop||i.params.rewind)&&(i.slidePrev(),r("navigationPrev"))}function p(e){e.preventDefault(),(!i.isEnd||i.params.loop||i.params.rewind)&&(i.slideNext(),r("navigationNext"))}function c(){let e=i.params.navigation;if(i.params.navigation=q(i,i.originalParams.navigation,i.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let t=a(e.nextEl),s=a(e.prevEl);Object.assign(i.navigation,{nextEl:t,prevEl:s}),t=_(t),s=_(s);let l=(t,s)=>{t&&t.addEventListener("click","next"===s?p:d),!i.enabled&&t&&t.classList.add(...e.lockClass.split(" "))};t.forEach(e=>l(e,"next")),s.forEach(e=>l(e,"prev"))}function u(){let{nextEl:e,prevEl:t}=i.navigation;e=_(e),t=_(t);let s=(e,t)=>{e.removeEventListener("click","next"===t?p:d),e.classList.remove(...i.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),t.forEach(e=>s(e,"prev"))}s({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),i.navigation={nextEl:null,prevEl:null},l("init",()=>{!1===i.params.navigation.enabled?f():(c(),o())}),l("toEdge fromEdge lock unlock",()=>{o()}),l("destroy",()=>{u()}),l("enable disable",()=>{let{nextEl:e,prevEl:t}=i.navigation;if(e=_(e),t=_(t),i.enabled){o();return}[...e,...t].filter(e=>!!e).forEach(e=>e.classList.add(i.params.navigation.lockClass))}),l("click",(e,t)=>{let{nextEl:s,prevEl:l}=i.navigation;s=_(s),l=_(l);let a=t.target,n=l.includes(a)||s.includes(a);if(i.isElement&&!n){let o=t.path||t.composedPath&&t.composedPath();o&&(n=o.find(e=>s.includes(e)||l.includes(e)))}if(i.params.navigation.hideOnClick&&!n){if(i.pagination&&i.params.pagination&&i.params.pagination.clickable&&(i.pagination.el===a||i.pagination.el.contains(a)))return;let d;s.length?d=s[0].classList.contains(i.params.navigation.hiddenClass):l.length&&(d=l[0].classList.contains(i.params.navigation.hiddenClass)),!0===d?r("navigationShow"):r("navigationHide"),[...s,...l].filter(e=>!!e).forEach(e=>e.classList.toggle(i.params.navigation.hiddenClass))}});let h=()=>{i.el.classList.remove(...i.params.navigation.navigationDisabledClass.split(" ")),c(),o()},f=()=>{i.el.classList.add(...i.params.navigation.navigationDisabledClass.split(" ")),u()};Object.assign(i.navigation,{enable:h,disable:f,update:o,init:c,destroy:u})},function e(t){let{swiper:i,extendParams:s,on:l,emit:r}=t,a="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${a}-bullet`,bulletActiveClass:`${a}-bullet-active`,modifierClass:`${a}-`,currentClass:`${a}-current`,totalClass:`${a}-total`,hiddenClass:`${a}-hidden`,progressbarFillClass:`${a}-progressbar-fill`,progressbarOppositeClass:`${a}-progressbar-opposite`,clickableClass:`${a}-clickable`,lockClass:`${a}-lock`,horizontalClass:`${a}-horizontal`,verticalClass:`${a}-vertical`,paginationDisabledClass:`${a}-disabled`}}),i.pagination={el:null,bullets:[]};let n,o=0;function d(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function p(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function c(e){let t=e.target.closest(j(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=v(t)*i.params.slidesPerGroup;if(i.params.loop){var l,r,a;if(i.realIndex===s)return;let n=(l=i.realIndex,r=s,(l%=a=i.slides.length,(r%=a)===l+1)?"next":r===l-1?"previous":void 0);"next"===n?i.slideNext():"previous"===n?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function u(){let e=i.rtl,t=i.params.pagination;if(d())return;let s=i.pagination.el;s=_(s);let l,a,c=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,u=i.params.loop?Math.ceil(c/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(a=i.previousRealIndex||0,l=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(l=i.snapIndex,a=i.previousSnapIndex):(a=i.previousIndex||0,l=i.activeIndex||0),"bullets"===t.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let h=i.pagination.bullets,f,m,g;if(t.dynamicBullets&&(n=w(h[0],i.isHorizontal()?"width":"height"),s.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${n*(t.dynamicMainBullets+4)}px`}),t.dynamicMainBullets>1&&void 0!==a&&((o+=l-(a||0))>t.dynamicMainBullets-1?o=t.dynamicMainBullets-1:o<0&&(o=0)),g=((m=(f=Math.max(l-o,0))+(Math.min(h.length,t.dynamicMainBullets)-1))+f)/2),h.forEach(e=>{let i=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${t.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...i)}),s.length>1)h.forEach(e=>{let s=v(e);s===l?e.classList.add(...t.bulletActiveClass.split(" ")):i.isElement&&e.setAttribute("part","bullet"),t.dynamicBullets&&(s>=f&&s<=m&&e.classList.add(...`${t.bulletActiveClass}-main`.split(" ")),s===f&&p(e,"prev"),s===m&&p(e,"next"))});else{let $=h[l];if($&&$.classList.add(...t.bulletActiveClass.split(" ")),i.isElement&&h.forEach((e,t)=>{e.setAttribute("part",t===l?"bullet-active":"bullet")}),t.dynamicBullets){let b=h[f],y=h[m];for(let T=f;T<=m;T+=1)h[T]&&h[T].classList.add(...`${t.bulletActiveClass}-main`.split(" "));p(b,"prev"),p(y,"next")}}if(t.dynamicBullets){let S=Math.min(h.length,t.dynamicMainBullets+4),E=(n*S-n)/2-g*n,x=e?"right":"left";h.forEach(e=>{e.style[i.isHorizontal()?x:"top"]=`${E}px`})}}s.forEach((e,s)=>{if("fraction"===t.type&&(e.querySelectorAll(j(t.currentClass)).forEach(e=>{e.textContent=t.formatFractionCurrent(l+1)}),e.querySelectorAll(j(t.totalClass)).forEach(e=>{e.textContent=t.formatFractionTotal(u)})),"progressbar"===t.type){let a;a=t.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let n=(l+1)/u,o=1,d=1;"horizontal"===a?o=n:d=n,e.querySelectorAll(j(t.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${o}) scaleY(${d})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===t.type&&t.renderCustom?(e.innerHTML=t.renderCustom(i,l+1,u),0===s&&r("paginationRender",e)):(0===s&&r("paginationRender",e),r("paginationUpdate",e)),i.params.watchOverflow&&i.enabled&&e.classList[i.isLocked?"add":"remove"](t.lockClass)})}function h(){let e=i.params.pagination;if(d())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=_(s);let l="";if("bullets"===e.type){let a=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&a>t&&(a=t);for(let n=0;n<a;n+=1)e.renderBullet?l+=e.renderBullet.call(i,n,e.bulletClass):l+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(l=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(l=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&(t.innerHTML=l||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(j(e.bulletClass)))}),"custom"!==e.type&&r("paginationRender",s[0])}function f(){i.params.pagination=q(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let e=i.params.pagination;if(!e.el)return;let t;"string"==typeof e.el&&i.isElement&&(t=i.el.querySelector(e.el)),t||"string"!=typeof e.el||(t=[...document.querySelectorAll(e.el)]),t||(t=e.el),t&&0!==t.length&&(i.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(t)&&t.length>1&&(t=[...i.el.querySelectorAll(e.el)]).length>1&&(t=t.filter(e=>$(e,".swiper")[0]===i.el)[0]),Array.isArray(t)&&1===t.length&&(t=t[0]),Object.assign(i.pagination,{el:t}),(t=_(t)).forEach(t=>{"bullets"===e.type&&e.clickable&&t.classList.add(...(e.clickableClass||"").split(" ")),t.classList.add(e.modifierClass+e.type),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(t.classList.add(`${e.modifierClass}${e.type}-dynamic`),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&t.classList.add(e.progressbarOppositeClass),e.clickable&&t.addEventListener("click",c),i.enabled||t.classList.add(e.lockClass)}))}function m(){let e=i.params.pagination;if(d())return;let t=i.pagination.el;t&&(t=_(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",c))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}l("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=_(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),l("init",()=>{!1===i.params.pagination.enabled?b():(f(),h(),u())}),l("activeIndexChange",()=>{void 0===i.snapIndex&&u()}),l("snapIndexChange",()=>{u()}),l("snapGridLengthChange",()=>{h(),u()}),l("destroy",()=>{m()}),l("enable disable",()=>{let{el:e}=i.pagination;e&&(e=_(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),l("lock unlock",()=>{u()}),l("click",(e,t)=>{let s=t.target,l=_(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&l&&l.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;let a=l[0].classList.contains(i.params.pagination.hiddenClass);!0===a?r("paginationShow"):r("paginationHide"),l.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let g=()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=_(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),f(),h(),u()},b=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=_(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),m()};Object.assign(i.pagination,{enable:g,disable:b,render:h,update:u,init:f,destroy:m})},function e(t){let{swiper:i,extendParams:s,on:l}=t;function r(e,t){let i,s,l,r=(e,t)=>{for(s=-1,i=e.length;i-s>1;)e[l=i+s>>1]<=t?s=l:i=l;return i};this.x=e,this.y=t,this.lastIndex=e.length-1;let a,n;return this.interpolate=function e(t){return t?(a=(n=r(this.x,t))-1,(t-this.x[a])*(this.y[n]-this.y[a])/(this.x[n]-this.x[a])+this.y[a]):0},this}function n(){i.controller.control&&i.controller.spline&&(i.controller.spline=void 0,delete i.controller.spline)}s({controller:{control:void 0,inverse:!1,by:"slide"}}),i.controller={control:void 0},l("beforeInit",()=>{if("undefined"!=typeof window&&("string"==typeof i.params.controller.control||i.params.controller.control instanceof HTMLElement)){let e="string"==typeof i.params.controller.control?[...document.querySelectorAll(i.params.controller.control)]:[i.params.controller.control];e.forEach(e=>{if(i.controller.control||(i.controller.control=[]),e&&e.swiper)i.controller.control.push(e.swiper);else if(e){let t=`${i.params.eventsPrefix}init`,s=l=>{i.controller.control.push(l.detail[0]),i.update(),e.removeEventListener(t,s)};e.addEventListener(t,s)}});return}i.controller.control=i.params.controller.control}),l("update",()=>{n()}),l("resize",()=>{n()}),l("observerUpdate",()=>{n()}),l("setTranslate",(e,t,s)=>{i.controller.control&&!i.controller.control.destroyed&&i.controller.setTranslate(t,s)}),l("setTransition",(e,t,s)=>{i.controller.control&&!i.controller.control.destroyed&&i.controller.setTransition(t,s)}),Object.assign(i.controller,{setTranslate:function e(t,s){let l=i.controller.control,a,n,o=i.constructor;function d(e){if(e.destroyed)return;let t=i.rtlTranslate?-i.translate:i.translate;if("slide"===i.params.controller.by){var s;s=e,i.controller.spline=i.params.loop?new r(i.slidesGrid,s.slidesGrid):new r(i.snapGrid,s.snapGrid),n=-i.controller.spline.interpolate(-t)}n&&"container"!==i.params.controller.by||((Number.isNaN(a=(e.maxTranslate()-e.minTranslate())/(i.maxTranslate()-i.minTranslate()))||!Number.isFinite(a))&&(a=1),n=(t-i.minTranslate())*a+e.minTranslate()),i.params.controller.inverse&&(n=e.maxTranslate()-n),e.updateProgress(n),e.setTranslate(n,i),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(l))for(let p=0;p<l.length;p+=1)l[p]!==s&&l[p]instanceof o&&d(l[p]);else l instanceof o&&s!==l&&d(l)},setTransition:function e(t,s){let l=i.constructor,r=i.controller.control,n;function o(e){e.destroyed||(e.setTransition(t,i),0===t||(e.transitionStart(),e.params.autoHeight&&a(()=>{e.updateAutoHeight()}),function e(t,i){function s(e){e.target===t&&(i.call(t,e),t.removeEventListener("transitionend",s))}i&&t.addEventListener("transitionend",s)}(e.wrapperEl,()=>{r&&e.transitionEnd()})))}if(Array.isArray(r))for(n=0;n<r.length;n+=1)r[n]!==s&&r[n]instanceof l&&o(r[n]);else r instanceof l&&s!==r&&o(r)}})},function e(t){let{swiper:i,extendParams:l,on:r}=t;l({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),i.a11y={clicked:!1};let a=null,n,o,d=new Date().getTime();function p(e){let t=a;0!==t.length&&(t.innerHTML="",t.innerHTML=e)}function c(e){(e=_(e)).forEach(e=>{e.setAttribute("tabIndex","0")})}function u(e){(e=_(e)).forEach(e=>{e.setAttribute("tabIndex","-1")})}function h(e,t){(e=_(e)).forEach(e=>{e.setAttribute("role",t)})}function f(e,t){(e=_(e)).forEach(e=>{e.setAttribute("aria-roledescription",t)})}function g(e,t){(e=_(e)).forEach(e=>{e.setAttribute("aria-label",t)})}function $(e){(e=_(e)).forEach(e=>{e.setAttribute("aria-disabled",!0)})}function w(e){(e=_(e)).forEach(e=>{e.setAttribute("aria-disabled",!1)})}function b(e){if(13!==e.keyCode&&32!==e.keyCode)return;let t=i.params.a11y,s=e.target;if(!(i.pagination&&i.pagination.el&&(s===i.pagination.el||i.pagination.el.contains(e.target)))||e.target.matches(j(i.params.pagination.bulletClass))){if(i.navigation&&i.navigation.prevEl&&i.navigation.nextEl){let l=_(i.navigation.prevEl),r=_(i.navigation.nextEl);r.includes(s)&&(i.isEnd&&!i.params.loop||i.slideNext(),i.isEnd?p(t.lastSlideMessage):p(t.nextSlideMessage)),l.includes(s)&&(i.isBeginning&&!i.params.loop||i.slidePrev(),i.isBeginning?p(t.firstSlideMessage):p(t.prevSlideMessage))}i.pagination&&s.matches(j(i.params.pagination.bulletClass))&&s.click()}}function y(){return i.pagination&&i.pagination.bullets&&i.pagination.bullets.length}function T(){return y()&&i.params.pagination.clickable}let S=(e,t,i)=>{c(e),"BUTTON"!==e.tagName&&(h(e,"button"),e.addEventListener("keydown",b)),g(e,i),function e(t,i){(t=_(t)).forEach(e=>{e.setAttribute("aria-controls",i)})}(e,t)},E=e=>{o&&o!==e.target&&!o.contains(e.target)&&(n=!0),i.a11y.clicked=!0},x=()=>{n=!1,requestAnimationFrame(()=>{requestAnimationFrame(()=>{i.destroyed||(i.a11y.clicked=!1)})})},C=e=>{d=new Date().getTime()},L=e=>{if(i.a11y.clicked||!i.params.a11y.scrollOnFocus||new Date().getTime()-d<100)return;let t=e.target.closest(`.${i.params.slideClass}, swiper-slide`);if(!t||!i.slides.includes(t))return;o=t;let s=i.slides.indexOf(t)===i.activeIndex,l=i.params.watchSlidesProgress&&i.visibleSlides&&i.visibleSlides.includes(t);!s&&!l&&(e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(i.isHorizontal()?i.el.scrollLeft=0:i.el.scrollTop=0,requestAnimationFrame(()=>{n||(i.params.loop?i.slideToLoop(parseInt(t.getAttribute("data-swiper-slide-index")),0):i.slideTo(i.slides.indexOf(t),0),n=!1)})))},P=()=>{let e=i.params.a11y;e.itemRoleDescriptionMessage&&f(i.slides,e.itemRoleDescriptionMessage),e.slideRole&&h(i.slides,e.slideRole);let t=i.slides.length;e.slideLabelMessage&&i.slides.forEach((s,l)=>{let r=i.params.loop?parseInt(s.getAttribute("data-swiper-slide-index"),10):l,a=e.slideLabelMessage.replace(/\{\{index\}\}/,r+1).replace(/\{\{slidesLength\}\}/,t);g(s,a)})},M=()=>{let e=i.params.a11y;i.el.append(a);let t=i.el;e.containerRoleDescriptionMessage&&f(t,e.containerRoleDescriptionMessage),e.containerMessage&&g(t,e.containerMessage),e.containerRole&&h(t,e.containerRole);let l=i.wrapperEl,r=e.id||l.getAttribute("id")||`swiper-wrapper-${function e(t){let i=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(16).replace(/x/g,i)}(16)}`,n=i.params.autoplay&&i.params.autoplay.enabled?"off":"polite";!function e(t,i){(t=_(t)).forEach(e=>{e.setAttribute("id",i)})}(l,r),function e(t,i){(t=_(t)).forEach(e=>{e.setAttribute("aria-live",i)})}(l,n),P();let{nextEl:o,prevEl:d}=i.navigation?i.navigation:{};if(o=_(o),d=_(d),o&&o.forEach(t=>S(t,r,e.nextSlideMessage)),d&&d.forEach(t=>S(t,r,e.prevSlideMessage)),T()){let p=_(i.pagination.el);p.forEach(e=>{e.addEventListener("keydown",b)})}let c=s();c.addEventListener("visibilitychange",C),i.el.addEventListener("focus",L,!0),i.el.addEventListener("focus",L,!0),i.el.addEventListener("pointerdown",E,!0),i.el.addEventListener("pointerup",x,!0)};r("beforeInit",()=>{(a=m("span",i.params.a11y.notificationClass)).setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true")}),r("afterInit",()=>{i.params.a11y.enabled&&M()}),r("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{i.params.a11y.enabled&&P()}),r("fromEdge toEdge afterInit lock unlock",()=>{i.params.a11y.enabled&&function e(){if(i.params.loop||i.params.rewind||!i.navigation)return;let{nextEl:t,prevEl:s}=i.navigation;s&&(i.isBeginning?($(s),u(s)):(w(s),c(s))),t&&(i.isEnd?($(t),u(t)):(w(t),c(t)))}()}),r("paginationUpdate",()=>{i.params.a11y.enabled&&function e(){let t=i.params.a11y;y()&&i.pagination.bullets.forEach(e=>{i.params.pagination.clickable&&(c(e),i.params.pagination.renderBullet||(h(e,"button"),g(e,t.paginationBulletMessage.replace(/\{\{index\}\}/,v(e)+1)))),e.matches(j(i.params.pagination.bulletActiveClass))?e.setAttribute("aria-current","true"):e.removeAttribute("aria-current")})}()}),r("destroy",()=>{i.params.a11y.enabled&&function e(){a&&a.remove();let{nextEl:t,prevEl:l}=i.navigation?i.navigation:{};if(t=_(t),l=_(l),t&&t.forEach(e=>e.removeEventListener("keydown",b)),l&&l.forEach(e=>e.removeEventListener("keydown",b)),T()){let r=_(i.pagination.el);r.forEach(e=>{e.removeEventListener("keydown",b)})}let n=s();n.removeEventListener("visibilitychange",C),i.el&&"string"!=typeof i.el&&(i.el.removeEventListener("focus",L,!0),i.el.removeEventListener("pointerdown",E,!0),i.el.removeEventListener("pointerup",x,!0))}()})},function e(t){let{swiper:i,extendParams:l,on:r,emit:a,params:n}=t;i.autoplay={running:!1,paused:!1,timeLeft:0},l({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,d,p=n&&n.autoplay?n.autoplay.delay:3e3,c=n&&n.autoplay?n.autoplay.delay:3e3,u,h=new Date().getTime(),f,m,g,v,$,w,_;function b(e){if(i&&!i.destroyed&&i.wrapperEl&&e.target===i.wrapperEl){if(i.wrapperEl.removeEventListener("transitionend",b),!_&&(!e.detail||!e.detail.bySwiperTouchMove))L()}}let y=()=>{if(i.destroyed||!i.autoplay.running)return;i.autoplay.paused?f=!0:f&&(c=u,f=!1);let e=i.autoplay.paused?u:h+c-new Date().getTime();i.autoplay.timeLeft=e,a("autoplayTimeLeft",e,e/p),d=requestAnimationFrame(()=>{y()})},T=()=>{let e;if(!(e=i.virtual&&i.params.virtual.enabled?i.slides.filter(e=>e.classList.contains("swiper-slide-active"))[0]:i.slides[i.activeIndex]))return;let t=parseInt(e.getAttribute("data-swiper-autoplay"),10);return t},S=e=>{if(i.destroyed||!i.autoplay.running)return;cancelAnimationFrame(d),y();let t=void 0===e?i.params.autoplay.delay:e;p=i.params.autoplay.delay,c=i.params.autoplay.delay;let s=T();!Number.isNaN(s)&&s>0&&void 0===e&&(t=s,p=s,c=s),u=t;let l=i.params.speed,r=()=>{i&&!i.destroyed&&(i.params.autoplay.reverseDirection?!i.isBeginning||i.params.loop||i.params.rewind?(i.slidePrev(l,!0,!0),a("autoplay")):i.params.autoplay.stopOnLastSlide||(i.slideTo(i.slides.length-1,l,!0,!0),a("autoplay")):!i.isEnd||i.params.loop||i.params.rewind?(i.slideNext(l,!0,!0),a("autoplay")):i.params.autoplay.stopOnLastSlide||(i.slideTo(0,l,!0,!0),a("autoplay")),i.params.cssMode&&(h=new Date().getTime(),requestAnimationFrame(()=>{S()})))};return t>0?(clearTimeout(o),o=setTimeout(()=>{r()},t)):requestAnimationFrame(()=>{r()}),t},E=()=>{h=new Date().getTime(),i.autoplay.running=!0,S(),a("autoplayStart")},x=()=>{i.autoplay.running=!1,clearTimeout(o),cancelAnimationFrame(d),a("autoplayStop")},C=(e,t)=>{if(i.destroyed||!i.autoplay.running)return;clearTimeout(o),e||(w=!0);let s=()=>{a("autoplayPause"),i.params.autoplay.waitForTransition?i.wrapperEl.addEventListener("transitionend",b):L()};if(i.autoplay.paused=!0,t){$&&(u=i.params.autoplay.delay),$=!1,s();return}let l=u||i.params.autoplay.delay;u=l-(new Date().getTime()-h),i.isEnd&&u<0&&!i.params.loop||(u<0&&(u=0),s())},L=()=>{i.isEnd&&u<0&&!i.params.loop||i.destroyed||!i.autoplay.running||(h=new Date().getTime(),w?(w=!1,S(u)):S(),i.autoplay.paused=!1,a("autoplayResume"))},P=()=>{if(i.destroyed||!i.autoplay.running)return;let e=s();"hidden"===e.visibilityState&&(w=!0,C(!0)),"visible"===e.visibilityState&&L()},M=e=>{"mouse"===e.pointerType&&(w=!0,_=!0,i.animating||i.autoplay.paused||C(!0))},k=e=>{"mouse"===e.pointerType&&(_=!1,i.autoplay.paused&&L())},A=()=>{i.params.autoplay.pauseOnMouseEnter&&(i.el.addEventListener("pointerenter",M),i.el.addEventListener("pointerleave",k))},I=()=>{i.el&&"string"!=typeof i.el&&(i.el.removeEventListener("pointerenter",M),i.el.removeEventListener("pointerleave",k))},z=()=>{let e=s();e.addEventListener("visibilitychange",P)},O=()=>{let e=s();e.removeEventListener("visibilitychange",P)};r("init",()=>{i.params.autoplay.enabled&&(A(),z(),E())}),r("destroy",()=>{I(),O(),i.autoplay.running&&x()}),r("_freeModeStaticRelease",()=>{(g||w)&&L()}),r("_freeModeNoMomentumRelease",()=>{i.params.autoplay.disableOnInteraction?x():C(!0,!0)}),r("beforeTransitionStart",(e,t,s)=>{!i.destroyed&&i.autoplay.running&&(s||!i.params.autoplay.disableOnInteraction?C(!0,!0):x())}),r("sliderFirstMove",()=>{if(!i.destroyed&&i.autoplay.running){if(i.params.autoplay.disableOnInteraction){x();return}m=!0,g=!1,w=!1,v=setTimeout(()=>{w=!0,g=!0,C(!0)},200)}}),r("touchEnd",()=>{if(!i.destroyed&&i.autoplay.running&&m){if(clearTimeout(v),clearTimeout(o),i.params.autoplay.disableOnInteraction){g=!1,m=!1;return}g&&i.params.cssMode&&L(),g=!1,m=!1}}),r("slideChange",()=>{!i.destroyed&&i.autoplay.running&&($=!0)}),Object.assign(i.autoplay,{start:E,stop:x,pause:C,resume:L})},function e(t){let{swiper:i}=t;Object.assign(i,{appendSlide:U.bind(i),prependSlide:K.bind(i),addSlide:Z.bind(i),removeSlide:J.bind(i),removeAllSlides:Q.bind(i)})}];return W.use(ee),W}();
/*!
	js-cookie
	Copyright	Klaus Hartl, Fagner Brack, GitHub Contributors
	License		MIT
	Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions: The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software. THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
	Version		3.0.5

	https://github.com/js-cookie/js-cookie
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self,function(){var n=e.Cookies,o=e.Cookies=t();o.noConflict=function(){return e.Cookies=n,o}}())}(this,(function(){"use strict";function e(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)e[o]=n[o]}return e}var t=function t(n,o){function r(t,r,i){if("undefined"!=typeof document){"number"==typeof(i=e({},o,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var c="";for(var u in i)i[u]&&(c+="; "+u,!0!==i[u]&&(c+="="+i[u].split(";")[0]));return document.cookie=t+"="+n.write(r,t)+c}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],o={},r=0;r<t.length;r++){var i=t[r].split("="),c=i.slice(1).join("=");try{var u=decodeURIComponent(i[0]);if(o[u]=n.read(c,u),e===u)break}catch(e){}}return e?o[e]:o}},remove:function(t,n){r(t,"",e({},n,{expires:-1}))},withAttributes:function(n){return t(this.converter,e({},this.attributes,n))},withConverter:function(n){return t(e({},this.converter,n),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(n)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});return t}));
/*!
	Shopify.formatMoney
	Copyright	Stewart Knapman

	https://gist.github.com/stewartknapman/8d8733ea58d2314c373e94114472d44c
*/
var Shopify=Shopify||{};Shopify.money_format=(typeof general!=='undefined'&&general.currency)?general.currency:'{{amount}}';Shopify.formatMoney=function(cents,format){if(typeof cents=='string'){cents=cents.replace('.','')}
  var value='';var placeholderRegex=/\{\{\s*(\w+)\s*\}\}/;var formatString=(format||this.money_format);function defaultOption(opt,def){return(typeof opt=='undefined'?def:opt)}
  function formatWithDelimiters(number,precision,thousands,decimal){precision=defaultOption(precision,2);thousands=defaultOption(thousands,',');decimal=defaultOption(decimal,'.');if(isNaN(number)||number==null){return 0}
    number=(number/100.0).toFixed(precision);var parts=number.split('.'),dollars=parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,'$1'+thousands),cents=parts[1]?(decimal+parts[1]):'';return dollars+cents}
  switch(formatString.match(placeholderRegex)[1]){case 'amount':value=formatWithDelimiters(cents,2);break;case 'amount_no_decimals':value=formatWithDelimiters(cents,0);break;case 'amount_with_comma_separator':value=formatWithDelimiters(cents,2,'.',',');break;case 'amount_no_decimals_with_comma_separator':value=formatWithDelimiters(cents,0,'.',',');break}
  return formatString.replace(placeholderRegex,value)}

// Ratings
function createRatingsHtmlElement(rating, total) {
  const ratingElem = createElementWithClass("span", "rating");
  const split = rating.toString().split(".");
  const intRating = +split[0];
  let intDecimalRating = +split[1] || 0;

  for (let i = 1; i <= total; i++) {
    if (i <= intRating) {
      ratingElem.appendChild(createStarHtmlElement(100));
    } else if (intDecimalRating > 0) {
      const baseTenValue = (intDecimalRating + "0").substr(0, 2);
      ratingElem.appendChild(createStarHtmlElement(baseTenValue));
      intDecimalRating = 0;
    } else {
      ratingElem.appendChild(createStarHtmlElement(0));
    }
  }
  return ratingElem;
}

function createElementWithClass(tag, classList) {
  const tagElem = document.createElement(tag);
  if (!Array.isArray(classList)) {
    classList = [classList];
  }
  /*for (var i = 0; i < classList.length; i++) {
      tagElem.classList.add(classList[i])
  }*/
  tagElem.classList.add(...classList);
  return tagElem;
}

function createStarHtmlElement(fillPercentage) {
  const star = createElementWithClass("span", "star");
  const fill = createElementWithClass("span", "fill");
  fill.style.width = fillPercentage + "%";
  star.appendChild(fill);
  return star;
}

// Tabs
function semanticTabs(en) {
  "use strict";
  var tabs_header = en.children[0],
      tabs_header_items = tabs_header.querySelectorAll("li"),
      tabs_content = en.children[1],
      active_class = 'active',
      custom_active_class = '',
      classes;

  if (en.classList.contains('btn')) {
    active_class = 'link-btn';
  }

  if (en.dataset.activeClass) {
    custom_active_class = en.dataset.activeClass;
  }

  Array.from(tabs_content.children).forEach(function (el, index) {
    const classes = el.classList.contains('tab-closed') ? 'tabs-header' : 'tabs-header toggle';
    const dataIndex = el.getAttribute("data-index");
    if (dataIndex === null) {
      el.setAttribute("data-index", tabs_header_items.length - index);
    }
    el.innerHTML = `<a href="./" class="${classes}">Tabs Header</a> <div class="tabs-inner">${el.innerHTML}</div>`;
  });

  Array.from(tabs_content.querySelectorAll(".tabs-header")).forEach(function (el) {
    el.addEventListener("click", function (e) {
      el.classList.toggle("toggle");
      e.preventDefault();
    });
  });

  Array.from(tabs_header_items).forEach(function (el, index) {
    const dataIndex = el.getAttribute("data-index");
    if (dataIndex === null) {
      const newIndex = tabs_header_items.length - index;
      el.setAttribute("data-index", newIndex);
    }

    const d = el.dataset.index;
    const alias = el.querySelector('a').innerHTML;

    Array.from(tabs_content.querySelectorAll(`[data-index="${d}"] .tabs-header`)).forEach(function (el) {
      el.innerHTML = alias;
    });

    if (el.classList.contains(active_class)) {
      el.classList.add('item-active');
    }
    if (!en.classList.contains('static')) {
      Array.from(el.querySelectorAll('a')).forEach(function (link) {
        link.addEventListener('click', function (e) {
          tabs_header_items.forEach(function (item) {
            item.classList.remove(active_class);
            item.classList.remove('item-active');
            if (custom_active_class !== '') {
              item.classList.remove(custom_active_class);
            }
            item.classList.remove('active');
          });

          el.classList.add(active_class);
          el.classList.add('item-active');
          if (custom_active_class !== '') {
            el.classList.add(custom_active_class);
          }

          Array.from(tabs_content.children).forEach(function (content) {
            content.classList.add('hidden');
            if (content.matches(`[data-index="${d}"]`)) {
              content.classList.remove('hidden');
            }
          });
          if (link.getAttribute('href') === './' || link.getAttribute('href') === '#' || (typeof Shopify !== 'undefined' && Shopify.designMode)) {
            e.preventDefault();
          }
        });
      });
    }
  })
}