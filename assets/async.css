
/*! Async - Mixins --------- */
/* show */	.changed .swiper-custom-fraction .swiper-pagination-current, .changed ~ .swiper-custom-pagination .swiper-custom-fraction .swiper-pagination-current { visibility: visible; opacity: 1; }

.slider-fraction.changed .swiper-custom-pagination .swiper-pagination-current, .slider-fraction.changed .swiper-custom-pagination .swiper-button-prev, .slider-fraction .changed .swiper-custom-pagination .swiper-pagination-current, .slider-fraction .changed .swiper-custom-pagination .swiper-button-prev, .slider-fraction.changed .swiper-custom-pagination .swiper-button-prev, .slider-fraction .changed ~ .swiper-custom-pagination .swiper-button-prev, .no-thumbs-mobile.changed .swiper-pagination-current, .no-thumbs-mobile.changed ~ .swiper-custom-pagination .swiper-pagination-current { visibility: visible; opacity: 1; }


/*! Async - Forms --------- */
.f8ps, .has-panels .m6pn, .overlay-close, .overlay-close-clipping { transition-property: all; transition-duration: 0.4s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
.f8ps, .has-panels .m6pn, #nav-user > ul > li > form, .l4cl li, .has-filters .m6cl > aside, #cookie-bar, .overlay-close, .overlay-close-clipping { transition-property: transform, visibility, opacity, backdrop-filter; }

#root button.disabled, .shopify-section-header li.disabled, button[disabled], input[type="button"][disabled], input[type="reset"][disabled], input[type="submit"][disabled], .no-click { cursor: default; pointer-events: none; }


/*! Async - Helpers --------- */
button:after, .check.switch label:before, .link-btn a:after, button:before, .link-btn a:before, .shopify-section-announcement-bar a.close, .l4cl/*.category*/ li, .l4cl/*.category*/ a:after, #root, #nav-user > ul > li > a i span:before, .custom-progressbar > *, #nav-user > ul > li > form, #nav-user > ul > li > form ~ .toggle, .has-filters #root > .f8fl-toggle, .has-filters .m6cl > aside, #cookie-bar, .fancybox__slide, .l4ft figure img, .l4cl.inline li, .l4cl.inline a:after, .l4ne li, .l4ne a:before, .swiper-pagination-bullet:before, /*.fancybox__container .carousel__button,*/ .l4cl .link-btn, .l4al a.close, img, video, iframe, picture, #root > .m6pn-close, .icon-chevron-right, .icon-chevron-left, .l4cl figure .link-btn, .l4cl figure form, .l4ft li, #totop, .l4us.wide.s4wi .swiper-slide { transition-property: all; transition-duration: 0.4s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
#root { transition-property: padding; }
#nav-user > ul > li > form, .l4cl/*.category*/ li, .has-filters .m6cl > aside, #cookie-bar, .icon-chevron-left, .icon-chevron-right, .l4cl figure .link-btn, .l4cl figure form, .l4ft li, .l4us.wide.s4wi .swiper-slide { transition-property: transform, visibility, opacity; }
#nav-user > ul > li > form ~ .toggle, .fancybox__slide, img, video, iframe, picture, #root > .m6pn-close { transition-property: visibility, opacity; }
 .l4cl .link-btn { transition-property: transform, visibility, opacity; } 
.check.switch label:before { transition-property: left, right; }
#search button:before, .fancybox__container .carousel__button:before { transition: none; }

.m6fr .swiper-slide, .transition .swiper-button-nav { transition-property: all; transition-duration: 0.4s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
.transition .swiper-button-nav { transition-property: height; }


/*! Async - Miscellaneous --------- */
::selection { background: var(--secondary_bg); color: var(--white); text-shadow: none; opacity: 1; }
::-moz-selection { background: var(--secondary_bg); color: var(--white); text-shadow: none; opacity: 1; }


/*! Async -Outlines --------- */
[role="button"], a { outline: none; }
[data-whatintent="keyboard"] a:focus, [data-whatintent="keyboard"] button:focus, [data-whatintent="keyboard"] button[role="button"]:focus, [data-whatintent="keyboard"] summary:focus, [data-whatintent="keyboard"] input:focus, [data-whatintent="keyboard"] select:focus, [data-whatintent="keyboard"] textarea:focus, [data-whatintent="keyboard"] .shopify-section-header > input:focus + label, [data-whatintent="keyboard"] #header > input:focus + label, [data-whatintent="keyboard"] #header-inner > input:focus + label, [data-whatintent="keyboard"] #nav > ul > li > input:focus ~ label, [data-whatintent="keyboard"] .check input:focus ~ label:before, [data-whatintent="keyboard"] [role="button"]:focus { outline-offset: 2px; outline-width: 2px; outline-style: solid; outline-color: var(--secondary_bg); }
[data-whatintent="keyboard"] button:focus, [data-whatintent="keyboard"] .link-btn a:focus { outline-offset: 2px; }
[data-whatintent="keyboard"] .l4ft figure a:focus:after { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; border: 2px solid var(--secondary_bg); }
[data-whatintent="mouse"] input:focus, [data-whatintent="mouse"] select:focus, [data-whatintent="mouse"] textarea:focus, [data-whatintent="keyboard"] .l4pr li a:focus { outline: none; }
[data-whatintent="touch"] button, [data-whatintent="touch"] a, [data-whatintent="touch"] input, [data-whatintent="touch"] select, [data-whatintent="touch"] textarea, [data-whatintent="touch"] [role="button"] { outline: none !important; }


/* Animations --------- */
@keyframes spin { 0% { transform: none; } 100% { transform: rotate(360deg); } }