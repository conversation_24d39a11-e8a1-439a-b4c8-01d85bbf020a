.m6ca:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.m6ca { position: relative; z-index: 2; margin-bottom: 20px; padding: 16px 16px 1px 350px; }
	.m6ca:before { background: var(--sand); }
	.m6ca > * { width: 100%; margin-bottom: 0; padding-top: 6px; }
	.m6ca > header { width: 350px; margin-left: -350px; padding-left: 25px; padding-right: 25px; }
	.m6ca > .l4cl { margin-top: 0; margin-bottom: 0; padding-top: 0; }
	.m6ca .l4cl li { margin-bottom: 16px; padding: 20px 20px 16px; }
	.m6ca .l4cl li:before { background: var(--white); }
.m6ca { 
	display: flex;
	justify-content: space-between; 
}
@media only screen and (max-width: 1356px) {
.m6ca { padding-left: 335px; }
	.m6ca > header { width: 335px; margin-left: -335px; }
}
@media only screen and (max-width: 1200px) {
.m6ca { padding-left: 320px; }
	.m6ca > header { width: 320px; margin-left: -320px; }
	#root .m6ca .l4cl.list .price span { display: block; margin-left: 0; margin-right: 0; line-height: var(--main_lh_h); }
}
@media only screen and (max-width: 1100px) { 
.m6ca, #root .m6ca { display: block; padding-left: 16px; padding-right: 16px; }
	.m6ca > header, #root .m6ca > header { width: auto; margin-left: 0; margin-right: 0; padding-left: 9px; padding-right: 9px; }
	#root .m6ca .l4cl.list .price span { display: inline; margin-left: 0; margin-right: 2px; }
}
@media only screen and (max-width: 760px) {
.m6ca, #root .m6ca { margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-top: 16px; border-top-width: 0; }
	#root .m6ca > header { padding: 0; }
	.m6ca > header h1, .m6ca > header h2, .m6ca > header h3, .m6ca > header h4, .m6ca > header h5, .m6ca > header h6 { margin-bottom: 8px; font-size: var(--size_16_f); }
	.m6ca > .l4cl { margin-bottom: 20px; }
		.m6ca > header + .l4cl { margin-top: -4px; }
}