/*! Async - Defaults --------- */
[data-whatintent="mouse"] h1 a:hover, [data-whatintent="mouse"] h2 a:hover, [data-whatintent="mouse"] h3 a:hover, [data-whatintent="mouse"] h4 a:hover, [data-whatintent="mouse"] h5 a:hover, [data-whatintent="mouse"] h6 a:hover { color: inherit; }

:root {
	--pulse: pulse;
	--pulse_classic: pulse_classic;
	--pulse_header: pulse_header;
	--pulse_footer: pulse_footer;
	--pulse_dark: pulse_dark;
	--pulse_custom: pulse_custom;
}
.no-hover-pulse {
	--pulse: none;
	--pulse_classic: none;
	--pulse_header: none;
	--pulse_footer: none;
	--pulse_dark: none;
	--pulse_custom: none;
}


/*! Async - Mixins --------- */
/* show */	[data-whatintent="mouse"] .l4ca footer p a:hover i, [data-whatintent="mouse"] #nav-top > ul > li > ul li a:hover i, [data-whatintent="mouse"] #nav-user > ul > li > ul a:hover i, [data-whatintent="mouse"] .l4dr ul li a:hover i, [data-whatintent="mouse"] #nav-top > ul > li > form ul li a:hover i, [data-whatintent="mouse"] .m6fr.slider-fraction .swiper-custom-pagination .swiper-button-nav:hover { visibility: visible; opacity: 1; }
/* cu:d */ 	.bv_mainselect .bv_ul_inner .li.bv_disabled, .l4vw li.active *, .l4in li.active a, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active *, .l4pr .swiper-pagination-bullets li.swiper-pagination-bullet-active, .bv_mainselect .bv_ul_inner .li.active a, .n6pg li.active a, .check input[type="radio"]:checked ~ label, .check input[type="radio"]:checked ~ label:before, .shopify-section-footer li.active > a, #nav > ul > li.active > a, [disabled], .disabled { cursor: default; }
.link-mute a:before { display: block; position: absolute; left: 0; right: 0; top: 50%; margin: -10px 0 0; font-weight: 400; font-family: i; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal; }
/* show */ [data-whatintent="mouse"] .l4cl li:hover .check.color, [data-whatintent="mouse"] .l4cl li:hover .check.plain, [data-whatintent="mouse"] .n6br a:hover, [data-whatintent="mouse"] a:hover .icon-print, [data-whatintent="mouse"] #search .clear-toggle:hover:before, [data-whatintent="mouse"] #root .l4ca.xpert section p.has-close a:hover i { visibility: visible; opacity: 1; }
/* tr:n*/	[data-whatintent="mouse"] .l4cl li:hover .check.plain { transform: none; }
/* td:u */ 	[data-whatintent="mouse"] #root a.text-no-underline:hover, [data-whatintent="mouse"] #root .l4cl p:not(.link-btn) a:hover, [data-whatintent="mouse"] #root .l4ft p a.strong span:hover, [data-whatintent="mouse"] #root .l4cl.category a span:hover, [data-whatintent="mouse"] .l4us a:hover, [data-whatintent="mouse"] #nav-top .li > a:hover, [data-whatintent="mouse"] .l4al a:hover, [data-whatintent="mouse"] #root button.inline:hover, [data-whatintent="mouse"] #root .l4cl a.link-more:hover, [data-whatintent="mouse"] #root .l4cn a:hover, [data-whatintent="mouse"] nav .l4in a:hover, [data-whatintent="mouse"] #search div a:hover, [data-whatintent="mouse"] #root .l4rv footer a:hover, [data-whatintent="mouse"] .shopify-section-header li.overlay-theme > a:hover, [data-whatintent="mouse"] #root .link-btn a.inline:hover, [data-whatintent="mouse"] .l4ca p:not(.removed) a:hover, [data-whatintent="mouse"] .n6pg li a:hover, [data-whatintent="mouse"] .l4cl a:hover, [data-whatintent="mouse"] a.strong:hover, [data-whatintent="mouse"] .n6br a:hover, [data-whatintent="mouse"] a.overlay-content:hover, [data-whatintent="mouse"] .l4dr ul li a:hover, [data-whatintent="mouse"] h1 a:hover, [data-whatintent="mouse"] h2 a:hover, [data-whatintent="mouse"] h3 a:hover, [data-whatintent="mouse"] h4 a:hover, [data-whatintent="mouse"] h5 a:hover, [data-whatintent="mouse"] h6 a:hover, [data-whatintent="mouse"] #root .l4ca li a.link-more:hover, [data-whatintent="mouse"] a.overlay-gray:hover, [data-whatintent="mouse"] a.overlay-c:hover, [data-whatintent="mouse"] #root .l4ne p a span:hover, [data-whatintent="mouse"] #root .l4ft a.strong span:hover, [data-whatintent="mouse"] .spr-pagination > div > * a:hover, [data-whatintent="mouse"] #root .f8fl .check .link-more a:hover, [data-whatintent="mouse"] #root .f8fl .check a.link-more:hover, [data-whatintent="mouse"] .submit > a:hover, [data-whatintent="mouse"] #root .l4cl a.strong:hover, [data-whatintent="mouse"] #root .l4ml a.link-more:hover { text-decoration: underline; }
/* td:n */ 	#root [data-whatintent="mouse"] #root .l4cl.inline-links p a:hover, [data-whatintent="mouse"] .shopify-section-footer .l4us a:hover, [data-whatintent="mouse"] #nav-top > .l4us a:hover, [data-whatintent="mouse"] #nav-top > .l4us .longer a.linked:hover, [data-whatintent="mouse"] #root #nav-user > ul > li > a:hover i span, [data-whatintent="mouse"] #root .l4cn.box a:hover, [data-whatintent="mouse"] #root .shopify-section-footer .l4cn a:hover, [data-whatintent="mouse"] #root .l4rv footer .size-12 a:hover, [data-whatintent="mouse"] .size-12 a.overlay-content:hover, [data-whatintent="mouse"] .l4rv h1 a.small:hover, [data-whatintent="mouse"] .l4rv h2 a.small:hover, [data-whatintent="mouse"] .l4rv h3 a.small:hover, [data-whatintent="mouse"] .l4rv h4 a.small:hover, [data-whatintent="mouse"] .l4rv h5 a.small:hover, [data-whatintent="mouse"] .l4rv h6 a.small:hover, [data-whatintent="mouse"] .l4rv p a:hover, [data-whatintent="mouse"] #nav .m6cn p a:hover, [data-whatintent="mouse"] #nav-bar .m6cn p a:hover, [data-whatintent="mouse"] a:hover, #root .l4ft p a.strong, #root .n6br p a, [data-whatintent="mouse"] a.overlay-content.text-underline:hover, #root .l4ca.xpert section p.has-close a { text-decoration: none; }

.m6fr .swiper-pagination-bullets, [data-whatintent="mouse"] .shopify-section-header li.show-all > a, .m6fr figure:before, .l4ft .img-overlay, figure .img-overlay, .pointer-events-none, .l4cl .active figure, html:not(.cart-hover) #header .l4al, #root .swiper-button-disabled, a[data-panel] *, #nav-top > ul > li > a[role="none"], .m6fr article > .link-btn { pointer-events: none; }
#root button.disabled, button[disabled], input[type="button"][disabled], input[type="reset"][disabled], input[type="submit"][disabled], button[disabled], input[type="button"][disabled], input[type="reset"][disabled], input[type="submit"][disabled], .link-btn a.disabled { cursor: default; pointer-events: none; }

.m6fr article > .link-btn { z-index: 999; }
.accordion-a summary label { pointer-events: none; }
.m6fr article > .link-btn a, .l4ft .main > .link-btn a, figure .s1lb a, .l4pr .s1lb a { pointer-events: auto; }

.l4cl .active figure, .l4in input[type="radio"]:checked ~ label, #nav-top > ul > li > a[role="none"] { cursor: default; }
.l4cl.small input:not(:checked) ~ label figure, .l4in input ~ label { cursor: pointer; }

[data-whatintent="mouse"] #root .l4cl p:not(.link-btn, .link-more) a:has(i):hover { text-decoration: none; }
[data-whatintent="mouse"] #root .l4cl p:not(.link-btn, .link-more) a:has(i):hover span { text-decoration: none; }
[data-whatintent="mouse"] #root a.strong.link-more:hover, [data-whatintent="mouse"] #root .l4cl a.strong.link-more:hover { text-decoration: underline; }

.link-btn a.circle:not(.loading):after, form:not(.processing) button.circle:not(.loading):after, #root .l4cl.box a.remove:after, .l4hs-l li > a.toggle:before { content: ""; display: block; position: absolute; left: 50%; top: 50%; right: auto; bottom: auto; z-index: 8; width: 100%; min-width: 44px; height: 100%; min-height: 44px; margin: 0; padding: 0; box-shadow: none; border-radius: 0; border-width: 0; transform: translate(-50%, -50%); }


/*! Async - Layout --------- */
[data-theme="xpert"] #root > .overlay-close, [data-theme="xpert"] .shopify-section-header .overlay-close, [data-theme="xpert"] .shopify-section-announcement-bar .overlay-close, [data-theme="xpert"] .overlay-close-clipping { -webkit-backdrop-filter: blur(3px); backdrop-filter: blur(3px); }

[data-whatintent="mouse"] #nav-top > ul > li > a:hover, [data-whatintent="mouse"] .shopify-section-header .l4us.s4wi .swiper-button-nav:hover, [data-whatintent="mouse"] #root .shopify-section-header .l4us a.next-item:hover, [data-whatintent="mouse"] #nav-user > ul > li.link-btn > a:hover { color: var(--custom_top_up_fg_hover); }
[data-whatintent="mouse"] #nav-top > ul > li > a:hover path { fill: var(--custom_top_up_fg_hover); }
.hover-inv[data-whatintent="mouse"] #root #header-inner > .link-btn a.inv:hover, .hover-inv[data-whatintent="mouse"] #root #nav-user > ul > li.link-btn > a.inv:hover { color: var(--custom_top_main_link_text); }
html[data-whatintent="mouse"]:not(.hover-inv) #header-inner > .link-btn a.inv:hover:before, html[data-whatintent="mouse"]:not(.hover-inv) #nav-user > ul > li.link-btn > a.inv:hover:before { background: none; }
[data-whatintent="mouse"] .shopify-section-header > .link-btn a:hover:before, [data-whatintent="mouse"] #header > .link-btn a:not(.inv):hover:before, [data-whatintent="mouse"] #header-inner > .link-btn a:not(.inv):hover:before, [data-whatintent="mouse"] #root #nav-user > ul > li > a:hover i span:before, [data-whatintent="mouse"] #nav-user > ul > li.link-btn > a:hover:before { background-color: var(--custom_top_main_link_dark); }
@media only screen and (min-width: 1001px) {
	[data-whatintent="mouse"] #header-outer li.show-all:hover:before { border-color: var(--custom_top_main_link_bg); background: var(--custom_top_main_link_bg); opacity: 1; }
	[data-whatintent="mouse"] #header-outer li.show-all:hover:after { color: var(--custom_top_main_link_text); }

	[data-whatintent="mouse"] .shopify-section-header > .link-btn a:hover, [data-whatintent="mouse"] #header > .link-btn a:hover, [data-whatintent="mouse"] #header-inner > .link-btn a:hover, [data-whatintent="mouse"] #root #nav-user > ul > li > a:hover i span, [data-whatintent="mouse"] #nav-user > ul > li.link-btn > a:hover { animation-name: var(--pulse_header); }
}
@media only screen and (max-width: 1000px) {
	[data-whatintent="mouse"] .shopify-section-header > .link-btn a:hover:before, [data-whatintent="mouse"] #header > .link-btn a:hover:before, [data-whatintent="mouse"] #header-inner > .link-btn a:hover:before, [data-whatintent="mouse"] .shopify-section-header > .link-btn a:hover, [data-whatintent="mouse"] #header > .link-btn a:hover, [data-whatintent="mouse"] #header-inner > .link-btn a:hover, [data-whatintent="mouse"] #root #nav-user > ul > li > a:hover i span { animation: none; background: none; }
	#nav.fixed + #distance-spacer, #nav-bar.fixed + #distance-spacer { display: none; }
}
[data-whatintent="mouse"] .shopify-section-footer > div a:hover { color: var(--custom_footer_fg_bottom_hover); }
[data-whatintent="mouse"] .shopify-section-footer .link-btn a:hover:before, [data-whatintent="mouse"] .shopify-section-footer button:hover:before { background-color: var(--custom_footer_link_dark); }
[data-whatintent="mouse"] .shopify-section-footer .link-btn a:hover, [data-whatintent="mouse"] .shopify-section-footer button:hover { animation-name: var(--pulse_footer); }
[data-whatintent="mouse"] .shopify-section-footer > nav a:hover, .shopify-section-footer nav li.active > a, [data-whatintent="mouse"] .shopify-section-footer a:hover { color: var(--custom_footer_fg_hover); }
[data-whatintent="mouse"] .shopify-section-announcement-bar a.close:hover, [data-whatintent="mouse"] .m6pn > .m6pn-close:hover { color: inherit; }
[data-whatintent="mouse"] .shopify-section-announcement-bar > a.close:hover, [data-whatintent="mouse"] .m6pn > .m6pn-close:hover, [data-whatintent="mouse"] .popup-a .box-inset > .close:hover, [data-whatintent="mouse"] .fancybox__container .carousel__button.fancybox__button--close:hover, [data-whatintent="mouse"] .fancybox__content > .carousel__button.is-close:hover, [data-whatintent="mouse"] .l4al a.close:hover, [data-whatintent="mouse"] .recommendation-modal__close-button-container button:hover:after, [data-whatintent="mouse"] .m6pn-close:hover { transform: rotate(90deg); }


@media only screen and (min-width: 761px) {
	[data-whatintent="mouse"] #root #nav-user > ul > li.link-btn > a:hover { animation-name: var(--pulse_header); }
}
.fixed + #distance-spacer { display: block; }

[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh .shopify-section-header.transparent.has-no-wide:not(.fixed):before { display: block; }
[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh .shopify-section-header.transparent.has-no-wide:not(.fixed) #nav-outer:before { display: none; }

[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh .shopify-section-header.transparent.tr_h:before { /*border-width: 0;*/ border-bottom-width: 1px; background: var(--custom_top_main_bg); opacity: 1; }

[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh .shopify-section-header.transparent.tr_h:not(.fixed) #nav:before, [data-whatintent="mouse"].has-first-m6fr-wide.tr_hh .shopify-section-header.transparent.tr_h:not(.fixed) #nav-bar:before { border-bottom-width: 1px; }

[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh:not(.m2a) .shopify-section-header.transparent.tr_h:not(.fixed) #nav.no-wide:after, [data-whatintent="mouse"].has-first-m6fr-wide:not(.m2a) .shopify-section-header.transparent.tr_h:not(.fixed) #nav-bar.no-wide:after { background: var(--custom_top_nav_bg); }

[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh:not(.m2a) .shopify-section-header.transparent.tr_h:not(.fixed) #nav:not(.fixed):before,
[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh:not(.m2a) .shopify-section-header.transparent.tr_h:not(.fixed) #nav-bar:not(.fixed):before { background: var(--custom_top_nav_bg); opacity: 1; }

@media only screen and (max-width: 760px) {
	[data-whatintent="mouse"].has-first-m6fr-wide.tr_hh:not(.m2a) .shopify-section-header:not(.fixed) #search.no-bg:before { background: var(--custom_top_search_bg_cont); opacity: 1; }
}

/*! Async - Modules --------- */
/*.m6cp {}*/
.m6cp > div { position: relative; z-index: 2; padding: var(--btn_ph) var(--btn_ph) max(0.1px, calc(var(--btn_ph) - var(--main_mr))); background: var(--white); }
.m6cp > div > .link-btn:last-child { margin-bottom: calc(var(--btn_ph) - var(--btn_dist2)); }
.m6cp > footer { padding: var(--btn_ph); background: var(--sand); }
.m6cp > footer .link-btn, .m6cp > footer .link-btn > * { margin-bottom: 0; }
.m6cp > footer .link-btn a { width: 100%; }
#root .m6cp .l4cl.box { margin-bottom: 0; margin-left: 0; margin-right: 0; --img_w: 52px; }
.m6cp .l4cl.box h1, .m6cp .l4cl.box h2, .m6cp .l4cl.box h3, .m6cp .l4cl.box h4, .m6cp .l4cl.box h5, .m6cp .l4cl.box h6 { font-size: var(--fz_main); }
.m6cp .l4cl.box .price:not(:first-child) { margin-top: 0; }
.m6cp .l4cl.box p:not(:first-child) { margin-top: 2px; }
#root .m6cp .l4cl.box > li { width: calc(100% + var(--ds)) !important; margin-left: calc(0px - var(--ds)); padding-left: 0; padding-right: 0; }
#root .m6cp .l4cl.box > li:first-child { padding-top: 0; }
#root .m6cp .l4cl.box > li:before { left: var(--ds); border-left-width: 0; border-right-width: 0; border-top-width: var(--bw); border-bottom-width: 0 !important; background: none; }
#root .m6cp .l4cl.box > li:first-child:before { border-top-width: 0; }
#root .m6cp .l4cl.box > li:last-child:before { bottom: 0 !important; }
#root .l4cl.box a.remove {
	display: block; position: relative; z-index: 12; text-decoration: none; opacity: .5;
	align-self: flex-start;
}
#root .l4cl.box a.remove i { font-size: 22px; line-height: 1; }
[data-whatintent="mouse"] #root .l4cl.box a.remove:hover { color: var(--secondary_bg); opacity: 1; }
.m6cp-open .m6cp { width: min(100%, 435px); transform: none; }
.m6cp-open .m6cp > header .link-btn > a [class*="icon-chevron-"] { transform: rotate(180deg); }

@media only screen and (max-width: 760px) {
	.m6cp > div { overflow-x: hidden; overflow-y: auto; max-height: calc(100vh - var(--btn_ph) * 2 - 2 * (var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh))); }
	#root .m6cp .l4cl.box { --img_w: 44px; }
}

.m6tb:not(.static) li.item-active { pointer-events: none; }

.swiper-pagination-bullet.swiper-pagination-bullet-active { cursor: default; }
[data-whatintent="mouse"] .swiper-pagination-bullet:hover:before, [data-whatintent="mouse"] .s1tt a:hover ~ .icon-info, [data-whatintent="mouse"] a.s1tt:hover .icon-info, .cart-hover #header .l4al { visibility: visible; opacity: 1; }

.recommendation-modal__container { visibility: visible; opacity: 1; }
.recommendation-modal__content { display: block !important; padding: 0 !important; }
.recommendation-modal__close-button-container { display: block; position: absolute; right: 0; top: 0; }
.recommendation-modal__close-button-container button, [data-whatintent="mouse"] .recommendation-modal__close-button-container button:hover, .recommendation-modal__close-button-container button:focus { width: 44px !important; min-width: 44px !important; height: 44px !important; margin: 0 !important; box-shadow: none !important; color: var(--gray_text) !important; font-size: var(--size_12_f) !important; text-align: left; text-indent: -3000em; direction: ltr; outline: none !important; }
html .recommendation-modal__container .recommendation-modal__close-button-container button:hover, html .recommendation-modal__container .recommendation-modal__close-button-container button:focus { width: 44px !important; min-width: 44px !important; outline: none !important; }
[data-whatintent="mouse"] .recommendation-modal__close-button-container button:hover { color: var(--secondary_bg) !important; }
.recommendation-modal__close-button-container button:before, .recommendation-modal__button--minimal:before { display: none; }
.recommendation-modal__close-button-container button:after { content: "\e91f"; }
.recommendation-modal__flag, .recommendation-modal__benefits ul, .recommendation-modal__content > *:last-child, .recommendation-modal__container .recommendation-modal__content > .recommendation-modal__button--minimal:last-child { display: block; float: none !important; margin-bottom: var(--main_mr) !important; padding: 0 !important; box-shadow: none !important; }
.recommendation-modal__container .recommendation-modal__content > .recommendation-modal__button--minimal:last-child { text-decoration: underline; }
.recommendation-modal__flag img { display: block; margin: 0 auto; border: 1px solid var(--alto); }
.recommendation-modal__benefits ul, .recommendation-modal__container .recommendation-modal__benefits ul li { list-style: none !important; padding: 0 !important; font-size: 1em !important; line-height: inherit !important; }
.recommendation-modal__benefits ul, .recommendation-modal__benefits ul li { margin-bottom: 0 !important; }
.recommendation-modal__benefits { margin-bottom: var(--main_mr) !important; }
.recommendation-modal__selector-wrapper--flag { display: block !important; margin-bottom: var(--main_mr); }
.recommendation-modal__form button, .recommendation-modal__container .recommendation-modal__form button:last-of-type { float: none !important; width: 100% !important; height: auto !important; margin: 0 0 16px !important; padding: var(--btn_pv) var(--btn_ph) !important; background: none !important; color: var(--secondary_text) !important; font-size: var(--btn_fz) !important; line-height: var(--main_lh_h) !important; outline: none !important; }
.recommendation-modal__form button:before { display: block !important; }
.recommendation-modal__container .recommendation-modal__button--minimal { display: block !important; width: 100% !important; margin: -4px 0 18px !important; border-radius: 0 !important; color: var(--gray) !important; font-weight: var(--main_fw) !important; font-style: var(--main_fs) !important; font-size: var(--main_fz) !important; font-family: var(--main_ff) !important; line-height: inherit !important; text-decoration: none !important; text-transform: var(--main_tt) !important; letter-spacing: var(--main_ls) !important; outline: none !important; }
[data-whatintent="mouse"] .recommendation-modal__container .recommendation-modal__button--minimal:hover { width: 100% !important; text-decoration: none !important; outline: none !important; }
[data-whatintent="mouse"] .recommendation-modal__container .recommendation-modal__button--minimal:hover { text-decoration: underline !important; }
html .recommendation-modal__container .recommendation-modal__button--minimal:hover, html .recommendation-modal__container .recommendation-modal__button--minimal:focus { width: 100% !important; min-width: 100% !important; outline: none !important; }
[data-whatin] .recommendation-modal__container h1, [data-whatin] .recommendation-modal__container h2, [data-whatin] .recommendation-modal__container h3, [data-whatin] .recommendation-modal__container h4, [data-whatin] .recommendation-modal__container h5, [data-whatin] .recommendation-modal__container h6, [data-whatin] .recommendation-modal__message, html .recommendation-modal__message { margin: 0 0 12px !important; font-style: var(--main_fs_h) !important; font-weight: var(--main_fw_h) !important; font-size: var(--main_h5) !important; line-height: var(--main_lh_h) !important; text-align: center !important; text-transform: var(--main_tt_h) !important; letter-spacing: var(--main_ls_h) !important; }
[data-whatin] .recommendation-modal__message, html .recommendation-modal__message { font-size: var(--main_h5) !important; }
.recommendation-modal__backdrop { visibility: visible; opacity: .2; }


/*! Async - Content --------- */
[data-whatintent="mouse"] a.hover-theme:hover, [data-whatintent="mouse"] #search .clear-toggle:hover, [data-whatintent="mouse"] #root .l4ca.xpert section p.has-close a:hover { color: var(--secondary_bg); text-decoration: none; }

[data-whatintent="mouse"] .l4dr ul li a:hover i, [data-whatintent="mouse"] label a.show:hover, [data-whatintent="mouse"] .input-amount a[role="button"]:hover, [data-whatintent="mouse"] .l4dr li.has-social:hover > a i, [data-whatintent="mouse"] #root .popup-a .box-inset > .close:hover, /*[data-whatintent="mouse"] a:hover,*/ [data-whatintent="mouse"] .n6as a:hover, [data-whatintent="mouse"] .swiper-button-nav:hover, [data-whatintent="mouse"] #root .l4ca footer p a:hover i, [data-whatintent="mouse"] .l4ca footer p a:hover i, [data-whatintent="mouse"] .n6br a:hover, [data-whatintent="mouse"] .swiper-button-nav:hover, [data-whatintent="mouse"] #nav-top > ul > li > form ul li :hover, [data-whatintent="mouse"] .l4sc a:hover, [data-whatintent="mouse"] .l4dr ul li a:hover, [data-whatintent="mouse"] #root .l4dr ul a:hover, [data-whatintent="mouse"] figure a .icon-play:hover, [data-whatintent="mouse"] #root .l4dr a:hover, [data-whatintent="mouse"] .recommendation-modal__close-button-container button:hover [data-whatintent="mouse"] #root .l4us a.next-item:hover, [data-whatintent="mouse"] .m6pn > .m6pn-close:hover, [data-whatintent="mouse"] .s1tt a:hover ~ .icon-info, [data-whatintent="mouse"] a.s1tt:hover .icon-info, [data-whatintent="mouse"] a:hover i.overlay-gray { color: var(--secondary_bg); }
[data-whatintent="mouse"] .m6pn > .m6pn-close:hover { opacity: 1; }
[data-whatintent="mouse"] #root .l4cl h1 a:hover, [data-whatintent="mouse"] #root .l4cl h2 a:hover, [data-whatintent="mouse"] #root .l4cl h3 a:hover, [data-whatintent="mouse"] #root .l4cl h4 a:hover, [data-whatintent="mouse"] #root .l4cl h5 a:hover, [data-whatintent="mouse"] #root .l4cl h6 a:hover { color: inherit; text-decoration: underline; }
[data-whatintent="mouse"] #root .l4cl.upsell p:not(.link-btn) a:hover { text-decoration: underline; }

[data-whatintent="mouse"] #nav > ul > li > a:hover, [data-whatintent="mouse"] #nav-bar > ul > li > a:hover { color: var(--custom_top_nav_fg_hover); }
[data-whatintent="mouse"] #nav > ul > li.empty-url > a:hover, [data-whatintent="mouse"] #nav-bar > ul > li.empty-url > a:hover { color: var(--custom_top_nav_fg); }
[data-whatintent="mouse"].m2a .shopify-section-header #nav > ul > li > a:hover ~ a.toggle, [data-whatintent="mouse"].m2a .shopify-section-header #nav > ul > li:hover > a, [data-whatintent="mouse"].m2a .shopify-section-header #nav-bar > ul > li > a:hover ~ a.toggle, [data-whatintent="mouse"].m2a .shopify-section-header #nav-bar > ul > li:hover > a, [data-whatintent="mouse"] #nav-user > ul > li > ul a:hover { color: var(--custom_drop_nav_fg_hover); }
[data-whatintent="mouse"] #nav-top > ul > li > ul li > a:hover, [data-whatintent="mouse"] #nav > ul > li > ul ul li > a:hover, [data-whatintent="mouse"] #nav-bar > ul > li > ul ul li > a:hover, [data-whatintent="mouse"] #nav-top > ul > li > ul li > a:hover, [data-whatintent="mouse"] #nav-top > ul > li > ul li> a:hover i, [data-whatintent="mouse"] #nav-user > ul > li > ul > a:hover i { color: var(--custom_drop_nav_fg_hover); }

[data-whatintent="mouse"] a.overlay-gray:hover, [data-whatintent="mouse"] a.overlay-c:hover, .l4al .overlay-gray > i { color: var(--gray_text); }

[data-whatintent="mouse"] .link-btn a.inv:hover, [data-whatintent="mouse"] button.inv:hover, [data-whatintent="mouse"] .inv-btn button:hover { color: var(--secondary_bg_btn); }
[data-whatintent="mouse"] .link-btn a.inline:hover { background: none; /*color: var(--secondary_bg);*/ }
[data-whatintent="mouse"] .link-btn a.overlay-content:hover, [data-whatintent="mouse"] button.overlay-content:hover, [data-whatintent="mouse"] .overlay-content button:hover, [data-whatintent="mouse"] .link-btn.tags a:hover, [data-whatintent="mouse"] #root .l4cn.box li a:hover:before { animation-name: var(--pulse_dark); }
[data-whatintent="mouse"] .link-btn a.overlay-gradient:not(.inv, .inline):hover, [data-whatintent="mouse"] button.overlay-gradient:not(.inv, .inline):hover, [data-whatintent="mouse"] .overlay-gradient button:not(.inv, .inline):hover { animation-name: var(--pulse_custom); }

[data-whatintent="mouse"] a.strong:not(.link-more):has(i):hover { text-decoration: none; }
[data-whatintent="mouse"] a.strong:not(.link-more):has(i):hover span:not(.s1bx) { text-decoration: underline; }


/*! Async - Media --------- */
.video-clicked .icon-play, .video-clicked .img-overlay, .video-clicked ~ .icon-play, .video-clicked ~ .img-overlay, .video-clicked ~ picture .img-overlay { display: none; }


/*! Async - Links --------- */
#root .link-btn a.loading, #root .link-btn button.loading, #root form.loading .link-btn button[type="submit"] { color: rgba(0,0,0,0); pointer-events: none; }
#root .link-btn a.loading:after, #root .link-btn button.loading:after, #root form.loading .link-btn button[type="submit"]:after {
	content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 18px; height: 18px; min-width: 0; min-height: 0; margin: -9px 0 0 -9px; border-radius: 20px; border: 2px solid var(--secondary_btn_text); border-left-color: rgba(0,0,0,0) !important;
	animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear;
	transition: none;
}
#root .link-btn a.inv.loading:after, #root .link-btn button.inv.loading:after, #root form.loading .link-btn button.inv[type="submit"]:after { border-color: var(--secondary_bg_btn); }

.link-mute { overflow: hidden; position: relative; z-index: 99; width: 100%; height: 44px; margin-top: -44px; font-size: var(--size_20_f); text-indent: -3000em; text-align: left; direction: ltr;  pointer-events: none; }
#root .link-mute a { display: block; position: relative; z-index: 2; width: 44px; height: 100%; margin: 0; padding: 0; color: var(--primary_text); text-decoration: none; pointer-events: auto; }
.link-mute a:before { content: "\e99d"; }
.link-mute.muted a:before { content: "\e99c"; }
[data-theme="xclusive"] .link-mute a:before { content: "\e99b"; }
[data-theme="xclusive"] .link-mute.muted a:before { content: "\e99a"; }
figure:has(video[muted]) .link-mute { display: none !important; }
#root .l4cl .link-mute, #root .l4ft .link-mute, #root .m6fr article > figure .link-mute, #root .media-flexible .link-mute { position: absolute; left: var(--lar0); right: var(--l0ra); top: auto; bottom: 0; z-index: 9; margin: 0; padding: 0; }

[data-whatintent="mouse"] a:hover .icon-chevron-right, [data-whatintent="mouse"] .l4ne li:hover p:not(.link-btn) .icon-chevron-right { transform: translateX(3px); }
[data-whatintent="mouse"] a:hover .icon-chevron-left, [data-whatintent="mouse"] .l4ne li:hover p:not(.link-btn) .icon-chevron-left { transform: translateX(-3px); }

[data-whatintent="mouse"] .recommendation-modal__container .recommendation-modal__content > .recommendation-modal__button--minimal:last-child:hover { text-decoration: none; }

[data-whatintent="mouse"] input[type="submit"]:hover { box-shadow: 0 0 15px 5px rgba(0,0,0,0); color: var(--secondary_btn_text); }
[data-whatintent="mouse"] .l4sc.strong a:hover i { box-shadow: 0 0 15px 5px rgba(0,0,0,0); }
[data-whatintent="mouse"] .l4sc.strong.hover-move-up a:hover i { transform: translateY(-10%); }
[data-whatintent="mouse"] .l4sc.strong.hover-move-up.inv a:hover i { animation: none; box-shadow: none; }
[data-whatintent="mouse"] input[type="submit"]:hover, [data-whatintent="mouse"] .l4sc.strong a:hover i { animation: var(--pulse) .75s; }


/* hover-inv */
.hover-inv .link-btn a, .hover-inv button, .hover-inv .link-btn a:before, .hover-inv button:before, .hover-inv [type="button"], .hover-inv [type="reset"], .hover-inv [type="submit"] { transition: none; }

.hover-inv #root .n6pg.inv a, .hover-inv #root .l4sc.strong.inv i, .hover-inv #root .spr-pagination.inv [class*="spr-pagination"] a, .hover-inv #root .n6pg.inv a:after, .hover-inv #root .spr-pagination.inv [class*="spr-pagination"] a:after,
.hover-inv #root .link-btn a.inv, .hover-inv #root button.inv, .hover-inv #root .link-btn a.inv:before, .hover-inv #root button.inv:before,
.hover-inv #root .inv-btn .link-btn a, .hover-inv #root .inv-btn button, .hover-inv #root .inv-btn .link-btn a:before, .hover-inv #root .inv-btn button:before,
.hover-inv #root #totop.inv a, .hover-inv #root #totop.inv a:before, .hover-inv input.inv[type="button"], .hover-inv input.inv[type="reset"], .hover-inv input.inv[type="submit"], .hover-inv .btn-inv input[type="button"], .hover-inv .btn-inv input[type="reset"], .hover-inv .btn-inv input[type="submit"], #root .l4sc.hover-move-up i {
	transition-property: color, background-color, border-color, transform; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: -.1s;
}
.hover-inv[data-whatintent="mouse"] .link-btn a:not(.overlay-gradient):hover, .hover-inv[data-whatintent="mouse"] button:not(.overlay-gradient):hover { color: var(--secondary_bg_btn); --btn_bc_h: var(--secondary_bg_btn); --btn_bg_h: none; }
.hover-inv[data-whatintent="mouse"] .link-btn a:not(.overlay-gradient):hover:before, .hover-inv[data-whatintent="mouse"] button:not(.overlay-gradient):hover:before { border-color: var(--secondary_bg_btn); border-width: var(--btn_bd); background: none; }
.hover-inv[data-whatintent="mouse"] .link-btn a:not(.overlay-gradient, .shadow):hover:before, .hover-inv[data-whatintent="mouse"] button:not(.overlay-gradient, .shadow):hover:before { box-shadow: none; }

.hover-inv[data-whatintent="mouse"] .link-btn a.inv:hover, .hover-inv[data-whatintent="mouse"] button.inv:hover, .hover-inv[data-whatintent="mouse"] .inv-btn button:hover { color: var(--secondary_btn_text); --btn_bc_h: var(--secondary_bg_btn); --btn_bg_h: var(--secondary_bg_btn); }
.hover-inv[data-whatintent="mouse"] #root .link-btn a.inv.overlay-content:hover, .hover-inv[data-whatintent="mouse"] #root button.inv.overlay-content:hover { color: var(--secondary_btn_text); --btn_bc_h: var(--secondary_bg_btn); --btn_bg_h: var(--secondary_bg_btn); }

.hover-inv[data-whatintent="mouse"] #root .link-btn a.inv:hover, .hover-inv[data-whatintent="mouse"] #root button.inv:hover, .hover-inv[data-whatintent="mouse"] #root .inv-btn button:hover { color: var(--secondary_btn_text); }

.hover-inv[data-whatintent="mouse"] #root .n6pg.inv li.prev a:hover, .hover-inv[data-whatintent="mouse"] #root .n6pg.inv li.next a:hover, .hover-inv[data-whatintent="mouse"] #root .spr-pagination.inv .spr-pagination-prev a:hover, .hover-inv[data-whatintent="mouse"] #root .spr-pagination.inv .spr-pagination-next a:hover, .hover-inv[data-whatintent="mouse"] #root .l4sc.strong.inv a:hover i { box-shadow: none; border-width: var(--btn_bd); background: var(--secondary_bg); color: var(--secondary_text); }

[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover:before, [data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover:before { border-color: var(--custom_footer_link_bg); }
.hover-inv[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover, .hover-inv[data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover { color: var(--custom_footer_link_text); }
.hover-inv[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover:before, .hover-inv[data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover:before { border-color: var(--custom_footer_link_bg); border-width: var(--btn_bd); background-color: var(--custom_footer_link_bg); }
.hover-inv[data-whatintent="mouse"] #header > .link-btn a.inv:hover, .hover-inv[data-whatintent="mouse"] #header-inner > .link-btn a.inv:hover  { color: var(--custom_top_main_link_text); }
.hover-inv[data-whatintent="mouse"] #header > .link-btn a.inv:hover:before, .hover-inv[data-whatintent="mouse"] #header-inner > .link-btn a.inv:hover:before { border-color: var(--custom_top_main_link_bg); border-width: var(--btn_bd); background-color: var(--custom_top_main_link_bg); }

.hover-inv[data-whatintent="mouse"] #root .inv-btn input[type="button"]:hover, .hover-inv[data-whatintent="mouse"] #root .inv-btn input[type="reset"]:hover, .hover-inv[data-whatintent="mouse"] #root .inv-btn input[type="submit"]:hover { padding: calc(var(--btn_pv) - var(--btn_bd)) calc(var(--btn_ph) - var(--btn_bd)); box-shadow: none; border-color: var(--secondary_bg_btn); border-width: var(--btn_bd); background-color: var(--secondary_bg_btn); color: var(--white); }

/* no-hover-pulse */
.no-hover-pulse .link-btn a, .no-hover-pulse button, .no-hover-pulse .link-btn a:before, .no-hover-pulse button:before, .no-hover-pulse [type="button"], .no-hover-pulse [type="reset"], .no-hover-pulse [type="submit"] { transition: none; }

.no-hover-pulse #root .n6pg.inv a, .no-hover-pulse #root .l4sc.strong.inv i, .no-hover-pulse #root .spr-pagination.inv [class*="spr-pagination"] a, .no-hover-pulse #root .n6pg.inv a:after, .no-hover-pulse #root .spr-pagination.inv [class*="spr-pagination"] a:after,
.no-hover-pulse #root .link-btn a.inv, .no-hover-pulse #root button.inv, .no-hover-pulse #root .link-btn a.inv:before, .no-hover-pulse #root button.inv:before,
.no-hover-pulse #root #totop.inv a, .no-hover-pulse #root #totop.inv a:before, .no-hover-pulse input.inv[type="button"], .no-hover-pulse input.inv[type="reset"], .no-hover-pulse input.inv[type="submit"], .no-hover-pulse .btn-inv input[type="button"], .no-hover-pulse .btn-inv input[type="reset"], .no-hover-pulse .btn-inv input[type="submit"], #root .l4sc.hover-move-up i {
	transition-property: color, background-color, border-color, transform; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: -.1s;
}

.no-hover-pulse[data-whatintent="mouse"] .link-btn a.inv:hover, .no-hover-pulse[data-whatintent="mouse"] button.inv:hover, .no-hover-pulse[data-whatintent="mouse"] .inv-btn button:hover { color: var(--secondary_btn_text); --btn_bc_h: var(--secondary_bg_btn); --btn_bg_h: var(--secondary_bg_btn); }
.no-hover-pulse[data-whatintent="mouse"] #root .link-btn a.inv.overlay-content:hover, .no-hover-pulse[data-whatintent="mouse"] #root button.inv.overlay-content:hover { color: var(--secondary_btn_text); --btn_bc_h: var(--secondary_bg_btn); --btn_bg_h: var(--secondary_bg_btn); }

.no-hover-pulse[data-whatintent="mouse"] #root .link-btn a.inv:hover, .no-hover-pulse[data-whatintent="mouse"] #root button.inv:hover, .no-hover-pulse[data-whatintent="mouse"] #root .inv-btn button:hover { color: var(--secondary_btn_text); }

.no-hover-pulse[data-whatintent="mouse"] #root .n6pg.inv li.prev a:hover, .no-hover-pulse[data-whatintent="mouse"] #root .n6pg.inv li.next a:hover, .no-hover-pulse[data-whatintent="mouse"] #root .spr-pagination.inv .spr-pagination-prev a:hover, .no-hover-pulse[data-whatintent="mouse"] #root .spr-pagination.inv .spr-pagination-next a:hover, .no-hover-pulse[data-whatintent="mouse"] #root .l4sc.strong.inv a:hover i { box-shadow: none; border-width: var(--btn_bd); background: var(--secondary_bg); color: var(--secondary_text); }

[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover:before, [data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover:before { border-color: var(--custom_footer_link_bg); }
.no-hover-pulse[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover, .no-hover-pulse[data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover { color: var(--custom_footer_link_text); }
.no-hover-pulse[data-whatintent="mouse"] #root .shopify-section-footer .link-btn a.inv:hover:before, .no-hover-pulse[data-whatintent="mouse"] #root .shopify-section-footer button.inv:hover:before { border-color: var(--custom_footer_link_bg); border-width: var(--btn_bd); background-color: var(--custom_footer_link_bg); }
.no-hover-pulse[data-whatintent="mouse"] #header > .link-btn a.inv:hover, .no-hover-pulse[data-whatintent="mouse"] #header-inner > .link-btn a.inv:hover  { color: var(--custom_top_main_link_text); }
.no-hover-pulse[data-whatintent="mouse"] #header > .link-btn a.inv:hover:before, .no-hover-pulse[data-whatintent="mouse"] #header-inner > .link-btn a.inv:hover:before { border-color: var(--custom_top_main_link_bg); border-width: var(--btn_bd); background-color: var(--custom_top_main_link_bg); }

.no-hover-pulse[data-whatintent="mouse"] #root .inv-btn input[type="button"]:hover, .no-hover-pulse[data-whatintent="mouse"] #root .inv-btn input[type="reset"]:hover, .no-hover-pulse[data-whatintent="mouse"] #root .inv-btn input[type="submit"]:hover { padding: calc(var(--btn_pv) - var(--btn_bd)) calc(var(--btn_ph) - var(--btn_bd)); box-shadow: none; border-color: var(--secondary_bg_btn); border-width: var(--btn_bd); background-color: var(--secondary_bg_btn); color: var(--white); }


.link-toggle-clicked [class*="-down"], .link-toggle-clicked [class*="-up"] { transform: rotate(180deg); }


/*! Async - Lists --------- */
[data-whatintent="mouse"] .l4ca footer p a:hover i.icon-trash:before { content: "\e93a"; }
[data-whatintent="mouse"] #root .m6pn .l4ca.compact:not(.no-hover) > li:not(.has-l4ml):hover:after, [data-whatintent="mouse"] #search .l4ca.compact:not(.no-hover) > li:not(.has-l4ml):hover:after { border-width: 0; border-color: var(--custom_drop_nav_fg); background: var(--custom_drop_nav_fg); opacity: .04; }
/*[data-whatintent="mouse"] #root .m6pn .l4ca.compact:not(.no-hover) > li:not(.has-l4ml):hover + li:before, [data-whatintent="mouse"] #search .l4ca.compact:not(.no-hover) > li:not(.has-l4ml):hover + li:before { border-top-width: 0; }*/
[data-whatintent="mouse"] #root .m6pn .l4ca.compact:not(.no-hover) li:not(.has-l4ml):hover .r6rt .rating > * .fill, [data-whatintent="mouse"] #search .l4ca.compact:not(.no-hover) li:not(.has-l4ml):hover .r6rt .rating > * .fill { /*border-width: 0;*/ background: var(--sand); }

[data-whatintent="mouse"] .l4ft.zoom li:hover > figure img, [data-whatintent="mouse"] .l4ft.zoom li:hover > figure video, [data-whatintent="mouse"] .l4ft.zoom li:hover > figure iframe,
[data-whatintent="mouse"] .l4ft.zoom li:hover .main > figure img, [data-whatintent="mouse"] .l4ft.zoom li:hover .main > figure video, [data-whatintent="mouse"] .l4ft.zoom li:hover .main > figure iframe { transform: scale(1.1); }

/*.l4al {}*/
[data-whatintent="mouse"] #root .l4al .close:hover { color: inherit; }

/*.l4ft {}*/
.no-mobile[data-whatintent="mouse"] .l4ft.hover-out:hover > li:not(:hover):after { opacity: 0.7; }
.no-mobile[data-whatintent="mouse"] .l4ft.hover-out:hover > li:hover:after { visibility: hidden; opacity: 0; }
.no-mobile[data-whatintent="mouse"] .l4ft.hover-out:hover > li:hover figure ~ div { opacity: 1; }

.l4ca { --processing_color: var(--primary_text); }
.l4ca li.removing > *:not(.removed), .l4ml li.removing > *:not(.removed) { visibility: hidden; opacity: 0; }
.l4ca li.removing .removed, .l4ml li.removing .removed { visibility: visible; opacity: 1; }
#root .l4ca li.removing + .has-l4ca, #root .l4ca li.removing + .has-l4ml { display: none; }
.l4ca li.removing2, .l4ml li.removing2, #root .l4ca.compact .l4ml li.removing2 { visibility: hidden; opacity: 0; transform: translateY(-10px); }
.l4ca li.processing.removing .removed, .l4ca li.processing.removing .removed a, .l4ml li.processing.removing .removed, .l4ml li.processing.removing .removed a { color: rgba(0,0,0,0); }
.l4ca .removed, .l4ml .removed {
	display: block; visibility: hidden; position: absolute; left: 0; right: 0; top: 50%; z-index: 99; width: auto; max-width: none; margin-left: 0; margin-right: 0; text-align: center; opacity: 0;
	transform: translateY(-50%);
}
#root .l4ml li.removing .removed { display: block; position: absolute; right: 16px; margin-left: 0; margin-right: 0; }
#root .l4ml li.removing.processing .removed > *, #root .l4ca li.removing.processing .removed > * { visibility: hidden; opacity: 0; pointer-events: none; }
#root .l4ca li > section:first-child ~ p.removed { margin-left: 0; margin-right: 0; }
.l4ca .removed a, #root .l4ml .removed a { color: var(--secondary_bg); font-weight: var(--main_fw_strong); text-decoration: underline; }
[data-whatintent="mouse"] .l4ca .removed a:hover, [data-whatintent="mouse"] #root .l4ml .removed a:hover { text-decoration: none; }
.l4ca > li.processing a.remove { width: 20px; color: inherit; text-indent: -3000em; }
.l4ca > li.processing a:has(.icon-trash) { width: 20px; color: inherit; text-indent: -3000em; }
.l4ca > li.processing a.remove i:before { opacity: 0; }
.l4ca > li.processing a:has(.icon-trash) i:before { opacity: 0; }
.l4ca li.processing a.remove:before, .l4ca li.processing.removing .removed:before { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 20px; height: 20px; border-radius: 100%; border: 2px solid var(--processing_color); margin: -10px 0 0 -10px; border-left-color: rgba(0,0,0,0) !important; opacity: .42; animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear; transition: none; }
.l4ca > li.processing a:has(.icon-trash):before { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 20px; height: 20px; border-radius: 100%; border: 2px solid var(--processing_color); margin: -10px 0 0 -10px; border-left-color: rgba(0,0,0,0) !important; opacity: .42; animation-name: spin; animation-duration: .75s; animation-fill-mode: forwards; animation-iteration-count: infinite; animation-timing-function: linear; transition: none; }
.l4ca > li.has-l4ca { display: none; padding-left: calc(var(--img_d) + var(--img_w)); }
/*.l4ca > li:has(.l4ca) { display: none; padding-left: calc(var(--img_d) + var(--img_w)); }*/
[dir="rtl"] .l4ca > li.has-l4ca { padding-left: 0; padding-right: calc(var(--img_d) + var(--img_w)); }
/*[dir="rtl"] .l4ca > li:has(.l4ca) { padding-left: 0; padding-right: calc(var(--img_d) + var(--img_w)); }*/
.l4ca > li.has-l4ca { padding-top: 0; padding-bottom: 0; }
/*.l4ca > li:has(.l4ca) { padding-top: 0; padding-bottom: 0; }*/
#root .l4ml > li.has-l4ca:before { display: none; }
/*#root .l4ml > li:has(.l4ca):before { display: none; }*/
.l4ca .show-l4ca { position: relative; z-index: 9; }
.l4ca.compact > li.has-l4ca { padding-left: calc(var(--img_d) + var(--offset) - var(--img_d)); }
/*.l4ca.compact > li:has(.l4ca) { padding-left: calc(var(--img_d) + var(--offset) - var(--img_d)); }*/
[dir="rtl"] .l4ca.compact > li.has-l4ca { padding-right: calc(var(--img_d) + var(--offset) - var(--img_d)); padding-left: 0; }
/*[dir="rtl"] .l4ca.compact > li:has(.l4ca) { padding-right: calc(var(--img_d) + var(--offset) - var(--img_d)); padding-left: 0; }*/
#root .l4ml li.has-l4ca { padding-bottom: 0; }
/*#root .l4ml li:has(.l4ca) { padding-bottom: 0; }*/
.l4ml li.has-l4ca:before { border-top-width: 0; }
./*l4ml li:has(.l4ca):before { border-top-width: 0; }*/
.l4ca .l4ca, .l4ml .l4ca { width: 100%; margin-bottom: 0; border-top-width: 0; color: inherit; --mr_i: 2px; }
#root .l4ml .l4ca { margin-top: calc(0px - var(--pt) * 0.5); }
#root .l4ca .l4ca li, #root .l4ml .l4ca li { padding-top: 0; opacity: 1; --img_w: 50px; --img_d: 16px; }
#root .l4ml .l4ca li { justify-content: flex-start; }
.l4ca .l4ca h1, .l4ca .l4ca h2, .l4ca .l4ca h3, .l4ca .l4ca h4, .l4ca .l4ca h5, .l4ca .l4ca h6, .l4ml .l4ca h1, .l4ml .l4ca h2, .l4ml .l4ca h3, .l4ml .l4ca h4, .l4ml .l4ca h5, .l4ml .l4ca h6 { font-size: var(--size_12_f); }
#root .l4ca li.toggle-l4ca + li.has-l4ca { display: block; }
/*.l4ca li.toggle-l4ca + li:has(.l4ca) { display: block; }*/
.l4ca li.toggle-l4ca + li.has-l4ca:before { border-top-width: 0; }
/*.l4ca li.toggle-l4ca + li:has(.l4ca):before { border-top-width: 0; }*/
/*.toggle-l4ca .show-l4ca {}*/
.toggle-l4ca .show-l4ca span:not(.hidden), #root .l4ml .l4ca li:before { display: none; }
#root .toggle-l4ca .show-l4ca span.hidden { display: inline; position: relative; left: 0; top: 0; }
.toggle-l4ca .show-l4ca .icon-chevron-down { transform: rotate(180deg); }
.l4ca.compact .l4ca { margin-top: 0; margin-bottom: 0; }
.l4ca.compact .l4ca li { --offset: 60px; }
#root .l4ml li.toggle-l4ca:before { border-bottom-width: 0; }
.m6pn .l4ca { --processing_color: var(--custom_drop_nav_fg); }


/*.l4cl {}*/
[data-whatintent="mouse"] #root .l4cl p:not(.link-btn) a.overlay-content:not(:has(.link-underline)):hover { color: var(--secondary_bg); text-decoration: none; }
[data-whatintent="mouse"] .l4cl li:hover .link-btn a { position: relative; left: 0; }
[data-whatintent="mouse"] .l4cl li:hover .link-btn, [data-whatintent="mouse"] .l4cl li:hover form.sticky, [data-whatintent="mouse"] #root .l4pr:hover .swiper-outer ~ .swiper-custom-pagination .swiper-button-nav:not(.swiper-button-disabled), [data-whatintent="mouse"] #root .l4pr .swiper-button-nav:not(.swiper-button-disabled):hover {
	transform: none;
	visibility: visible; opacity: 1;
}
[data-whatintent="mouse"] #root .l4cl li:hover figure .link-btn, [data-whatintent="mouse"] #root .l4cl li:hover figure form { visibility: visible; opacity: 1; }
.l4cl picture { transition-duration: .2s; }
[data-whatintent="mouse"] .l4cl figure > a.remove:hover i { background: var(--secondary_bg); }
/*[data-whatintent="mouse"] .l4cl.align-stretch li:has(a):hover:before, [data-whatintent="mouse"] #root .l4cl.align-stretch li:has(a):hover figure { border-color: var(--secondary_bg); }*/
[data-whatintent="mouse"] .l4cl.align-stretch li:has(a):hover:before,
[data-whatintent="mouse"] #root .l4cl.align-stretch li:has(a):hover figure,
[data-whatintent="mouse"] .l4cl.align-center li:has(a):hover:before,
[data-whatintent="mouse"] #root .l4cl.align-center li:has(a):hover figure { border-color: var(--secondary_bg); }
/*.l4cl.category {}*/
/*.l4cl.category a span, .l4ft p a.strong span { position: relative; z-index: 9; }*/
[data-whatintent="mouse"] .l4cl.category:not(.s4wi, .dont-move) li:not(.dont-move):hover, [data-whatintent="mouse"] .l4cl.static:not(.s4wi, .dont-move) li:not(.dont-move):hover, [data-whatintent="mouse"] .l4ne:not(.s4wi, .dont-move) li:not(.dont-move):hover, [data-whatintent="mouse"] .l4ft:not(.s4wi, .dont-move) li:not(.dont-move):hover { transform: translateY(-10px); }
[data-whatintent="mouse"] .m6ac .l4ft li:hover { transform: none; }
[data-whatintent="mouse"] .l4cl.category li:hover a:after, [data-whatintent="mouse"] .l4cl.static li:hover a:after, [data-whatintent="mouse"] .l4ne li:hover a:before { bottom: -10px; }
[data-whatintent="mouse"] .l4ne li:hover .link-btn a:before { bottom: 0; }
/*.l4cl.inline {}*/
[data-whatintent="mouse"] .l4cl.inline li:hover { transform: scale(1.1); }
[data-whatintent="mouse"] .l4cl.inline.s4wi li:hover { transform: none; }
.l4hs { pointer-events: none; }
.l4hs > li { pointer-events: auto; }
.l4hs > li > a:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; right: auto; bottom: auto; z-index: 1; width: var(--hs_size); height: var(--hs_size); margin: calc(0px - var(--hs_size) * 0.5) 0 0 calc(0px - var(--hs_size) * 0.5); border-radius: 99px; }
.l4hs.dots > li:not(.toggle) > a:after { animation: 1.4s hotspots cubic-bezier(.35,.22,.46,1) infinite; }
/*.l4hs.ol {}*/
.l4hs.ol > li.hover > a:before, .l4hs-l li.hover:before, .l4hs-l li.toggle:before { border-color: var(--bg_active); background: var(--bg_active); color: var(--fg_active); }
/*.l4hs-l {}*/
.l4hs-l li > a.toggle, .l4hs-l li > a.toggle-mobile { display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: calc(var(--main_fz) * var(--main_lh) - var(--w)); z-index: 9; width: var(--w); height: var(--w); text-align: left; text-indent: -3000em; direction: ltr; }
.l4hs-l li > a.toggle-mobile { display: none; }



/*.l4pr {}*/
@media only screen and (min-width: 1101px), only screen and (min-width: 761px) and (max-width: 1000px) { /* 1100-, 760-1000 */
	[data-whatintent="mouse"] .l4pr.aside-pager.s4wi .swiper-button-nav:hover:before { color: var(--secondary_bg); }
}

/*! Async - Forms --------- */
[data-whatintent="mouse"] input[type="button"]:not(.inv):hover, [data-whatintent="mouse"] input[type="reset"]:not(.inv):hover, [data-whatintent="mouse"] input[type="submit"]:not(.inv):hover { border-color: var(--secondary_bg_btn_dark); background: var(--secondary_bg_btn_dark); color: var(--secondary_btn_text); }
[data-whatintent="mouse"] button:hover, [data-whatintent="mouse"] .shopify-section-header .link-btn a:hover, [data-whatintent="mouse"] .link-btn a:hover, [data-whatintent="mouse"] #nav-user > ul > li > a:hover i span, [data-whatintent="mouse"] .n6pg li.prev a:hover, [data-whatintent="mouse"] .n6pg li.next a:hover, [data-whatintent="mouse"] .l4cn.box li a:hover:before, [data-whatintent="mouse"] #totop a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-prev a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-next a:hover, [data-whatintent="mouse"] input[type="button"]:hover, [data-whatintent="mouse"] input[type="reset"]:hover, [data-whatintent="mouse"] input[type="submit"]:hover { box-shadow: 0 0 15px 5px rgba(0,0,0,0); color: var(--secondary_btn_text); }
[data-whatintent="mouse"] input[type="button"]:hover, [data-whatintent="mouse"] input[type="reset"]:hover, [data-whatintent="mouse"] input[type="submit"]:hover, [data-whatintent="mouse"] button:hover, [data-whatintent="mouse"] .link-btn a:hover, [data-whatintent="mouse"] #nav-user > ul > li > a:hover i span, [data-whatintent="mouse"] .n6pg li.prev a:hover, [data-whatintent="mouse"] .n6pg li.next a:hover, [data-whatintent="mouse"] .l4cn.box li a:hover:before, [data-whatintent="mouse"] #totop a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-prev a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-next a:hover { animation: var(--pulse) .75s; }
[data-whatintent="mouse"] button:hover:before, [data-whatintent="mouse"] .link-btn a:hover:before, [data-whatintent="mouse"] .n6pg li.prev a:hover:after, [data-whatintent="mouse"] .n6pg li.next a:hover:after, [data-whatintent="mouse"] #totop a:hover:before { border: 0 solid var(--secondary_bg_btn_dark); background: var(--secondary_bg_btn_dark); }
[data-whatintent="mouse"] .n6pg li.prev a:hover, [data-whatintent="mouse"] .n6pg li.next a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-prev a:hover, [data-whatintent="mouse"] .spr-pagination > div > .spr-pagination-next a:hover, [data-whatintent="mouse"] .l4sc.strong a:hover i {
	border-color: var(--secondary_bg_btn_dark); background: var(--secondary_bg_btn_dark);
	animation-name: var(--pulse);
}

[data-whatintent="mouse"] .l4cl figure .check.plain a:hover { color: inherit; text-decoration: underline; }

/*[data-whatintent="mouse"] .f8pr .submit .input-amount input:hover, [data-whatintent="mouse"] .f8ps .submit .input-amount input:hover, [data-whatintent="mouse"] .m6pr-compact .submit .input-amount input:hover { border-color: var(--alto); }*/

#root .check.box input[disabled] ~ label, #root .check.box input[disabled] ~ label *, #root .check input[disabled] ~ label:before, #root .check input[disabled] ~ label:after { cursor: default; }
[data-whatintent="mouse"] .check input:not([disabled]) ~ label:hover:before { border-color: var(--secondary_bg); }
[data-whatintent="mouse"] .check input.disabled ~ label:hover:before { border-color: var(--alto); }
[data-whatintent="mouse"] .check.wide > *:hover { background: var(--sand); }
[data-whatintent="mouse"] .check.wide > *:hover label:before { border-color: var(--secondary_bg); }

.check.box label, .check.box label *, #nav-user > ul > li > label, .check.inside label > span { cursor: pointer; }
.check.box input:checked ~ label, .check.box input:checked ~ label *, #root .check.color input[type="radio"]:checked ~ label, #root .check.color input[disabled] ~ label, #root .check.color input[type="radio"]:checked ~ label *, #root .check.color input[disabled] ~ label * { cursor: default; }
[data-whatintent="mouse"] #root .check.color label:hover { border-color: var(--secondary_bg); }

.no-click, .shopify-section-header li.disabled { cursor: default; pointer-events: none; }

[data-whatintent="mouse"] .f8fl header label:hover, [data-whatintent="mouse"] .f8fl header li:not(.strong) a:hover { color: inherit; text-decoration: underline; }

/*! Async - Helpers --------- */
#root > .overlay-close-clipping { z-index: 159; }
#root > .overlay-close ~ .overlay-close-clipping { display: block; }
.m6pn-open #root > .overlay-close-clipping { visibility: visible; opacity: 1; }

.m6pn-open:has(.m6pn.no-overlay.toggle) #root > .overlay-close-clipping { visibility: hidden; opacity: 0; }

.l4ca.is-empty { display: none; }

[data-whatintent="mouse"] #root .m6fr:not(.slider-fraction):hover .swiper-button-nav { visibility: visible; opacity: 1; transform: none; }

.recommendation-modal__container, .recommendation-modal__backdrop, .l4ft li, #preview-bar-iframe, .l4ft figure, .l4ft .main > div, .l4ft.hover-out > li:after, .l4cl figure .check, #cookie-bar:before, .f8ps, #header .l4al, .m6cp, .m6cp [class*="icon-chevron"], .has-first-m6fr-wide #root #nav-bar > ul > li > a:before, .link-underline:after, .link-underline:before, .l4pr .swiper-button-nav { transition-property: visibility, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
.l4ft:not(.masonry) li, .l4ft figure, .l4ft .main > div, #cookie-bar:before, .f8ps, #preview-bar-iframe, .l4cl figure .check, .m6cp, .m6cp [class*="icon-chevron"], /*.has-first-m6fr-wide #root #nav > ul > li > a:before,*/ .has-first-m6fr-wide #root #nav-bar > ul > li > a:before, .link-underline:after, .link-underline:before, .m6fr .swiper-button-nav, .l4pr .swiper-button-nav, #nav-bar > ul > li > a:after, .check.inside label > span, .check.inside label > span:before, .check.inside label > span:after { transition-property: all; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
.l4ca li[class*="removing"], #root .l4ml li[class*="removing"] { transition-property: visibility, opacity, transform; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
#nav > ul > li > a:after{ transition-property: width; transition-duration: 0.3s; transition-timing-function: cubic-bezier(.4,0,.2,1); transition-delay: 0s; }
[data-whatintent="mouse"] #nav > ul > li:hover:after, [data-whatintent="mouse"] #nav-bar > ul > li:hover:after, [data-whatintent="mouse"] .l4ft li:hover > .main picture ~ picture, [data-whatintent="mouse"] .l4ft li:hover > .main picture ~ video, [data-whatintent="mouse"] .l4ft li:hover > .main video ~ picture, [data-whatintent="mouse"] .l4ft li:hover > .main video ~ video { display: block; }
.limit-clicked .limit { display: none !important; }

.l4ca .l4ml li { transition: none; }


/*! Flexbox --------- */
#root .video-clicked .link-mute, #root .video-clicked ~ .link-mute, #root picture:has(.video-clicked) ~ .link-mute { display: flex; flex-wrap: wrap; }
#root .link-mute { flex-direction: row; }
#root .link-mute { justify-content: flex-end; }


/*! Animations --------- */
.fade-me-out { animation-duration: .4s; animation-fill-mode: forwards; animation-name: fade-up; }
#root > .l4al li.fade-me-out { animation-name: fade-right; }

@keyframes fade-up { 0% { visibility: visible; opacity: 1; transform: none; } 100% { visibility: hidden; opacity: 0; transform: translateY(-10px); } }
@keyframes fade-down { 0% { visibility: visible; opacity: 1; transform: none; } 100% { visibility: hidden; opacity: 0; transform: translateY(10px); } }
@keyframes fade-left { 0% { visibility: visible; opacity: 1; transform: none; } 100% { visibility: hidden; opacity: 0; transform: translateX(-10px); } }
@keyframes fade-right { 0% { visibility: visible; opacity: 1; transform: none; } 100% { visibility: hidden; opacity: 0; transform: translateX(10px); } }

@keyframes pulse { 0% { box-shadow: 0 0 0 0 var(--secondary_bg_btn); } }
@keyframes pulse_classic { 0% { box-shadow: 0 0 0 0 var(--secondary_bg); } }
@keyframes pulse_custom { 0% { box-shadow: 0 0 0 0 var(--pulse); } }
@keyframes pulse_dark { 0% { box-shadow: 0 0 0 0 var(--alto); } }
@keyframes pulse_header { 0% { box-shadow: 0 0 0 0 var(--custom_top_main_link_bg); } }
@keyframes pulse_footer { 0% { box-shadow: 0 0 0 0 var(--custom_footer_link_bg); } }

@keyframes hotspots {
	0%, 100% { transform: none; }
	50% { transform: scale(1.05); }
}

[data-whatintent="mouse"] #nav-user > ul > li > a:hover [class*="icon-heart"] { animation: heart 1s infinite; opacity: 1 !important; }

@keyframes heart {
	0%, 50%, 100% { transform: scale(1); }
	25%, 75% { transform: scale(.9); }
}

@media only screen and (min-width: 1001px) {
	.nav-hover #root > .overlay-close { visibility: visible; opacity: 1; }

	.shopify-section-header { --bd_w: 100%; }
	[data-theme="xclusive"] .shopify-section-header li.sub:not(.show-all, .no-arrow), [data-theme="xclusive"] .shopify-section-header li.sub:not(.show-all, .no-arrow) { --bd_w: calc(100% - 16px); }
	.has-first-m6fr-wide #root #nav > ul > li > a, .has-first-m6fr-wide #root #nav-bar > ul > li > a, #root #nav.hr > ul > li > a, #root #nav-bar.hr > ul > li > a { color: var(--custom_top_nav_fg); }
	.has-first-m6fr-wide #root #nav > ul > li > a:after, .has-first-m6fr-wide #root #nav-bar > ul > li > a:after, #root #nav > ul > li > a:after, #root #nav-bar > ul > li > a:after {
		content: ""; display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; bottom: calc(50% - var(--btn_lh) * var(--custom_top_nav_fz) * 0.5 - 4px); width: 0%; border-bottom: 1px solid var(--custom_top_nav_fg);
		transform: none;
	}
	[data-whatintent="mouse"].has-first-m6fr-wide #root #nav > ul > li > a:hover:after, [data-whatintent="mouse"].has-first-m6fr-wide #root #nav-bar > ul > li > a:hover:after, [data-whatintent="mouse"] #root #nav.hr > ul > li > a:hover:after, [data-whatintent="mouse"] #root #nav-bar.hr > ul > li > a:hover:after { width: var(--bd_w); }

	[data-whatintent="mouse"] #nav > ul > li > a:after, [data-whatintent="mouse"] #nav-bar > ul > li > a:after { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; text-align: left; text-indent: -3000em; direction: ltr; }
	[data-whatintent="mouse"] #nav > ul > li:hover > a { z-index: 9; }
	[data-whatintent="mouse"] #nav > ul > li.sub:hover > a:after, [data-whatintent="mouse"] #nav-bar > ul > li.sub:hover > a:after { left: -24px; right: -24px; top: -10px; bottom: -8px; transform: perspective(200px) rotateX(45deg); }
}
@media only screen and (max-width: 1000px) {
	#nav.fixed + #distance-spacer { display: none; }
}
@media only screen and (min-width: 761px) and (max-width: 1200px) {
	[data-whatintent="mouse"] #nav > ul > li.sub:hover > a:after, [data-whatintent="mouse"] #nav-bar > ul > li.sub:hover > a:after { left: -16px; right: -16px; }
}
@media only screen and (min-width: 761px) and (max-width: 1100px) {
	[data-whatintent="mouse"] #nav > ul > li.sub:hover > a:after, [data-whatintent="mouse"] #nav-bar > ul > li.sub:hover > a:after { left: -12px; right: -12px; }
}
@media only screen and (min-width: 761px) {

	[data-whatintent="mouse"] #root .l4cl .second-img-hover:has(picture ~ picture):hover picture { display: none; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover:has(picture ~ picture):hover picture ~ picture { display: block; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover.second-img-first:has(picture ~ picture):hover picture, [data-whatintent="mouse"] #root .l4cl.second-img-first .second-img-hover:has(picture ~ picture):hover picture { display: block; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover.second-img-first:has(picture ~ picture):hover picture ~ picture, [data-whatintent="mouse"] #root .l4cl.second-img-first .second-img-hover:has(picture ~ picture):hover picture ~ picture { display: none; }

	[data-whatintent="mouse"] #root .l4cl .second-img-hover.has-picture-picture:hover picture { display: none; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover.has-picture-picture:hover picture ~ picture { display: block; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover.second-img-first.has-picture-picture:hover picture { display: block; }
	[data-whatintent="mouse"] #root .l4cl .second-img-hover.second-img-first.has-picture-picture:hover picture ~ picture { display: none; }
}
@media only screen and (max-width: 760px) { /* 760 */
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile:hover { box-shadow: none; color: var(--secondary_bg); text-decoration: underline; }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile.overlay-content:hover { color: var(--primary_text); }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile.overlay-tertiary:hover { color: var(--tertiary_bg); }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile.overlay-quaternary:hover { color: var(--quaternary_bg); }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile.overlay-coal:hover { color: var(--primary_text); }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile.overlay-white:hover { color: var(--white); }


	.shopify-section-header:has(#header-inner.mobile-visible-search) + #distance-spacer { margin-bottom: calc(var(--custom_top_search_h) + var(--mob_cl)); --mob_cl: calc(var(--search_mob_pd) * 2); }
	.shopify-section-header.has-mobile-visible-search + #distance-spacer { margin-bottom: calc(var(--custom_top_search_h) + var(--mob_cl)); --mob_cl: calc(var(--search_mob_pd) * 2); }

	.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)) + #distance-spacer { --mob_cl: var(--search_mob_pd); }
	.shopify-section-header.has-mobile-visible-search.no-bd-m + #distance-spacer { --mob_cl: var(--search_mob_pd); }

	.shopify-section-header.no-bd-m:has(#header-inner.mobile-visible-search):has(#search.no-pd-t) + #distance-spacer { --mob_cl: var(--search_mob_pd); }
	.shopify-section-header.has-mobile-visible-search.no-pd-t + #distance-spacer { --mob_cl: var(--search_mob_pd); }

	.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)):has(#search.no-pd-t) + #distance-spacer { --mob_cl: var(--search_mob_pd); }
	.shopify-section-header.has-mobile-visible-search.no-bd-m.no-pd-t + #distance-spacer { --mob_cl: var(--search_mob_pd); }


	/*.l4hs.ol {}*/
	.l4hs.ol > li > a.m6pn-clicked:before, .l4hs-l > li:has(>a.m6pn-clicked):before { border-color: var(--bg_active); background: var(--bg_active); color: var(--fg_active); }

	/*.f8sr-fixed #search { display: none; }*/
	.l4hs-l li > a.toggle { display: none; }
	.l4hs-l li > a.toggle-mobile, #root .l4ca li.toggle-l4ca .has-l4ca.hidden { display: block; }
	#root .l4ca li.toggle-l4ca section ~ div.has-l4ca .l4ca { margin-top: 0; }
}

.l4vw a:hover .icon-view-grid:before { content: "\e91b"; }
.l4vw a:hover .icon-view-square:before { content: "\e98e"; }



/*! Outlines --------- */
[data-whatinput="keyboard"] a:focus, [data-whatinput="keyboard"] button:focus, [data-whatinput="keyboard"] .check input:focus ~ label:before, [data-whatinput="keyboard"] [role="button"]:focus { outline-width: 2px; outline-style: solid; outline-color: var(--secondary_bg); outline-offset: -2px; }
[data-whatintent="mouse"] input:focus, [data-whatintent="mouse"] select:focus, [data-whatintent="mouse"] textarea:focus { outline: none; }


@media only screen and (max-width: 400px) {
	.recommendation-modal__container { width: auto !important; }
}