#nav-top { font-size: calc(0.9285714286 * var(--main_fz)); }

input.size-s, select.size-s, .bv_atual.size-s, .size-s input, .size-s select, .size-s .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 0.65 * 2) }
input.size-m, select.size-m, .bv_atual.size-m, .size-m input, .size-m select, .size-m .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 1.2 * 2) }
input.size-l, select.size-l, .bv_atual.size-l, .size-l input, .size-l select, .size-l .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 1.35 * 2) }

/*.f8sr .l4vw + p { margin-left: 22px; }
	.f8sr .l4vw + p:before { display: none; }
	[dir="rtl"] .f8sr .l4vw + p { margin-left: 0; margin-right: 22px; }*/
		
/*@media only screen and (max-width: 1356px) {
.l4ft li { --mih: 22vw; }
} */
.l4al img, .l4al svg, .l4al picture { max-height: 25px !important; }

.l4cl.small .swiper-button-next:before { content: "\e96b"; }
.l4cl.small .swiper-button-prev:before { content: "\e96a"; }
[dir="rtl"] .l4cl.small .swiper-button-next:before { content: "\e96a"; }
[dir="rtl"] .l4cl.small .swiper-button-prev:before { content: "\e96b"; }


/*! Responsive --------- */
@media only screen and (max-width: 1356px) {
  .l4ft.static li.w33, .l4ft.static.w33 li { --mih: 22vw; }
  .l4ft.static li.w12,
  .l4ft.static li.w15,
  .l4ft.static li.w16,
  .l4ft.static li.w20,
  .l4ft.static li.w25,
  .l4ft.static li.w33,
  .l4ft.static.w12 li,
  .l4ft.static.w14 li,
  .l4ft.static.w16 li,
  .l4ft.static.w20 li,
  .l4ft.static.w25 li,
  .l4ft.static.w33 li { --mih: 22vw; }
}
@media only screen and (max-width: 1000px) { /* 1000 */
#root .l4ft.mobile-compact li { --mih: 145px; }
}
@media only screen and (max-width: 760px) { /* 760 */
h1, .h1, .l4cl.category h1, #root .l4cl.category .size-50 { font-size: var(--mob_h1); }
	h1, .h1, .l4cl.category h1, #root .l4cl.category .size-50 { font-size: var(--mob_h1); }
    h2, .h2, .l4cl.category h2 { font-size: var(--mob_h2); }
    h3, .h3, .l4cl.category h3 { font-size: var(--mob_h3); }
    h4, .h4, .l4cl.category h4 { font-size: var(--mob_h4); }
    h5, .h5, .l4cl.category h5 { font-size: var(--mob_h5); }
    h6, .h6, .l4cl.category h6 { font-size: var(--mob_h6); }
    .lead { font-size: var(--mob_lead); }

.f8sr { --f8sr_dist: 20px; }
	.f8sr fieldset > *:not(h1, h2, h3, h4, h5, h6, :first-child:not(hr)):before { display: none; }
  .l4ft.static li.w12,
  .l4ft.static li.w15,
  .l4ft.static li.w16,
  .l4ft.static li.w20,
  .l4ft.static li.w25,
  .l4ft.static li.w33,
  .l4ft.static.w12 li,
  .l4ft.static.w14 li,
  .l4ft.static.w16 li,
  .l4ft.static.w20 li,
  .l4ft.static.w25 li,
  .l4ft.static.w33 li { --mih: 300px; }
  
.l4vw li.active:not(:first-child:last-child) { display: none; } 
}
