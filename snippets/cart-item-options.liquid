{%- capture line_item_options -%}
    {%- liquid
        assign show_deliverytime = false
        if origin == 'cartpage'
            assign show_deliverytime = true
        elsif orign == 'cartpage' and bundle
            assign show_deliverytime = true
        endif
        if line_item.item_components.size > 0
            assign show_deliverytime = false
        endif

        assign slash = false
        assign show_line_item_options = false
        if line_item.unit_price_measurement != blank or line_item.selling_plan_allocation != blank or line_item.properties.size > 0 or line_item.line_level_discount_allocations != blank
            assign show_line_item_options = true
        elsif line_item.options_with_values and line_item.product.has_only_default_variant == false
            assign show_line_item_options = true
        elsif show_deliverytime
            assign show_line_item_options = true
        endif
        assign shown_options = false
    -%}
    {%- if origin == 'cartpage' or bundle -%}<span>{%- endif -%}
    {%- if show_line_item_options -%}
        {%- if origin == 'cartpage' or bundle -%}<span class="{% if origin == 'cartpanel' and bundle %}size-12{% endif %}">{%- else -%}<p class="{% if list == false %}size-12{% endif %}">{%- endif -%}
        {% if line_item.unit_price_measurement %}
            {%- assign slash = true -%}
            {%- assign shown_options = true -%}
            {{ line_item.unit_price | unit_price_with_measurement: line_item.unit_price_measurement }}
            {{ 'product.unit_price_label' | t }}&nbsp;{{ line_item.unit_price | unit_price_with_measurement: line_item.unit_price_measurement }}
        {% endif %}
        {%- unless line_item.product.has_only_default_variant -%}
            {%- assign shown_options = true -%}
            {%- if slash -%}&nbsp;/&nbsp;{%- else -%}{%- assign slash = true -%}{%- endif -%}
            {%- for option in line_item.options_with_values -%}
                {{ option.value }}
                {% unless forloop.last %}&nbsp;/&nbsp;{% endunless %}
            {%- endfor -%}
        {% endunless -%}
        {%- if line_item.selling_plan_allocation -%}
            {%- assign shown_options = true -%}
            {%- if slash -%}&nbsp;/&nbsp;{%- else -%}{%- assign slash = true -%}{%- endif -%}
            {{ line_item.selling_plan_allocation.selling_plan.name }}
        {%- endif -%}
        {% if line_item.properties.size > 0 and slash -%}&nbsp;/&nbsp;{%- endif -%}
        {%- for property in line_item.properties -%}
            {%- assign shown_options = true -%}
            {%- if property.first.first == '_' or property.last == blank -%}{%- continue -%}{%- endif -%}
            {{ property.first }}:&nbsp;{% if property.last contains '/uploads/' %}<a href="{{ property.last }}">{{ property.last | split: '/' | last }}</a>{% else %}{{ property.last }}{% endif %}
            {% unless forloop.last %}&nbsp;/&nbsp;{% endunless %}
        {%- endfor -%}
        {% if origin == 'cartpanel' and list %}
            {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                {%- assign shown_options = true -%}
                <br><span class="overlay-gray"><i aria-hidden="true" class="icon-label"></i> <span class="text-uppercase">{{ discount_allocation.discount_application.title }}&nbsp;(-{{ discount_allocation.amount | money }})</span></span>
            {%- endfor -%}
        {% endif %}
        {%- if origin == 'cartpage' or bundle -%}</span>{%- else -%}</p>{%- endif -%}
    {%- endif -%}
    {%- if show_deliverytime %}
        {%- liquid
            if shown_options
                assign deliverytime_extra_element = '<br>'
            else
                assign deliverytime_extra_element = ''
            endif
        -%}
        {%- render 'product-deliverytime',
                product: line_item.product,
                current_variant: line_item.variant,
                container: "span",
                extra_class: "size-12",
                extra_element: deliverytime_extra_element
        -%}
    {%- endif -%}
    {% if origin == 'cartpage' or list == false %}
        {%- for discount_allocation in line_item.line_level_discount_allocations -%}
            {% if origin == 'cartpage' %}<br>{% endif %}<span class="overlay-gray{% if origin == 'cartpanel' %} size-12{% endif %}"><i aria-hidden="true" class="icon-label"></i> <span class="text-uppercase">{{ discount_allocation.discount_application.title }}&nbsp;(-{{ discount_allocation.amount | money }})</span></span>
        {%- endfor -%}
    {% endif %}
    {%- if origin == 'cartpage' or bundle -%}</span>{%- endif -%}
{%- endcapture -%}
{{ line_item_options }}